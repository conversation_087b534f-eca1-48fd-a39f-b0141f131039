<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>الملفات الشخصية والإعدادات - StreamTok</title>
  <!-- تحميل سكريبت الوضع المظلم قبل أي شيء آخر لمنع الوميض -->
  <script src="/preload-theme.js"></script>
  <link rel="stylesheet" href="/dark-mode.css">
  <link rel="stylesheet" href="/css/loading-indicator.css">
  <link rel="stylesheet" href="/css/sidebar-enhanced.css">
  <style>
    :root {
      color-scheme: light dark;
      --primary-color: #ff3b5c;
      --secondary-color: #25f4ee;
      --dark-color: #333;
      --light-color: #f4f4f4;
      --danger-color: #dc3545;
      --success-color: #28a745;
    }

    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    body {
      font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--bg-gradient);
      color: var(--text-color);
      transition: background 0.3s ease, color 0.3s ease;
      min-height: 100vh;
      display: flex;
    }

    .container {
      max-width: 850px;
      margin: 0 auto;
      background-color: var(--section-bg);
      border-radius: 15px;
      padding: 30px;
      box-shadow: 0 10px 30px var(--shadow-color);
      transition: background-color 0.3s ease, box-shadow 0.3s ease;
      margin-top: 30px;
      opacity: 0.95 !important;
    }

    .sidebar {
      width: 250px;
      background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
      box-shadow: 3px 0 15px rgba(0, 0, 0, 0.08);
      padding: 0;
      position: fixed;
      height: 100%;
      right: 0;
      top: 0;
      overflow-y: auto;
      z-index: 1000;
      border-left: 1px solid rgba(0, 0, 0, 0.05);
      border-top-left-radius: 15px;
      border-bottom-left-radius: 15px;
      opacity: 0.95 !important;
    }

    .logo {
      text-align: center;
      padding: 25px 15px;
      margin-bottom: 10px;
      background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
      box-shadow: 0 4px 10px rgba(255, 59, 92, 0.2);
      position: relative;
      border-top-left-radius: 15px;
    }

    .logo h3 {
      color: white;
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .logo::after {
      content: '';
      position: absolute;
      bottom: -10px;
      right: 50%;
      transform: translateX(50%);
      width: 20px;
      height: 20px;
      background: #ff3b5c;
      transform: rotate(45deg) translateX(50%);
      box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
      z-index: -1;
    }

    .logo img {
      max-width: 80%;
      height: auto;
    }

    .nav-menu {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 20px 15px;
    }

    .nav-menu a {
      display: flex;
      align-items: center;
      padding: 14px 18px;
      color: #444;
      text-decoration: none;
      border-radius: 12px;
      transition: all 0.3s ease;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
      border: 1px solid rgba(0, 0, 0, 0.03);
      font-size: 14px;
      font-weight: normal;
    }

    .nav-menu a::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 3px;
      height: 100%;
      background: #ff3b5c;
      transform: scaleY(0);
      transition: transform 0.3s ease;
    }

    .nav-menu a:hover {
      background-color: #f0f4f8;
      color: #ff3b5c;
      transform: translateY(-2px);
      box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
    }

    .nav-menu a:hover::before {
      transform: scaleY(1);
    }

    .nav-menu a.active {
      background: linear-gradient(45deg, #ff3b5c 0%, #ff6b87 100%);
      color: white;
      box-shadow: 0 5px 15px rgba(255, 59, 92, 0.25);
      border: none;
      font-weight: normal;
      font-size: inherit;
    }

    .nav-menu a.active::before {
      transform: scaleY(0);
    }

    .nav-icon {
      margin-left: 15px;
      font-size: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .nav-menu a:hover .nav-icon {
      background-color: rgba(255, 59, 92, 0.1);
      transform: rotate(5deg);
    }

    .nav-menu a.active .nav-icon {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .main-content {
      flex: 1;
      margin-right: 250px;
      padding: 20px;
    }

    .section {
      background-color: var(--section-bg);
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 4px var(--shadow-color);
      transition: background-color 0.3s ease, box-shadow 0.3s ease;
    }

    h1 {
      color: var(--primary-color);
      text-align: center;
      font-size: 2.2rem;
      margin-bottom: 30px;
      font-weight: 700;
      border-bottom: 2px solid var(--border-color);
      padding-bottom: 15px;
    }

    h3 {
      color: var(--text-color);
      margin-bottom: 15px;
      font-size: 1.3rem;
      transition: color 0.3s ease;
    }

    .profile-card {
      background-color: var(--section-bg);
      border: 1px solid var(--border-color);
      border-radius: 10px;
      padding: 15px;
      margin-bottom: 15px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;
      position: relative;
      transition: transform 0.2s, box-shadow 0.2s, background-color 0.3s ease, border-color 0.3s ease;
    }

    .profile-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    .profile-card.active {
      border-color: var(--primary-color);
      background-color: var(--hover-bg);
    }

    .profile-info {
      flex: 1;
    }

    .profile-title {
      font-size: 1.1rem;
      margin-bottom: 5px;
      color: var(--text-color);
      transition: color 0.3s ease;
    }

    .profile-meta {
      font-size: 0.8rem;
      color: var(--text-secondary);
    }

    .profile-actions {
      display: flex;
      gap: 5px;
    }

    .profile-badge {
      position: absolute;
      top: -8px;
      left: -8px;
      background-color: var(--primary-color);
      color: white;
      padding: 2px 8px;
      border-radius: 10px;
      font-size: 0.7rem;
    }

    .profile-badge.limited {
      background-color: #ffc107;
      color: #212529;
    }

    .profile-card.limited {
      border-color: #ffc107;
      background-color: rgba(255, 193, 7, 0.05);
    }

    .profile-card.limited:hover {
      background-color: rgba(255, 193, 7, 0.1);
    }

    button {
      padding: 8px 15px;
      border: none;
      border-radius: 5px;
      cursor: pointer;
      transition: background-color 0.3s;
      font-size: 0.9rem;
    }

    .btn-primary {
      background-color: var(--primary-color);
      color: #fff;
    }

    .btn-primary:hover {
      background-color: #e0214a;
    }

    .btn-secondary {
      background-color: #eee;
      color: var(--dark-color);
    }

    .btn-secondary:hover {
      background-color: #ddd;
    }

    .btn-danger {
      background-color: var(--danger-color);
      color: #fff;
    }

    .btn-danger:hover {
      background-color: #bd2130;
    }

    .btn-sm {
      padding: 5px 10px;
      font-size: 0.8rem;
    }

    .btn-secondary.disabled {
      background-color: #6c757d;
      color: #fff;
      opacity: 0.6;
      cursor: not-allowed;
    }

    .btn-secondary.disabled:hover {
      background-color: #6c757d;
      opacity: 0.6;
    }

    .import-export-container {
      display: flex;
      gap: 15px;
      margin-top: 15px;
    }

    .import-export-card {
      flex: 1;
      background-color: var(--table-header-bg);
      border-radius: 8px;
      padding: 15px;
      border: 1px solid var(--border-color);
      transition: background-color 0.3s ease, border-color 0.3s ease;
    }

    .import-export-card h4 {
      margin-bottom: 10px;
      color: var(--text-color);
      transition: color 0.3s ease;
    }

    .import-export-card p {
      font-size: 0.9rem;
      margin-bottom: 15px;
      color: var(--text-secondary);
    }

    .alert {
      padding: 10px 15px;
      border-radius: 5px;
      margin-bottom: 20px;
      display: none;
    }

    .alert.success {
      background-color: var(--success-color);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .alert.error {
      background-color: var(--danger-color);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .alert.info {
      background-color: var(--info-color);
      color: white;
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-group label {
      display: block;
      margin-bottom: 5px;
      font-weight: 500;
      color: var(--text-color);
      transition: color 0.3s ease;
    }

    .form-control {
      width: 100%;
      padding: 8px 10px;
      border: 1px solid var(--input-border);
      border-radius: 5px;
      font-size: 1rem;
      background-color: var(--input-bg);
      color: var(--text-color);
      transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease;
    }

    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1050;
      overflow: auto;
    }

    .modal-dialog {
      margin: 50px auto;
      max-width: 500px;
      background-color: var(--section-bg);
      border-radius: 10px;
      box-shadow: 0 5px 15px var(--shadow-color);
      transition: background-color 0.3s ease, box-shadow 0.3s ease;
    }

    .modal-header {
      padding: 15px 20px;
      border-bottom: 1px solid var(--border-color);
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: var(--text-color);
      transition: border-color 0.3s ease, color 0.3s ease;
    }

    .modal-body {
      padding: 20px;
      color: var(--text-color);
      transition: color 0.3s ease;
    }

    .modal-footer {
      padding: 15px 20px;
      border-top: 1px solid var(--border-color);
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      transition: border-color 0.3s ease;
    }

    .close {
      font-size: 1.5rem;
      font-weight: 700;
      line-height: 1;
      color: var(--text-color);
      opacity: 0.5;
      background: none;
      border: none;
      padding: 0;
      cursor: pointer;
      transition: color 0.3s ease;
    }

    .close:hover {
      opacity: 0.75;
    }

    .hidden-file-input {
      display: none;
    }

    .footer {
      text-align: center;
      margin-top: 30px;
      padding: 20px;
      color: var(--text-secondary);
      font-size: 0.9rem;
      transition: color 0.3s ease;
    }
  </style>
</head>
<body>
  <div class="sidebar">
    <div class="logo">
      <h3>StreamTok</h3>
    </div>
    <div class="nav-menu">
      <a href="/">
        <span class="nav-icon">🔌</span>
        الاتصال بـ TikTok Live
      </a>
      <a href="/mappings.html">
        <span class="nav-icon">🛠️</span>
        ربط الهدايا بالإجراءات
      </a>
      <a href="/tts-comments.html">
        <span class="nav-icon">🔊</span>
        قراءة التعليقات
      </a>
      <a href="/games.html">
        <span class="nav-icon">🎯</span>
        الألعاب
      </a>
      <a href="/settings.html">
        <span class="nav-icon">⚙️</span>
        الإعدادات
      </a>
      <a href="/profiles.html" class="active">
        <span class="nav-icon">👤</span>
        الملفات الشخصية
      </a>
      <a href="/subscriptions.html">
        <span class="nav-icon">⚡</span>
        الاشتراكات
      </a>
      <a href="/contact.html">
        <span class="nav-icon">📞</span>
        اتصل بنا
      </a>
      <a href="/overlay.html" target="_blank">
        <span class="nav-icon">🖥️</span>
        فتح Overlay
      </a>
    </div>
  </div>

  <div class="main-content">
    <div class="container">
      <h1>الملفات الشخصية والإعدادات</h1>

      <div class="alert" id="alertBox"></div>

      <div class="section">
        <h3>الملفات الشخصية</h3>
        <p>يمكنك إنشاء ملفات شخصية متعددة لحفظ إعدادات مختلفة والتبديل بينها بسهولة.</p>
        <div id="profile-limit-info" style="display: none; background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 5px; padding: 10px; margin: 10px 0; color: #856404;">
          <strong>ℹ️ النسخة المجانية:</strong> يمكنك إنشاء ملف شخصي واحد إضافي فقط بالإضافة للملف الافتراضي، ولكن يمكنك استخدام البروفايل الافتراضي فقط.
          <a href="/subscriptions.html" style="color: #ff3b5c; text-decoration: none; font-weight: bold;">اشترك الآن</a> للحصول على ملفات شخصية غير محدودة والتبديل بينها.
        </div>

        <div id="profiles-container">
          <!-- سيتم تعبئة هذا القسم ديناميكيًا -->
          <div class="loading-profiles">جاري تحميل الملفات الشخصية...</div>
        </div>

        <button id="add-profile-btn" class="btn-primary">إنشاء ملف شخصي جديد</button>
      </div>

      <div class="section">
        <h3>إدارة البروفايلات</h3>
        <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 15px;">
          <label for="profile-select" style="font-weight: 500;">البروفايل الحالي:</label>
          <select id="profile-select" class="form-control" style="width: 220px;"></select>
          <button id="save-profile-settings-btn" class="btn-primary btn-sm">حفظ الإعدادات</button>
          <button id="export-profile-btn" class="btn-secondary btn-sm">تصدير البروفايل</button>
          <button id="import-profile-btn" class="btn-secondary btn-sm">استيراد بروفايل</button>
          <input type="file" id="import-profile-file" accept=".json" class="hidden-file-input">
        </div>
      </div>

      <div class="section">
        <h3>استيراد وتصدير الإعدادات</h3>
        <p>يمكنك تصدير جميع إعداداتك لنقلها بين الأجهزة أو لعمل نسخة احتياطية منها، كما يمكنك استيراد إعدادات من ملف تم تصديره مسبقًا.</p>

        <div class="import-export-container">
          <div class="import-export-card">
            <h4>تصدير الإعدادات</h4>
            <p>تصدير جميع الإعدادات والملفات الشخصية لحفظها أو نقلها إلى جهاز آخر.</p>
            <button id="export-btn" class="btn-primary">تصدير الإعدادات</button>
          </div>

          <div class="import-export-card">
            <h4>استيراد الإعدادات</h4>
            <p>استيراد إعدادات من ملف تم تصديره مسبقًا. سيتم استبدال الإعدادات الحالية.</p>
            <button id="import-btn" class="btn-primary">استيراد الإعدادات</button>
            <input type="file" id="import-file" accept=".json" class="hidden-file-input">
          </div>
        </div>
      </div>

      <!-- مربع حوار إضافة/تعديل الملف الشخصي -->
      <div class="modal" id="profileModal">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title" id="profileModalLabel">إضافة ملف شخصي جديد</h5>
              <button type="button" class="close" data-dismiss="modal">×</button>
            </div>
            <div class="modal-body">
              <div class="form-group">
                <label for="profile-name">اسم الملف الشخصي</label>
                <input type="text" id="profile-name" class="form-control" placeholder="أدخل اسم الملف الشخصي">
                <input type="hidden" id="profile-id">
              </div>
            </div>
            <div class="modal-footer">
              <button type="button" id="save-profile-btn" class="btn-primary">حفظ</button>
              <button type="button" class="btn-secondary" data-dismiss="modal">إلغاء</button>
            </div>
          </div>
        </div>
      </div>

      <div class="footer">
        StreamTok &copy; 2025
        - By : Abdelrahman Mohamed
      </div>
    </div>
  </div>

  <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
  <script src="/socket.io/socket.io.js"></script>
  <script src="/js/settings-manager.js"></script>
  <script type="module" src="/js/firebase-config.js"></script>
  <script>
    // اتصال Socket.IO
    const socket = io({
      reconnectionAttempts: 5
    });

    // جعل socket متاح عالمي<|im_start|>
    window.socket = socket;

    // إرسال session عند الاتصال إذا كان المستخدم مسجل دخول
    socket.on('connect', () => {
      console.log('🔌 Socket connected to profiles');

      // إرسال session إذا كان هناك مستخدم مسجل دخول
      if (window.pendingUserAuth) {
        socket.emit('userAuthenticated', window.pendingUserAuth);
        console.log('✅ Pending user session sent to server:', window.pendingUserAuth.email);
        window.pendingUserAuth = null;
      } else if (window.firebaseHelpers && window.firebaseHelpers.getCurrentUser()) {
        const user = window.firebaseHelpers.getCurrentUser();
        if (user && user.emailVerified) {
          socket.emit('userAuthenticated', {
            userId: user.uid,
            email: user.email,
            emailVerified: user.emailVerified
          });
          console.log('✅ Current user session sent to server:', user.email);
        }
      }
    });

    // تهيئة نظام إدارة الإعدادات
    SettingsManager.initialize().then(() => {
      // الحصول على الملفات الشخصية من الإعدادات
      const profilesSettings = SettingsManager.get('profiles');
      const activeProfileSettings = SettingsManager.get('activeProfile');

      if (profilesSettings && profilesSettings.profiles) {
        // تحديث المتغيرات المحلية
        profiles = profilesSettings.profiles;
        if (activeProfileSettings && activeProfileSettings.id) {
          activeProfile = activeProfileSettings.id;
        }

        // تحديث واجهة المستخدم
        updateProfilesUI();
        updateProfileSelect();
      }
    });

    // متغيرات الاشتراك
    let currentUser = null;
    let userSubscription = null;
    let hasActiveSubscription = false;

    let profiles = [];
    let activeProfile = 'default';

    // تحميل بيانات اشتراك المستخدم
    async function loadUserSubscription() {
      try {
        if (window.firebaseHelpers && currentUser) {
          userSubscription = await window.firebaseHelpers.getUserSubscription(currentUser.uid);
          hasActiveSubscription = userSubscription &&
            userSubscription.status === 'active' &&
            new Date() < (userSubscription.endDate?.toDate ? userSubscription.endDate.toDate() : new Date(userSubscription.endDate));

          console.log('User subscription status:', hasActiveSubscription ? 'Active' : 'None');
          updateProfilesUI();
        }
      } catch (error) {
        console.error('Error loading user subscription:', error);
        hasActiveSubscription = false;
        updateProfilesUI();
      }
    }

    // التحقق من حد الملفات الشخصية
    function checkProfileLimit() {
      // إذا كان لديه اشتراك نشط، لا يوجد حد
      if (hasActiveSubscription) {
        return true;
      }

      // عد الملفات الشخصية الحالية (باستثناء البروفايل الافتراضي)
      const nonDefaultProfiles = profiles.filter(profile => profile.id !== 'default');
      const freeLimit = 1; // بروفايل واحد فقط للنسخة المجانية (بالإضافة للافتراضي)

      if (nonDefaultProfiles.length >= freeLimit) {
        showProfileLimitDialog();
        return false;
      }

      return true;
    }

    // عرض نافذة تجاوز حد الملفات الشخصية
    function showProfileLimitDialog() {
      const message = `
        <div style="text-align: center; padding: 20px;">
          <div style="font-size: 48px; margin-bottom: 20px;">🔒</div>
          <h3 style="color: #495057; margin-bottom: 15px;">حد الملفات الشخصية</h3>
          <p style="color: #6c757d; margin-bottom: 20px;">
            النسخة المجانية تسمح ببروفايل واحد فقط بالإضافة للبروفايل الافتراضي.<br>
            للحصول على ملفات شخصية غير محدودة، يرجى الاشتراك.
          </p>
          <p style="color: #28a745; font-weight: bold; margin-bottom: 25px;">
            الاشتراك الشامل - $10/شهر
          </p>
          <a href="/subscriptions.html" style="
            display: inline-block;
            background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 4px 15px rgba(255, 59, 92, 0.3);
          ">اشترك الآن</a>
        </div>
      `;

      showAlert(message, 'info');
    }

    // عرض نافذة منع التبديل للملفات الشخصية
    function showProfileSwitchLimitDialog() {
      const message = `
        <div style="text-align: center; padding: 20px;">
          <div style="font-size: 48px; margin-bottom: 20px;">🚫</div>
          <h3 style="color: #495057; margin-bottom: 15px;">تبديل الملفات الشخصية محدود</h3>
          <p style="color: #6c757d; margin-bottom: 20px;">
            النسخة المجانية تسمح باستخدام البروفايل الافتراضي فقط.<br>
            للتبديل بين الملفات الشخصية المختلفة، يرجى الاشتراك.
          </p>
          <p style="color: #28a745; font-weight: bold; margin-bottom: 25px;">
            الاشتراك الشامل - $10/شهر
          </p>
          <a href="/subscriptions.html" style="
            display: inline-block;
            background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 600;
            font-size: 16px;
            box-shadow: 0 4px 15px rgba(255, 59, 92, 0.3);
          ">اشترك الآن</a>
        </div>
      `;

      showAlert(message, 'info');
    }

    // مراقبة حالة المصادقة
    window.addEventListener('authStateChanged', (event) => {
      currentUser = event.detail.user;
      if (currentUser) {
        console.log('User authenticated:', currentUser.email);
        loadUserSubscription();
      } else {
        console.log('User not authenticated');
        hasActiveSubscription = false;
        updateProfilesUI();
      }
    });

    // عند تحميل الصفحة
    $(document).ready(function() {
      // طلب بيانات الملفات الشخصية
      socket.emit('getProfiles');

      // معالج زر إضافة ملف شخصي جديد
      $('#add-profile-btn').click(function() {
        // التحقق من حد الملفات الشخصية
        if (!checkProfileLimit()) {
          return;
        }

        $('#profile-name').val('');
        $('#profile-id').val('');
        $('#profileModalLabel').text('إضافة ملف شخصي جديد');
        showModal('profileModal');
      });

      // معالج زر حفظ الملف الشخصي
      $('#save-profile-btn').click(function() {
        const name = $('#profile-name').val().trim();
        const id = $('#profile-id').val();

        if (!name) {
          showAlert('يرجى إدخال اسم للملف الشخصي');
          return;
        }

        if (id) {
          // تحديث ملف شخصي موجود
          socket.emit('updateProfile', { id, name });
        } else {
          // إنشاء ملف شخصي جديد - التحقق من الحد
          if (!checkProfileLimit()) {
            hideModal('profileModal');
            return;
          }

          // إرسال معرف المستخدم مع الطلب للتحقق من الاشتراك على الخادم
          const profileData = { name };
          if (currentUser && currentUser.uid) {
            profileData.userId = currentUser.uid;
            profileData.hasActiveSubscription = hasActiveSubscription;
          }

          socket.emit('createProfile', profileData);
        }

        hideModal('profileModal');
      });

      // معالج زر تصدير الإعدادات
      $('#export-btn').click(function() {
        socket.emit('exportSettings');
      });

      // معالج زر استيراد الإعدادات
      $('#import-btn').click(function() {
        $('#import-file').click();
      });

      // معالج تغيير ملف الاستيراد
      $('#import-file').change(function(e) {
        const file = e.target.files[0];
        if (!file) return;

        if (file.type !== 'application/json') {
          showAlert('يرجى اختيار ملف بتنسيق JSON');
          return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
          try {
            const importData = JSON.parse(e.target.result);

            if (confirm('سيتم استبدال جميع الإعدادات الحالية بالإعدادات المستوردة. هل أنت متأكد من المتابعة؟')) {
              socket.emit('importSettings', importData);
            }
          } catch (error) {
            showAlert('حدث خطأ أثناء قراءة ملف الإعدادات: ' + error.message);
          }
        };

        reader.readAsText(file);
      });

      // معالج زر حفظ إعدادات البروفايل
      $('#save-profile-settings-btn').click(function() {
        const selectedProfileId = $('#profile-select').val();
        if (selectedProfileId) {
          socket.emit('saveProfileSettings', { id: selectedProfileId });
        }
      });

      // معالج زر تصدير البروفايل
      $('#export-profile-btn').click(function() {
        const selectedProfileId = $('#profile-select').val();
        if (selectedProfileId) {
          socket.emit('exportProfile', { id: selectedProfileId });
        }
      });

      // معالج زر استيراد بروفايل
      $('#import-profile-btn').click(function() {
        $('#import-profile-file').click();
      });

      // معالج تغيير ملف استيراد البروفايل
      $('#import-profile-file').change(function(e) {
        const file = e.target.files[0];
        if (!file) return;

        if (file.type !== 'application/json') {
          showAlert('يرجى اختيار ملف بتنسيق JSON');
          return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
          try {
            const importData = JSON.parse(e.target.result);

            if (confirm('سيتم استبدال إعدادات البروفايل الحالي بالإعدادات المستوردة. هل أنت متأكد من المتابعة؟')) {
              const selectedProfileId = $('#profile-select').val();
              if (selectedProfileId) {
                socket.emit('importProfile', { id: selectedProfileId, data: importData });
              }
            }
          } catch (error) {
            showAlert('حدث خطأ أثناء قراءة ملف البروفايل: ' + error.message);
          }
        };

        reader.readAsText(file);
      });

      // معالج نقرات إغلاق النوافذ المنبثقة
      $('.close, [data-dismiss="modal"]').click(function() {
        const modal = $(this).closest('.modal');
        hideModal(modal.attr('id'));
      });
    });

    // معالج استجابة تصدير البيانات
    socket.on('exportData', function(data) {
      const json = JSON.stringify(data, null, 2);
      const blob = new Blob([json], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `tiktok_overlay_settings_${new Date().toISOString().slice(0, 10)}.json`;
      a.click();

      URL.revokeObjectURL(url);

      showAlert('تم تصدير الإعدادات بنجاح', 'success');
    });

    // معالج استجابة تصدير البروفايل
    socket.on('exportProfileData', function(data) {
      const json = JSON.stringify(data, null, 2);
      const blob = new Blob([json], { type: 'application/json' });
      const url = URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = `profile_${data.id}_${new Date().toISOString().slice(0, 10)}.json`;
      a.click();

      URL.revokeObjectURL(url);

      showAlert('تم تصدير البروفايل بنجاح', 'success');
    });

    // معالج استجابة استيراد البيانات
    socket.on('importComplete', function(data) {
      if (data.success) {
        showAlert('تم استيراد الإعدادات بنجاح', 'success');

        // إعادة تهيئة نظام الإعدادات لتحميل الإعدادات الجديدة
        try {
          // إعادة تحميل الإعدادات من التخزين المحلي
          SettingsManager.loadFromLocalStorage();
          // تحديث البيانات المعروضة
          socket.emit('getProfiles');
        } catch (error) {
          console.error('خطأ في إعادة تهيئة الإعدادات:', error);
        }
      }
    });

    // معالج استجابة استيراد البروفايل
    socket.on('importProfileComplete', function(data) {
      if (data.success) {
        showAlert('تم استيراد البروفايل بنجاح', 'success');
        // تحديث البيانات المعروضة
        socket.emit('getProfiles');
      }
    });

    // معالج استجابة إنشاء ملف شخصي
    socket.on('profileCreated', function(data) {
      showAlert('تم إنشاء الملف الشخصي بنجاح', 'success');
    });

    // معالج استجابة تحديث ملف شخصي
    socket.on('profileUpdated', function(data) {
      showAlert('تم تحديث الملف الشخصي بنجاح', 'success');
    });

    // معالج استجابة حذف ملف شخصي
    socket.on('profileDeleted', function(data) {
      showAlert('تم حذف الملف الشخصي بنجاح', 'success');
    });

    // معالج استجابة تعيين الملف الشخصي النشط
    socket.on('activeProfileSet', function(data) {
      showAlert('تم تغيير الملف الشخصي النشط بنجاح', 'success');

      // حفظ الملف النشط في نظام الإعدادات
      if (data && data.id) {
        try {
          SettingsManager.settings.activeProfile = { id: data.id };
          SettingsManager.saveToLocalStorage();
        } catch (error) {
          console.error('خطأ في حفظ الملف النشط:', error);
        }
      }
    });

    // معالج استجابة أخطاء
    socket.on('errorMessage', function(data) {
      showAlert(data.message);
    });

    // معالج استجابة تحديث الملفات الشخصية
    socket.on('profilesUpdated', function(data) {
      profiles = data.profiles || [];
      activeProfile = data.activeProfile || 'default';

      // حفظ الإعدادات في نظام الإعدادات
      try {
        SettingsManager.settings.profiles = { profiles: profiles };
        SettingsManager.settings.activeProfile = { id: activeProfile };
        SettingsManager.saveToLocalStorage();
      } catch (error) {
        console.error('خطأ في حفظ الملفات الشخصية:', error);
      }

      // تحديث واجهة المستخدم
      updateProfilesUI();
      updateProfileSelect();
    });

    // تحديث واجهة الملفات الشخصية
    function updateProfilesUI() {
      const container = $('#profiles-container');
      container.empty();

      // للمستخدمين غير المشتركين، فرض استخدام البروفايل الافتراضي
      // فقط إذا تم تحميل بيانات الاشتراك بالفعل
      if (currentUser && !hasActiveSubscription && activeProfile !== 'default') {
        const nonDefaultProfiles = profiles.filter(profile => profile.id !== 'default');
        if (nonDefaultProfiles.length > 0) {
          // إجبار التبديل للبروفايل الافتراضي
          socket.emit('setActiveProfile', { id: 'default' });
          activeProfile = 'default';
        }
      }

      // تحديث زر إضافة ملف شخصي حسب حالة الاشتراك
      const addButton = $('#add-profile-btn');
      const limitInfo = $('#profile-limit-info');

      if (!hasActiveSubscription) {
        const nonDefaultProfiles = profiles.filter(profile => profile.id !== 'default');

        // إظهار رسالة التحديد للمستخدمين غير المشتركين
        limitInfo.show();

        if (nonDefaultProfiles.length >= 1) {
          addButton.prop('disabled', true);
          addButton.text('اشتراك مطلوب لإضافة ملفات شخصية إضافية');
          addButton.css({
            'background': '#6c757d',
            'cursor': 'not-allowed',
            'opacity': '0.6'
          });
        } else {
          addButton.prop('disabled', false);
          addButton.text('إنشاء ملف شخصي جديد');
          addButton.css({
            'background': '',
            'cursor': '',
            'opacity': ''
          });
        }
      } else {
        // إخفاء رسالة التحديد للمشتركين
        limitInfo.hide();

        addButton.prop('disabled', false);
        addButton.text('إنشاء ملف شخصي جديد');
        addButton.css({
          'background': '',
          'cursor': '',
          'opacity': ''
        });
      }

      if (!profiles.length) {
        container.html('<div class="no-profiles">لا توجد ملفات شخصية حتى الآن</div>');
        return;
      }

      // ترتيب الملفات الشخصية بحيث يكون الملف النشط في المقدمة
      const sortedProfiles = [...profiles].sort((a, b) => {
        if (a.id === activeProfile) return -1;
        if (b.id === activeProfile) return 1;
        return b.lastModified - a.lastModified;
      });

      // إنشاء بطاقة لكل ملف شخصي
      sortedProfiles.forEach(profile => {
        const isActive = profile.id === activeProfile;
        const isDefault = profile.id === 'default';
        const isLimitedProfile = !hasActiveSubscription && !isDefault;

        const created = new Date(profile.created).toLocaleString();
        const lastModified = new Date(profile.lastModified).toLocaleString();

        const limitedBadge = isLimitedProfile ? '<div class="profile-badge limited">محدود</div>' : '';
        const limitedClass = isLimitedProfile ? 'limited' : '';

        // تحديد ما إذا كان يمكن تنشيط هذا البروفايل
        // إذا لم يتم تحميل بيانات الاشتراك بعد، اسمح بالتنشيط مؤقت<|im_start|>
        const canActivate = !currentUser || hasActiveSubscription || isDefault;
        const activateButtonClass = canActivate ? 'btn-primary' : 'btn-secondary disabled';
        const activateButtonText = canActivate ? 'تنشيط' : 'اشتراك مطلوب';

        const card = $(`
          <div class="profile-card ${isActive ? 'active' : ''} ${limitedClass}">
            ${isActive ? '<div class="profile-badge">نشط</div>' : ''}
            ${limitedBadge}
            <div class="profile-info">
              <div class="profile-title">${profile.name}</div>
              <div class="profile-meta">
                آخر تعديل: ${lastModified}
                ${isLimitedProfile ? '<br><small style="color: #dc3545;">⚠️ يتطلب اشتراك للاستخدام الكامل</small>' : ''}
              </div>
            </div>
            <div class="profile-actions">
              ${!isActive ? `<button class="btn-sm ${activateButtonClass} activate-profile-btn" data-id="${profile.id}" ${!canActivate ? 'disabled' : ''}>${activateButtonText}</button>` : ''}
              <button class="btn-sm btn-secondary edit-profile-btn" data-id="${profile.id}" data-name="${profile.name}">تعديل</button>
              ${!isDefault ? `<button class="btn-sm btn-danger delete-profile-btn" data-id="${profile.id}">حذف</button>` : ''}
            </div>
          </div>
        `);

        container.append(card);
      });

      // إضافة معالجات الأحداث للأزرار
      $('.activate-profile-btn').click(function() {
        const id = $(this).data('id');

        // التحقق من إمكانية التبديل للمستخدمين غير المشتركين
        // فقط إذا تم تحميل بيانات الاشتراك بالفعل
        if (currentUser && !hasActiveSubscription && id !== 'default') {
          showProfileSwitchLimitDialog();
          return;
        }

        // إرسال معرف المستخدم مع الطلب للتحقق من الاشتراك على الخادم
        const profileData = { id };
        if (currentUser && currentUser.uid) {
          profileData.userId = currentUser.uid;
          profileData.hasActiveSubscription = hasActiveSubscription;
        }

        socket.emit('setActiveProfile', profileData);
      });

      $('.edit-profile-btn').click(function() {
        const id = $(this).data('id');
        const name = $(this).data('name');

        $('#profile-id').val(id);
        $('#profile-name').val(name);
        $('#profileModalLabel').text('تعديل الملف الشخصي');

        showModal('profileModal');
      });

      $('.delete-profile-btn').click(function() {
        const id = $(this).data('id');

        if (confirm('هل أنت متأكد من حذف هذا الملف الشخصي؟ لا يمكن التراجع عن هذا الإجراء.')) {
          socket.emit('deleteProfile', { id });
        }
      });
    }

    // تحديث قائمة اختيار البروفايل
    function updateProfileSelect() {
      const select = $('#profile-select');
      select.empty();

      profiles.forEach(profile => {
        const option = $(`<option value="${profile.id}">${profile.name}</option>`);
        if (profile.id === activeProfile) {
          option.attr('selected', 'selected');
        }
        select.append(option);
      });
    }

    // عرض نافذة منبثقة
    function showModal(modalId) {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.style.display = 'block';
      }
    }

    // إخفاء نافذة منبثقة
    function hideModal(modalId) {
      const modal = document.getElementById(modalId);
      if (modal) {
        modal.style.display = 'none';
      }
    }

    // عرض رسالة تنبيه
    function showAlert(message, type = 'error') {
      const alertBox = $('#alertBox');
      alertBox.removeClass().addClass('alert');

      if (type === 'success') {
        alertBox.addClass('success');
      } else if (type === 'info') {
        alertBox.addClass('info');
      } else {
        alertBox.addClass('error');
      }

      // استخدام html() بدلاً من text() لدعم HTML
      alertBox.html(message).slideDown();

      // إخفاء الرسالة بعد 5 ثوانٍ للرسائل المعقدة
      const hideDelay = type === 'info' ? 5000 : 3000;
      setTimeout(() => {
        alertBox.slideUp();
      }, hideDelay);
    }
  </script>
  <script src="/js/global-text-direction.js"></script>
  <script src="/dark-mode.js"></script>
  <script src="/js/translation.js"></script>
  <script src="/js/smooth-nav.js"></script>
  <script src="/js/background-manager.js"></script>
</body>
</html>