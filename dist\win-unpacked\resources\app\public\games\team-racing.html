<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🏁 سباق الفرق المحترف - Professional Team Racing</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700;800;900&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%),
                linear-gradient(135deg, #0a0a1a 0%, #1a1a3a 25%, #2a1a4a 50%, #1a2a5a 75%, #0a1a4a 100%);
            overflow: hidden;
            user-select: none;
            touch-action: none;
            color: white;
            position: relative;
            min-height: 100vh;
        }

        /* Enhanced animated background */
        .bg-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 1;
            overflow: hidden;
        }

        .particle {
            position: absolute;
            border-radius: 50%;
            animation: float 8s ease-in-out infinite;
            filter: blur(1px);
        }

        .particle:nth-child(odd) {
            background: radial-gradient(circle, rgba(120, 219, 255, 0.4) 0%, rgba(120, 219, 255, 0.1) 70%, transparent 100%);
        }

        .particle:nth-child(even) {
            background: radial-gradient(circle, rgba(255, 119, 198, 0.4) 0%, rgba(255, 119, 198, 0.1) 70%, transparent 100%);
        }

        @keyframes float {
            0%, 100% {
                transform: translateY(0px) translateX(0px) rotate(0deg) scale(1);
                opacity: 0.3;
            }
            25% {
                transform: translateY(-30px) translateX(20px) rotate(90deg) scale(1.2);
                opacity: 0.6;
            }
            50% {
                transform: translateY(-60px) translateX(-10px) rotate(180deg) scale(0.8);
                opacity: 0.8;
            }
            75% {
                transform: translateY(-30px) translateX(-30px) rotate(270deg) scale(1.1);
                opacity: 0.4;
            }
        }

        #gameCanvas {
            display: block !important;
            cursor: crosshair;
            width: 100% !important;
            height: 100% !important;
            border-radius: 23px;
            position: relative;
            z-index: 1;
            background: transparent;
        }

        /* Three.js specific styles */
        canvas {
            display: block;
            outline: none;
            border-radius: 23px;
            background: transparent;
        }

        .webgl-info {
            position: absolute;
            bottom: 15px;
            left: 15px;
            color: #00ffff;
            font-size: 11px;
            background: rgba(0, 0, 0, 0.8);
            padding: 8px 12px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            border: 1px solid rgba(0, 255, 255, 0.3);
            backdrop-filter: blur(10px);
        }

        .game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            box-sizing: border-box;
        }

        #gameContainer {
            position: relative;
            width: 95%;
            height: 90%;
            border-radius: 25px;
            background:
                linear-gradient(145deg, rgba(0, 0, 0, 0.1), rgba(255, 255, 255, 0.05)),
                radial-gradient(circle at center, rgba(120, 219, 255, 0.1) 0%, transparent 70%);
            border: 2px solid transparent;
            background-clip: padding-box;
            box-shadow:
                0 25px 50px rgba(0, 0, 0, 0.3),
                0 0 100px rgba(120, 219, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1),
                inset 0 -1px 0 rgba(0, 0, 0, 0.2);
            backdrop-filter: blur(20px);
            overflow: hidden;
        }

        .ui-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }

        /* Settings Panel */
        .settings-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: linear-gradient(145deg, rgba(0, 0, 0, 0.95), rgba(20, 20, 40, 0.95));
            color: white;
            padding: 25px;
            border-radius: 20px;
            border: 2px solid transparent;
            background-clip: padding-box;
            box-shadow:
                0 0 40px rgba(155, 89, 182, 0.6),
                inset 0 0 20px rgba(155, 89, 182, 0.1);
            backdrop-filter: blur(15px);
            pointer-events: auto;
            max-width: 450px;
            max-height: 85vh;
            overflow-y: auto;
            z-index: 100;
            display: none;
            transform: translateX(100%);
            transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
        }

        .settings-panel.show {
            display: block;
            transform: translateX(0);
        }

        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid transparent;
            background: linear-gradient(90deg, #9b59b6, #3498db) padding-box,
                        linear-gradient(90deg, #9b59b6, #3498db) border-box;
            border-image: linear-gradient(90deg, #9b59b6, #3498db) 1;
        }

        .settings-header h2 {
            background: linear-gradient(45deg, #9b59b6, #3498db);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-size: 22px;
            font-weight: 800;
            text-shadow: 0 0 20px rgba(155, 89, 182, 0.5);
        }

        .close-settings {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 10px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 16px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
        }

        .close-settings:hover {
            background: linear-gradient(45deg, #c0392b, #a93226);
            transform: scale(1.1) rotate(90deg);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.5);
        }

        /* Tabs */
        .tabs {
            display: flex;
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            overflow: hidden;
        }

        .tab {
            flex: 1;
            padding: 10px 8px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            border: none;
            background: transparent;
            color: white;
        }

        .tab.active {
            background: #9b59b6;
        }

        .tab:hover {
            background: rgba(155, 89, 182, 0.5);
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Settings Controls */
        .setting-group {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .setting-group h3 {
            color: #3498db;
            margin-bottom: 15px;
            font-size: 16px;
            border-bottom: 1px solid rgba(52, 152, 219, 0.3);
            padding-bottom: 5px;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .setting-label {
            flex: 1;
            margin-right: 10px;
        }

        .setting-control {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        input, select, button {
            background: rgba(0, 0, 0, 0.7);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            padding: 5px 10px;
            color: white;
            font-size: 12px;
            font-family: 'Tajawal', sans-serif;
        }

        input[type="checkbox"] {
            transform: scale(1.2);
        }

        input[type="range"] {
            width: 80px;
        }

        input[type="number"] {
            width: 60px;
        }

        select {
            min-width: 100px;
        }

        button {
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 8px 12px;
        }

        button:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
        }

        .btn-primary {
            background: linear-gradient(45deg, #3498db, #5dade2);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn-primary:hover {
            background: linear-gradient(45deg, #2980b9, #3498db);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(52, 152, 219, 0.5);
        }

        .btn-success {
            background: linear-gradient(45deg, #27ae60, #2ecc71);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 12px;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(39, 174, 96, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }
        .btn-success:hover {
            background: linear-gradient(45deg, #229954, #27ae60);
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(39, 174, 96, 0.5);
        }

        .btn-danger {
            background: linear-gradient(45deg, #e74c3c, #ec7063);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 13px;
            font-weight: bold;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        .btn-danger:hover {
            background: linear-gradient(45deg, #c0392b, #e74c3c);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(231, 76, 60, 0.5);
        }

        .btn-warning {
            background: linear-gradient(45deg, #f39c12, #f1c40f);
            border: none;
            color: white;
            padding: 10px 20px;
            border-radius: 10px;
            cursor: pointer;
            font-size: 13px;
            font-weight: bold;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            box-shadow: 0 4px 15px rgba(243, 156, 18, 0.3);
            display: inline-flex;
            align-items: center;
            gap: 6px;
        }
        .btn-warning:hover {
            background: linear-gradient(45deg, #e67e22, #f39c12);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(243, 156, 18, 0.5);
        }

        /* Game Stats Panel */
        .stats-panel {
            position: absolute;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background:
                linear-gradient(145deg, rgba(0, 0, 0, 0.9), rgba(20, 20, 40, 0.9));
            color: white;
            padding: 15px 20px;
            border-radius: 20px;
            border: 2px solid transparent;
            background-clip: padding-box;
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.3),
                0 0 50px rgba(120, 219, 255, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(20px);
            pointer-events: auto;
            min-width: 450px;
            max-width: 75vw;
            z-index: 20;
        }

        .stats-panel h3 {
            background: linear-gradient(45deg, #78dbff, #ff77c6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            margin-bottom: 15px;
            text-align: center;
            font-size: 16px;
            font-weight: 800;
            text-shadow: 0 0 20px rgba(120, 219, 255, 0.5);
        }

        .teams-container {
            display: flex;
            gap: 12px;
            flex-wrap: wrap;
            justify-content: center;
        }

        .team-stat {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 12px 15px;
            border-radius: 15px;
            background:
                linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
            font-size: 11px;
            min-width: 90px;
            border: 2px solid transparent;
            background-clip: padding-box;
            transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            box-shadow:
                0 8px 25px rgba(0, 0, 0, 0.2),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
            overflow: hidden;
        }

        .team-stat::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s;
        }

        .team-stat:hover::before {
            left: 100%;
        }

        .team-stat:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow:
                0 15px 35px rgba(0, 0, 0, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
        }

        .team-name {
            font-weight: bold;
            margin-bottom: 4px;
            text-align: center;
            font-size: 11px;
        }

        .team-progress {
            color: #00ff7f;
            font-weight: bold;
            margin-bottom: 3px;
            font-size: 12px;
        }

        .team-supporters {
            font-size: 9px;
            color: #bbb;
            text-align: center;
            margin-top: 3px;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background:
                linear-gradient(90deg, rgba(0, 0, 0, 0.3), rgba(255, 255, 255, 0.1), rgba(0, 0, 0, 0.3));
            border-radius: 10px;
            margin: 8px 0;
            overflow: hidden;
            box-shadow:
                inset 0 2px 4px rgba(0, 0, 0, 0.3),
                0 1px 0 rgba(255, 255, 255, 0.1);
            position: relative;
        }

        .progress-bar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
        }

        .progress-fill {
            height: 100%;
            border-radius: 10px;
            transition: width 0.8s cubic-bezier(0.4, 0, 0.2, 1);
            background:
                linear-gradient(90deg, currentColor 0%, rgba(255,255,255,0.9) 50%, currentColor 100%);
            box-shadow:
                0 0 10px currentColor,
                inset 0 1px 0 rgba(255, 255, 255, 0.3);
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
            animation: shimmer 2s infinite;
        }

        @keyframes shimmer {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Advanced tab styles */
        .info-text {
            color: #78dbff;
            font-weight: bold;
            font-size: 14px;
        }

        #teamImageUpload {
            display: none;
        }

        /* Enhanced button animations */
        .btn-success, .btn-primary, .btn-warning, .btn-danger {
            position: relative;
            overflow: hidden;
        }

        .btn-success::before, .btn-primary::before, .btn-warning::before, .btn-danger::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-success:hover::before, .btn-primary:hover::before,
        .btn-warning:hover::before, .btn-danger:hover::before {
            left: 100%;
        }

        /* طبقة الصور والفيديوهات */
        .images-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 5;
        }

        .team-image, .team-video {
            position: absolute;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            object-fit: cover;
            will-change: transform;
            transition: transform 0.1s ease-out;
            pointer-events: none;
        }

        .team-video {
            background: transparent;
        }

        .team-image {
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        /* Control Buttons */
        .control-buttons {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 15px;
            pointer-events: auto;
        }

        .control-btn {
            background: linear-gradient(145deg, rgba(0, 0, 0, 0.9), rgba(20, 20, 40, 0.9));
            color: white;
            border: 2px solid transparent;
            background-clip: padding-box;
            padding: 14px 24px;
            border-radius: 15px;
            cursor: pointer;
            font-family: 'Tajawal', sans-serif;
            font-size: 14px;
            font-weight: bold;
            transition: all 0.4s cubic-bezier(0.68, -0.55, 0.265, 1.55);
            backdrop-filter: blur(15px);
            box-shadow:
                0 8px 32px rgba(52, 152, 219, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            display: inline-flex;
            align-items: center;
            gap: 8px;
            position: relative;
            overflow: hidden;
        }

        .control-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(52, 152, 219, 0.2), transparent);
            transition: left 0.5s;
        }

        .control-btn:hover::before {
            left: 100%;
        }

        .control-btn:hover {
            background: linear-gradient(145deg, rgba(52, 152, 219, 0.3), rgba(41, 128, 185, 0.3));
            transform: translateY(-3px) scale(1.05);
            box-shadow:
                0 12px 40px rgba(52, 152, 219, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            border-color: rgba(52, 152, 219, 0.5);
        }

        .settings-btn {
            background: linear-gradient(145deg, rgba(155, 89, 182, 0.9), rgba(142, 68, 173, 0.9));
        }

        .settings-btn::before {
            background: linear-gradient(90deg, transparent, rgba(155, 89, 182, 0.2), transparent);
        }

        .settings-btn:hover {
            background: linear-gradient(145deg, rgba(155, 89, 182, 1), rgba(142, 68, 173, 1));
            box-shadow: 0 12px 40px rgba(155, 89, 182, 0.4);
            border-color: rgba(155, 89, 182, 0.5);
        }

        .demo-btn {
            background: linear-gradient(145deg, rgba(231, 76, 60, 0.9), rgba(192, 57, 43, 0.9));
        }

        .demo-btn::before {
            background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.2), transparent);
        }

        .demo-btn:hover {
            background: linear-gradient(145deg, rgba(231, 76, 60, 1), rgba(192, 57, 43, 1));
            box-shadow: 0 12px 40px rgba(231, 76, 60, 0.4);
            border-color: rgba(231, 76, 60, 0.5);
        }

        /* Animations */
        @keyframes slideDown {
            from { transform: translateX(-50%) translateY(-100%); opacity: 0; }
            to { transform: translateX(-50%) translateY(0); opacity: 1; }
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: scale(0.8); }
            to { opacity: 1; transform: scale(1); }
        }
        
        @keyframes bounce {
            0%, 20%, 60%, 100% { transform: translateY(0); }
            40% { transform: translateY(-30px); }
            80% { transform: translateY(-15px); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .settings-panel {
                max-width: 90vw;
                right: 5vw;
            }
            
            .stats-panel {
                min-width: 200px;
            }
            
            .control-buttons {
                flex-direction: column;
                align-items: center;
            }
        }
    </style>
</head>
<body>
    <div class="game-container">
        <!-- Animated Background Particles -->
        <div class="bg-particles" id="bgParticles"></div>

        <div id="gameContainer" style="width: 100%; height: 100vh; position: relative;">
            <!-- Canvas and overlay will be added by JavaScript -->
        </div>

        <div class="ui-overlay">
            <!-- Game Stats Panel -->
            <div class="stats-panel">
                <h3>🏁 إحصائيات السباق</h3>
                <div class="teams-container" id="teamStats">
                    <!-- سيتم إضافة إحصائيات الفرق هنا -->
                </div>
            </div>

            <!-- Settings Panel -->
            <div class="settings-panel" id="settingsPanel">
                <div class="settings-header">
                    <h2>⚙️ إعدادات اللعبة</h2>
                    <button class="close-settings" onclick="toggleSettings()">✕</button>
                </div>

                <div class="tabs">
                    <button class="tab active" onclick="showTab('teams', this)">👥 فرق</button>
                    <button class="tab" onclick="showTab('gifts', this)">🎁 هدايا</button>
                    <button class="tab" onclick="showTab('game', this)">🎮 لعبة</button>
                    <button class="tab" onclick="showTab('visual', this)">🎨 مرئي</button>
                    <button class="tab" onclick="showTab('advanced', this)">⚙️ متقدم</button>
                </div>

                <!-- Teams Tab -->
                <div id="teams" class="tab-content active">
                    <div class="setting-group">
                        <h3><i class="fas fa-users"></i> إعداد الفرق</h3>
                        <div id="teamsConfig">
                            <!-- سيتم إضافة إعدادات الفرق هنا -->
                        </div>
                        <div style="display: flex; gap: 10px; margin-top: 15px;">
                            <button class="btn-success" onclick="addNewTeam()">
                                <i class="fas fa-plus"></i> إضافة فريق
                            </button>
                            <button class="btn-danger" onclick="clearAllTeams()">
                                <i class="fas fa-trash"></i> حذف جميع الفرق
                            </button>
                            <button class="btn-primary" onclick="resetAllTeams()">
                                <i class="fas fa-undo"></i> إعادة تعيين الكل
                            </button>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3><i class="fas fa-cog"></i> إعدادات عامة للفرق</h3>
                        <div class="setting-item">
                            <span class="setting-label">نوع عرض الفرق الافتراضي</span>
                            <div class="setting-control">
                                <select id="defaultTeamType" onchange="updateDefaultTeamType(this.value)">
                                    <option value="ball">كرة ملونة</option>
                                    <option value="image">صورة مخصصة</option>
                                    <option value="gif">صورة متحركة</option>
                                    <option value="mixed">مختلط</option>
                                </select>
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">حجم الفرق</span>
                            <div class="setting-control">
                                <input type="range" id="globalTeamSize" min="0.5" max="2.0" value="1.0" step="0.1">
                                <span id="globalTeamSizeValue">1.0x</span>
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">تأثيرات الحركة</span>
                            <div class="setting-control">
                                <input type="checkbox" id="enableTeamAnimations" checked>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Gifts Tab -->
                <div id="gifts" class="tab-content">
                    <div class="setting-group">
                        <h3><i class="fas fa-gift"></i> هدايا الانضمام</h3>
                        <div class="setting-item">
                            <span class="setting-label">تفعيل هدايا الانضمام</span>
                            <div class="setting-control">
                                <input type="checkbox" id="enableJoinGifts" checked>
                            </div>
                        </div>
                        <div id="joinGiftsConfig">
                            <!-- سيتم إضافة إعدادات هدايا الانضمام هنا -->
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3><i class="fas fa-bolt"></i> هدايا التقدم</h3>
                        <div class="setting-item">
                            <span class="setting-label">تفعيل هدايا التقدم</span>
                            <div class="setting-control">
                                <input type="checkbox" id="enableProgressGifts" checked>
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">مضاعف التقدم</span>
                            <div class="setting-control">
                                <input type="range" id="progressMultiplier" min="0.5" max="3.0" value="1.0" step="0.1">
                                <span id="progressMultiplierValue">1.0x</span>
                            </div>
                        </div>
                        <div id="progressGiftsConfig">
                            <!-- سيتم إضافة إعدادات هدايا التقدم هنا -->
                        </div>
                    </div>
                </div>

                <!-- Game Tab -->
                <div id="game" class="tab-content">
                    <div class="setting-group">
                        <h3><i class="fas fa-flag-checkered"></i> إعدادات السباق</h3>
                        <div class="setting-item">
                            <span class="setting-label">مسافة السباق</span>
                            <div class="setting-control">
                                <input type="range" id="raceDistance" min="100" max="1000" value="500" step="50">
                                <span id="raceDistanceValue">500</span> نقطة
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">سرعة الحركة</span>
                            <div class="setting-control">
                                <input type="range" id="animationSpeed" min="1" max="10" value="5" step="1">
                                <span id="animationSpeedValue">5</span>x
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">إعادة تعيين تلقائي</span>
                            <div class="setting-control">
                                <input type="checkbox" id="autoReset" checked>
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">وقت إعادة التعيين (ثانية)</span>
                            <div class="setting-control">
                                <input type="range" id="autoResetDelay" min="5" max="60" value="10" step="5">
                                <span id="autoResetDelayValue">10</span>s
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3><i class="fas fa-trophy"></i> إعدادات الفوز</h3>
                        <div class="setting-item">
                            <span class="setting-label">تفعيل أصوات الفوز</span>
                            <div class="setting-control">
                                <input type="checkbox" id="enableWinSounds" checked>
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">تفعيل تأثيرات الفوز</span>
                            <div class="setting-control">
                                <input type="checkbox" id="enableWinEffects" checked>
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">مدة عرض الفوز (ثانية)</span>
                            <div class="setting-control">
                                <input type="range" id="winDisplayDuration" min="3" max="15" value="5" step="1">
                                <span id="winDisplayDurationValue">5</span>s
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Visual Tab -->
                <div id="visual" class="tab-content">
                    <div class="setting-group">
                        <h3><i class="fas fa-palette"></i> الإعدادات المرئية</h3>
                        <div class="setting-item">
                            <span class="setting-label">إظهار التأثيرات</span>
                            <div class="setting-control">
                                <input type="checkbox" id="showEffects" checked>
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">إظهار أسماء الداعمين</span>
                            <div class="setting-control">
                                <input type="checkbox" id="showSupporterNames" checked>
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">حجم الخط</span>
                            <div class="setting-control">
                                <input type="range" id="fontSize" min="10" max="30" value="16" step="2">
                                <span id="fontSizeValue">16</span>px
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">حجم الفرق</span>
                            <div class="setting-control">
                                <input type="range" id="teamSize" min="30" max="120" value="60" step="10">
                                <span id="teamSizeValue">60</span>%
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">شفافية الخلفية</span>
                            <div class="setting-control">
                                <input type="range" id="backgroundOpacity" min="0" max="100" value="80" step="5">
                                <span id="backgroundOpacityValue">80</span>%
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">نمط الكاميرا</span>
                            <div class="setting-control">
                                <select id="cameraMode">
                                    <option value="follow">متابعة المتقدم</option>
                                    <option value="fixed">ثابتة</option>
                                    <option value="overview">عرض شامل</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3><i class="fas fa-tachometer-alt"></i> إعدادات الأداء</h3>
                        <div class="setting-item">
                            <span class="setting-label">معدل الإطارات المستهدف</span>
                            <div class="setting-control">
                                <select id="targetFPS">
                                    <option value="30">30 FPS</option>
                                    <option value="60" selected>60 FPS</option>
                                    <option value="120">120 FPS</option>
                                </select>
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">تفعيل VSync</span>
                            <div class="setting-control">
                                <input type="checkbox" id="enableVSync" checked>
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">إظهار عداد FPS</span>
                            <div class="setting-control">
                                <input type="checkbox" id="showFPS" checked>
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">جودة الرسوميات</span>
                            <div class="setting-control">
                                <select id="graphicsQuality">
                                    <option value="low">منخفضة</option>
                                    <option value="medium">متوسطة</option>
                                    <option value="high" selected>عالية</option>
                                    <option value="ultra">فائقة</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Advanced Tab -->
                <div id="advanced" class="tab-content">
                    <div class="setting-group">
                        <h3><i class="fas fa-download"></i> تصدير واستيراد الإعدادات</h3>
                        <div class="setting-item">
                            <span class="setting-label">حفظ جميع الإعدادات</span>
                            <div class="setting-control">
                                <button class="btn-success" onclick="exportSettings()">
                                    <i class="fas fa-download"></i> تصدير الإعدادات
                                </button>
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">استعادة إعدادات محفوظة</span>
                            <div class="setting-control">
                                <button class="btn-primary" onclick="importSettings()">
                                    <i class="fas fa-upload"></i> استيراد الإعدادات
                                </button>
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">إعادة تعيين كامل</span>
                            <div class="setting-control">
                                <button class="btn-danger" onclick="resetAllSettings()">
                                    <i class="fas fa-trash"></i> إعادة تعيين الكل
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3><i class="fas fa-image"></i> إدارة الصور</h3>
                        <div class="setting-item">
                            <span class="setting-label">رفع صور للفرق</span>
                            <div class="setting-control">
                                <input type="file" id="teamImageUpload" accept="image/*" multiple style="display: none;">
                                <button class="btn-primary" onclick="document.getElementById('teamImageUpload').click()">
                                    <i class="fas fa-images"></i> اختيار صور
                                </button>
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">مجلد الصور المحفوظة</span>
                            <div class="setting-control">
                                <button class="btn-warning" onclick="showSavedImages()">
                                    <i class="fas fa-folder"></i> عرض الصور
                                </button>
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">مسح جميع الصور</span>
                            <div class="setting-control">
                                <button class="btn-danger" onclick="clearAllImages()">
                                    <i class="fas fa-trash-alt"></i> مسح الصور
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="setting-group">
                        <h3><i class="fas fa-info-circle"></i> معلومات النظام</h3>
                        <div class="setting-item">
                            <span class="setting-label">إصدار اللعبة</span>
                            <div class="setting-control">
                                <span class="info-text">2.0 Enhanced</span>
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">معلومات الأداء</span>
                            <div class="setting-control">
                                <button class="btn-primary" onclick="showPerformanceInfo()">
                                    <i class="fas fa-chart-line"></i> عرض الأداء
                                </button>
                            </div>
                        </div>
                        <div class="setting-item">
                            <span class="setting-label">سجل الأخطاء</span>
                            <div class="setting-control">
                                <button class="btn-warning" onclick="showErrorLog()">
                                    <i class="fas fa-bug"></i> عرض السجل
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Control Buttons -->
            <div class="control-buttons">
                <button class="control-btn settings-btn" onclick="toggleSettings()">
                    <i class="fas fa-cog"></i> إعدادات
                </button>
                <button class="control-btn demo-btn" onclick="startDemo()">
                    <i class="fas fa-play"></i> تجربة
                </button>
                <button class="control-btn" onclick="resetRace()">
                    <i class="fas fa-undo"></i> إعادة تعيين
                </button>
                <button class="control-btn" onclick="toggleFullscreen()">
                    <i class="fas fa-expand"></i> ملء الشاشة
                </button>
            </div>
        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <!-- Three.js Library for WebGL acceleration -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>

    <!-- Enhanced Background Particles Script -->
    <script>
        // Create beautiful animated background particles
        function createBackgroundParticles() {
            const container = document.getElementById('bgParticles');
            const particleCount = 80;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                // Random size and position
                const size = Math.random() * 8 + 3;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';

                // Random animation delay and duration
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 6 + 6) + 's';

                // Random opacity
                particle.style.opacity = Math.random() * 0.6 + 0.2;

                container.appendChild(particle);
            }

            // Add some special larger particles
            for (let i = 0; i < 10; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';

                const size = Math.random() * 15 + 10;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 10 + 's';
                particle.style.animationDuration = (Math.random() * 8 + 8) + 's';
                particle.style.opacity = Math.random() * 0.3 + 0.1;

                container.appendChild(particle);
            }
        }

        // Initialize particles when page loads
        document.addEventListener('DOMContentLoaded', createBackgroundParticles);
    </script>

    <script src="/games/team-racing-game-threejs.js"></script>
    <script src="/games/team-racing-ui.js"></script>
</body>
</html>
