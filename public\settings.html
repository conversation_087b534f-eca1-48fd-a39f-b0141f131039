<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>الإعدادات - TikTok Live Overlay</title>
  <!-- تحميل سكريبت الوضع المظلم قبل أي شيء آخر لمنع الوميض -->
  <script src="/preload-theme.js"></script>
  <link rel="stylesheet" href="/dark-mode.css">
  <link rel="stylesheet" href="/css/loading-indicator.css">
  <link rel="stylesheet" href="/css/sidebar-enhanced.css">
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    :root {
      color-scheme: light dark;
    }
    body {
      font-family: '<PERSON><PERSON><PERSON>', '<PERSON><PERSON><PERSON>', Tahoma, Geneva, Verdana, sans-serif;
      margin: 0;
      padding: 0;
      background: var(--bg-gradient);
      color: var(--text-color);
      transition: background 0.3s ease, color 0.3s ease;
      min-height: 100vh;
      display: flex;
    }

    .sidebar {
      width: 250px;
      background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
      box-shadow: 3px 0 15px rgba(0, 0, 0, 0.08);
      padding: 0;
      position: fixed;
      height: 100%;
      right: 0;
      top: 0;
      overflow-y: auto;
      z-index: 1000;
      border-left: 1px solid rgba(0, 0, 0, 0.05);
      border-top-left-radius: 15px;
      border-bottom-left-radius: 15px;
      opacity: 0.95 !important;
    }

    .logo {
      text-align: center;
      padding: 25px 15px;
      margin-bottom: 10px;
      background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
      box-shadow: 0 4px 10px rgba(255, 59, 92, 0.2);
      position: relative;
      border-top-left-radius: 15px;
    }

    .logo h3 {
      color: white;
      margin: 0;
      font-size: 1.5rem;
      font-weight: 700;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .logo::after {
      content: '';
      position: absolute;
      bottom: -10px;
      right: 50%;
      transform: translateX(50%);
      width: 20px;
      height: 20px;
      background: #ff3b5c;
      transform: rotate(45deg) translateX(50%);
      box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
      z-index: -1;
    }

    .logo img {
      max-width: 80%;
      height: auto;
    }

    .nav-menu {
      display: flex;
      flex-direction: column;
      gap: 8px;
      padding: 20px 15px;
    }

    .nav-menu a {
      display: flex;
      align-items: center;
      padding: 14px 18px;
      color: #444;
      text-decoration: none;
      border-radius: 12px;
      transition: all 0.3s ease;
      font-weight: 500;
      position: relative;
      overflow: hidden;
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
      border: 1px solid rgba(0, 0, 0, 0.03);
    }

    .nav-menu a::before {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 3px;
      height: 100%;
      background: #ff3b5c;
      transform: scaleY(0);
      transition: transform 0.3s ease;
    }

    .nav-menu a:hover {
      background-color: #f0f4f8;
      color: #ff3b5c;
      transform: translateY(-2px);
      box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
    }

    .nav-menu a:hover::before {
      transform: scaleY(1);
    }

    .nav-menu a.active {
      background: linear-gradient(45deg, #ff3b5c 0%, #ff6b87 100%);
      color: white;
      box-shadow: 0 5px 15px rgba(255, 59, 92, 0.25);
      border: none;
    }

    .nav-menu a.active::before {
      transform: scaleY(0);
    }

    .nav-icon {
      margin-left: 20px;
      font-size: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 30px;
      height: 30px;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 8px;
      transition: all 0.3s ease;
    }

    .nav-menu a:hover .nav-icon {
      background-color: rgba(255, 59, 92, 0.1);
      transform: rotate(5deg);
    }

    .nav-menu a.active .nav-icon {
      background-color: rgba(255, 255, 255, 0.2);
    }

    .main-content {
      flex: 1;
      margin-right: 250px;
      padding: 20px;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background-color: var(--section-bg);
      border-radius: 15px;
      padding: 30px;
      box-shadow: 0 10px 30px var(--shadow-color);
      transition: background-color 0.3s ease, box-shadow 0.3s ease;
      margin-top: 30px;
      opacity: 0.95 !important;
    }

    h1 {
      color: var(--primary-color);
      text-align: center;
      font-size: 2.2rem;
      margin-bottom: 30px;
      font-weight: 700;
      border-bottom: 2px solid var(--border-color);
      padding-bottom: 15px;
    }

    .settings-section {
      margin-bottom: 30px;
      padding-bottom: 20px;
      border-bottom: 1px solid var(--border-color);
    }

    .settings-section:last-child {
      border-bottom: none;
    }

    h2 {
      color: var(--text-color);
      font-size: 1.5rem;
      margin-bottom: 20px;
      font-weight: 600;
    }

    .form-group {
      margin-bottom: 20px;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
      color: var(--text-color);
    }

    input, select, textarea {
      width: 100%;
      padding: 12px;
      border: 1px solid var(--input-border);
      background-color: var(--input-bg);
      color: var(--text-color);
      border-radius: 8px;
      font-size: 16px;
      box-sizing: border-box;
      transition: border-color 0.3s, box-shadow 0.3s;
      font-family: 'Tajawal', sans-serif;
    }

    input:focus, select:focus, textarea:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(255, 59, 92, 0.1);
    }

    input[type="color"] {
      height: 40px;
      padding: 5px;
    }

    .range-slider {
      display: flex;
      align-items: center;
      gap: 15px;
    }

    .range-slider input[type="range"] {
      flex: 1;
    }

    .range-value {
      min-width: 50px;
      text-align: center;
      font-weight: 500;
      color: var(--text-secondary);
    }

    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 60px;
      height: 34px;
    }

    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .toggle-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 34px;
    }

    .toggle-slider:before {
      position: absolute;
      content: "";
      height: 26px;
      width: 26px;
      left: 4px;
      bottom: 4px;
      background-color: white;
      transition: .4s;
      border-radius: 50%;
    }

    input:checked + .toggle-slider {
      background-color: var(--primary-color);
    }

    input:focus + .toggle-slider {
      box-shadow: 0 0 1px var(--primary-color);
    }

    input:checked + .toggle-slider:before {
      transform: translateX(26px);
    }

    .color-preview {
      display: inline-block;
      width: 30px;
      height: 30px;
      border-radius: 5px;
      border: 1px solid var(--border-color);
      vertical-align: middle;
      margin-right: 10px;
    }

    .form-row {
      display: flex;
      gap: 15px;
      margin-bottom: 20px;
    }

    .form-col {
      flex: 1;
    }

    button {
      background: var(--primary-gradient);
      color: var(--button-text);
      border: none;
      padding: 12px 24px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 16px;
      transition: all 0.3s;
      font-weight: 500;
      box-shadow: 0 4px 6px rgba(255, 59, 92, 0.2);
      font-family: 'Tajawal', sans-serif;
    }

    button:hover {
      background-color: #e6354f;
      transform: translateY(-2px);
      box-shadow: 0 6px 8px rgba(255, 59, 92, 0.25);
    }

    button:active {
      transform: translateY(0);
      box-shadow: 0 2px 4px rgba(255, 59, 92, 0.25);
    }

    .preview-container {
      border: 2px dashed var(--border-color);
      border-radius: 10px;
      padding: 20px;
      margin-top: 20px;
      text-align: center;
      min-height: 200px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      background-color: var(--table-header-bg);
      transition: background-color 0.3s ease, border-color 0.3s ease;
    }

    .preview-container h3 {
      margin-top: 0;
      color: var(--text-secondary);
    }

    .preview-gift {
      background-color: var(--card-bg);
      border-radius: 10px;
      padding: 15px;
      display: flex;
      align-items: center;
      max-width: 400px;
      margin: 0 auto;
      box-shadow: 0 4px 15px var(--shadow-color);
      transition: background-color 0.3s ease, box-shadow 0.3s ease;
    }

    .preview-gift-icon {
      width: 50px;
      height: 50px;
      background-color: var(--primary-color);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 24px;
      margin-left: 15px;
    }

    .preview-gift-info {
      flex: 1;
    }

    .preview-gift-text {
      font-weight: 500;
      margin-bottom: 5px;
    }

    .preview-gift-sender {
      font-size: 14px;
      color: var(--text-secondary);
    }

    .actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 30px;
      gap: 15px;
    }

    .reset-btn {
      background-color: #f8f9fa;
      color: #555;
      box-shadow: none;
    }

    .reset-btn:hover {
      background-color: #e9ecef;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .tooltip {
      position: relative;
      display: inline-block;
      margin-right: 8px;
      cursor: pointer;
    }

    .tooltip-icon {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      background-color: #f0f0f0;
      color: #777;
      font-size: 12px;
      font-weight: bold;
    }

    .tooltip-text {
      visibility: hidden;
      width: 200px;
      background-color: #333;
      color: #fff;
      text-align: center;
      border-radius: 6px;
      padding: 8px;
      position: absolute;
      z-index: 1;
      bottom: 125%;
      left: 50%;
      margin-left: -100px;
      opacity: 0;
      transition: opacity 0.3s;
      font-size: 14px;
      font-weight: normal;
    }

    .tooltip:hover .tooltip-text {
      visibility: visible;
      opacity: 1;
    }

    /* Estilo para la notificación de guardado */
    .save-confirmation {
      position: fixed;
      top: 20px;
      right: 20px;
      background-color: #52c41a;
      color: white;
      padding: 15px 20px;
      border-radius: 5px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      z-index: 9999;
      animation: slideIn 0.3s ease;
    }

    .save-confirmation.fade-out {
      animation: fadeOut 0.5s ease forwards;
    }

    @keyframes slideIn {
      from { transform: translateX(100px); opacity: 0; }
      to { transform: translateX(0); opacity: 1; }
    }

    @keyframes fadeOut {
      from { opacity: 1; }
      to { opacity: 0; }
    }

    /* Animaciones para la vista previa */
    @keyframes bounce {
      0%, 20%, 50%, 80%, 100% {transform: translateY(0);}
      40% {transform: translateY(-30px);}
      60% {transform: translateY(-15px);}
    }

    @keyframes shake {
      0%, 100% {transform: translateX(0);}
      10%, 30%, 50%, 70%, 90% {transform: translateX(-10px);}
      20%, 40%, 60%, 80% {transform: translateX(10px);}
    }

    @keyframes pulse {
      0% {transform: scale(1);}
      50% {transform: scale(1.1);}
      100% {transform: scale(1);}
    }

    @keyframes flip {
      0% {transform: perspective(400px) rotateY(0);}
      100% {transform: perspective(400px) rotateY(360deg);}
    }

    @keyframes slideIn {
      0% {transform: translateY(-100px); opacity: 0;}
      100% {transform: translateY(0); opacity: 1;}
    }

    @keyframes zoomIn {
      0% {transform: scale(0); opacity: 0;}
      100% {transform: scale(1); opacity: 1;}
    }

    @keyframes rotateIn {
      0% {transform: rotate(-200deg) scale(0); opacity: 0;}
      100% {transform: rotate(0) scale(1); opacity: 1;}
    }

    @keyframes glowPulse {
      0% {box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);}
      50% {box-shadow: 0 0 20px rgba(255, 255, 255, 0.8);}
      100% {box-shadow: 0 0 5px rgba(255, 255, 255, 0.5);}
    }

    /* Clases de animación */
    .animate-bounce {animation: bounce 1s ease;}
    .animate-shake {animation: shake 0.5s ease;}
    .animate-pulse {animation: pulse 0.5s ease infinite;}
    .animate-flip {animation: flip 1s ease;}
    .animate-slide-in {animation: slideIn 0.5s ease;}
    .animate-zoom-in {animation: zoomIn 0.5s ease;}
    .animate-rotate-in {animation: rotateIn 0.5s ease;}
    .animate-glow {animation: glowPulse 2s ease infinite;}

    /* أنماط رفع الملفات */
    .upload-container {
      display: flex;
      align-items: center;
      margin-bottom: 10px;
      position: relative;
    }

    .upload-container input[type="file"] {
      position: absolute;
      width: 0.1px;
      height: 0.1px;
      opacity: 0;
      overflow: hidden;
      z-index: -1;
    }

    .upload-btn {
      background: var(--primary-gradient);
      color: var(--button-text);
      border: none;
      padding: 10px 20px;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      transition: all 0.3s;
      font-weight: 500;
      box-shadow: 0 4px 6px rgba(255, 59, 92, 0.2);
      font-family: 'Tajawal', sans-serif;
      margin-right: 0;
    }

    .file-name {
      margin-right: 15px;
      color: var(--text-secondary);
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 200px;
    }

    /* أنماط رسالة الاشتراك المطلوب */
    .subscription-required {
      text-align: center;
      padding: 40px 20px;
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border: 2px dashed #dee2e6;
      border-radius: 15px;
      margin: 20px 0;
    }

    .lock-icon {
      font-size: 48px;
      margin-bottom: 20px;
      opacity: 0.7;
    }

    .subscription-required h3 {
      color: #495057;
      margin-bottom: 15px;
      font-size: 24px;
      font-weight: 600;
    }

    .subscription-required p {
      color: #6c757d;
      margin-bottom: 25px;
      font-size: 16px;
      line-height: 1.5;
    }

    .subscribe-btn {
      display: inline-block;
      background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
      color: white;
      padding: 12px 30px;
      border-radius: 25px;
      text-decoration: none;
      font-weight: 600;
      font-size: 16px;
      transition: all 0.3s ease;
      box-shadow: 0 4px 15px rgba(255, 59, 92, 0.3);
    }

    .subscribe-btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 6px 20px rgba(255, 59, 92, 0.4);
      color: white;
      text-decoration: none;
    }

  </style>
</head>
<body>
  <div class="sidebar">
    <div class="logo">
      <h3>StreamTok</h3>
    </div>
    <div class="nav-menu">
      <a href="/">
        <span class="nav-icon">🔌</span>
        الاتصال بـ TikTok Live
      </a>
      <a href="/mappings.html">
        <span class="nav-icon">🛠️</span>
        ربط الهدايا بالإجراءات
      </a>
      <a href="/tts-comments.html">
        <span class="nav-icon">🔊</span>
        قراءة التعليقات
      </a>
      <a href="/games.html">
        <span class="nav-icon">🎯</span>
        الألعاب
      </a>
      <a href="/settings.html" class="active">
        <span class="nav-icon">⚙️</span>
        الإعدادات
      </a>
      <a href="/profiles.html">
        <span class="nav-icon">👤</span>
        الملفات الشخصية
      </a>
      <a href="/subscriptions.html">
        <span class="nav-icon">⚡</span>
        الاشتراكات
      </a>
      <a href="/contact.html">
        <span class="nav-icon">📞</span>
        اتصل بنا
      </a>
      <a href="/overlay.html" target="_blank">
        <span class="nav-icon">🖥️</span>
        فتح Overlay
      </a>
    </div>
  </div>

  <div class="main-content">
    <div class="container">
      <h1>الإعدادات</h1>

      <form id="settingsForm">
        <!-- قسم إعدادات اللغة -->
        <div class="settings-section">
          <h2>إعدادات اللغة</h2>
          <p>اختر لغة التطبيق المفضلة لديك:</p>

          <div class="form-group">
            <label for="languageSelect">اللغة:</label>
            <select id="languageSelect">
              <option value="ar">العربية</option>
              <option value="en">English</option>
            </select>
            <small>سيتم تطبيق اللغة المختارة على جميع أجزاء التطبيق</small>
          </div>

          <div class="form-group">
            <label>
              <input type="checkbox" id="reverseTextDirection" style="margin-left: 10px;">
              عكس اتجاه النص للإنجليزية
            </label>
            <small>تفعيل الاتجاه من اليسار إلى اليمين للنصوص الإنجليزية</small>
          </div>

          <div class="actions">
            <button type="button" id="applyLanguageBtn">تطبيق اللغة</button>
          </div>
        </div>

        <!-- قسم إعدادات الفيديو والصور المتحركة -->
        <div class="settings-section" id="videoAnimationSection">
          <h2>إعدادات الفيديو والصور المتحركة</h2>
          <p>تخصيص إعدادات عرض ملفات الفيديو وصور GIF كخلفية:</p>

          <!-- رسالة الاشتراك المطلوب -->
          <div id="videoAnimationLocked" class="subscription-required" style="display: none;">
            <div class="lock-icon">🔒</div>
            <h3>اشتراك مطلوب</h3>
            <p>هذه الميزة متاحة للمشتركين فقط. يرجى الاشتراك للوصول إلى إعدادات الفيديو والصور المتحركة.</p>
            <a href="/subscriptions.html" class="subscribe-btn">اشترك الآن</a>
          </div>

          <!-- محتوى الإعدادات (سيتم إخفاؤه للمستخدمين غير المشتركين) -->
          <div id="videoAnimationContent">
            <!-- قسم رفع الملفات -->
            <div class="form-group">
              <label for="mediaUpload">رفع صورة أو فيديو:</label>
              <div class="upload-container">
                <input type="file" id="mediaUpload" accept="image/gif,image/webp,video/mp4,video/webm">
                <button type="button" id="uploadMediaBtn" class="upload-btn">اختيار ملف</button>
                <span id="uploadFileName" class="file-name">لم يتم اختيار ملف</span>
              </div>
              <small>يمكنك رفع ملفات GIF أو WebP أو MP4 أو WebM (الحد الأقصى: 200 ميجابايت)</small>
            </div>



            <div class="form-group">
              <label for="defaultRenderMode">وضع العرض:</label>
              <select id="defaultRenderMode">
                <option value="auto">تلقائي (الأفضل للأداء)</option>
                <option value="video">فيديو (للملفات الكبيرة)</option>
                <option value="image">صورة (للملفات الصغيرة)</option>
                <option value="canvas">canvas (للأداء الأمثل)</option>
              </select>
              <small>وضع "فيديو" أو "canvas" يحسن أداء الملفات الكبيرة بشكل كبير</small>
            </div>

            <div class="form-group">
              <label for="defaultMediaSize">حجم الوسائط:</label>
              <select id="defaultMediaSize">
                <option value="cover">تغطية كاملة (الافتراضي)</option>
                <option value="contain">احتواء كامل</option>
                <option value="100%">حجم طبيعي (100%)</option>
                <option value="75%">75% من الحجم الطبيعي</option>
                <option value="50%">50% من الحجم الطبيعي</option>
              </select>
              <small>تغيير حجم الوسائط قد يحسن من أداء الصور المتحركة الكبيرة</small>
            </div>

            <div class="form-group">
              <label for="defaultOpacity">الشفافية:</label>
              <div class="range-slider">
                <input type="range" id="defaultOpacity" min="0" max="100" value="15">
                <span class="range-value" id="defaultOpacityValue">15%</span>
              </div>
              <small>تحديد مستوى الشفافية للوسائط</small>
            </div>

            <div class="actions">
              <button type="button" id="applyMediaBtn">تطبيق</button>
              <button type="button" id="resetMediaBtn" class="reset-btn">إعادة ضبط</button>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>

  <script src="/socket.io/socket.io.js"></script>
  <script src="/js/settings-manager.js"></script>
  <script src="/js/background-manager.js"></script>
  <script type="module" src="/js/firebase-config.js"></script>
  <script>
    // متغيرات الاشتراك
    let currentUser = null;
    let userSubscription = null;
    let hasActiveSubscription = false;

    // تهيئة عناصر النموذج
    const mediaUpload = document.getElementById('mediaUpload');
    const uploadMediaBtn = document.getElementById('uploadMediaBtn');
    const uploadFileName = document.getElementById('uploadFileName');
    const defaultRenderMode = document.getElementById('defaultRenderMode');
    const defaultMediaSize = document.getElementById('defaultMediaSize');
    const defaultOpacity = document.getElementById('defaultOpacity');
    const defaultOpacityValue = document.getElementById('defaultOpacityValue');
    const applyMediaBtn = document.getElementById('applyMediaBtn');
    const resetMediaBtn = document.getElementById('resetMediaBtn');

    // تهيئة عناصر إعدادات اللغة
    const languageSelect = document.getElementById('languageSelect');
    const applyLanguageBtn = document.getElementById('applyLanguageBtn');

    // متغيرات عامة
    let selectedFile = null;
    let mediaType = '';
    let uploadedMediaPath = ''; // تغيير من mediaObjectURL إلى uploadedMediaPath

    // تهيئة Socket.IO
    const socket = io();

    // تحميل بيانات اشتراك المستخدم
    async function loadUserSubscription() {
      try {
        if (window.firebaseHelpers && currentUser) {
          userSubscription = await window.firebaseHelpers.getUserSubscription(currentUser.uid);
          hasActiveSubscription = userSubscription &&
            userSubscription.status === 'active' &&
            new Date() < (userSubscription.endDate?.toDate ? userSubscription.endDate.toDate() : new Date(userSubscription.endDate));

          console.log('User subscription status:', hasActiveSubscription ? 'Active' : 'None');
          updateVideoAnimationUI();
        }
      } catch (error) {
        console.error('Error loading user subscription:', error);
        hasActiveSubscription = false;
        updateVideoAnimationUI();
      }
    }

    // تحديث واجهة إعدادات الفيديو والصور المتحركة حسب حالة الاشتراك
    function updateVideoAnimationUI() {
      const videoAnimationContent = document.getElementById('videoAnimationContent');
      const videoAnimationLocked = document.getElementById('videoAnimationLocked');

      if (!hasActiveSubscription) {
        // إخفاء محتوى الإعدادات وعرض رسالة القفل
        if (videoAnimationContent) videoAnimationContent.style.display = 'none';
        if (videoAnimationLocked) videoAnimationLocked.style.display = 'block';
      } else {
        // إظهار محتوى الإعدادات وإخفاء رسالة القفل
        if (videoAnimationContent) videoAnimationContent.style.display = 'block';
        if (videoAnimationLocked) videoAnimationLocked.style.display = 'none';
      }
    }

    // مراقبة حالة المصادقة
    window.addEventListener('authStateChanged', (event) => {
      currentUser = event.detail.user;
      if (currentUser) {
        console.log('User authenticated:', currentUser.email);
        loadUserSubscription();
      } else {
        console.log('User not authenticated');
        hasActiveSubscription = false;
        updateVideoAnimationUI();
      }
    });

    // === معالجات أحداث إعدادات اللغة ===

    // تحديث اللغة المختارة عند تحميل الصفحة
    function updateLanguageSelection() {
      if (window.TranslationSystem) {
        const currentLang = window.TranslationSystem.getCurrentLanguage();
        languageSelect.value = currentLang;
      }
    }

    // معالجة حدث النقر على زر تطبيق اللغة
    applyLanguageBtn.addEventListener('click', () => {
      const selectedLanguage = languageSelect.value;

      if (window.TranslationSystem) {
        // تطبيق اللغة الجديدة
        window.TranslationSystem.setLanguage(selectedLanguage);

        // إظهار رسالة تأكيد
        showSaveConfirmation('تم تطبيق اللغة بنجاح');

        // إعادة تحميل الصفحة لضمان تطبيق الترجمة على جميع العناصر
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      }
    });





    // دالة لإظهار رسالة التأكيد
    function showSaveConfirmation(message) {
      // إزالة أي رسالة موجودة
      const existingConfirmation = document.querySelector('.save-confirmation');
      if (existingConfirmation) {
        existingConfirmation.remove();
      }

      // إنشاء رسالة جديدة
      const confirmation = document.createElement('div');
      confirmation.className = 'save-confirmation';
      confirmation.textContent = message;
      document.body.appendChild(confirmation);

      // إزالة الرسالة بعد 3 ثوانٍ
      setTimeout(() => {
        confirmation.classList.add('fade-out');
        setTimeout(() => {
          if (confirmation.parentNode) {
            confirmation.parentNode.removeChild(confirmation);
          }
        }, 500);
      }, 3000);
    }

    // === معالجات أحداث إعدادات الوسائط ===

    // تحديث قيمة الشفافية عند تحريك شريط التمرير وتطبيقها مباشرة
    defaultOpacity.addEventListener('input', () => {
      if (!hasActiveSubscription) {
        return;
      }

      defaultOpacityValue.textContent = `${defaultOpacity.value}%`;

      // تطبيق الشفافية الجديدة على الخلفية الحالية إذا وجدت
      if (uploadedMediaPath) {
        applyMediaToBackground(
          uploadedMediaPath,
          defaultRenderMode.value,
          defaultMediaSize.value,
          parseInt(defaultOpacity.value, 10)
        );
      }
    });

    // تطبيق وضع العرض الجديد عند تغييره
    defaultRenderMode.addEventListener('change', () => {
      if (!hasActiveSubscription) {
        return;
      }

      if (uploadedMediaPath) {
        applyMediaToBackground(
          uploadedMediaPath,
          defaultRenderMode.value,
          defaultMediaSize.value,
          parseInt(defaultOpacity.value, 10)
        );
      }
    });

    // تطبيق حجم الوسائط الجديد عند تغييره
    defaultMediaSize.addEventListener('change', () => {
      if (!hasActiveSubscription) {
        return;
      }

      if (uploadedMediaPath) {
        applyMediaToBackground(
          uploadedMediaPath,
          defaultRenderMode.value,
          defaultMediaSize.value,
          parseInt(defaultOpacity.value, 10)
        );
      }
    });

    // معالجة حدث النقر على زر اختيار الملف
    uploadMediaBtn.addEventListener('click', () => {
      if (!hasActiveSubscription) {
        alert('هذه الميزة متاحة للمشتركين فقط. يرجى الاشتراك للوصول إلى إعدادات الفيديو والصور المتحركة.');
        return;
      }
      mediaUpload.click();
    });

    // معالجة حدث اختيار ملف
    mediaUpload.addEventListener('change', (event) => {
      if (!hasActiveSubscription) {
        alert('هذه الميزة متاحة للمشتركين فقط. يرجى الاشتراك للوصول إلى إعدادات الفيديو والصور المتحركة.');
        event.target.value = '';
        return;
      }
      if (event.target.files && event.target.files[0]) {
        selectedFile = event.target.files[0];
        uploadFileName.textContent = selectedFile.name;

        // تحديد نوع الملف
        const fileType = selectedFile.type;
        if (fileType.includes('gif')) {
          mediaType = 'gif';
        } else if (fileType.includes('webp')) {
          mediaType = 'webp';
        } else if (fileType.includes('mp4')) {
          mediaType = 'mp4';
        } else if (fileType.includes('webm')) {
          mediaType = 'webm';
        } else {
          alert('نوع الملف غير مدعوم. يرجى اختيار ملف GIF أو WebP أو MP4 أو WebM.');
          resetFileInput();
          return;
        }

        // التحقق من حجم الملف (200 ميجابايت كحد أقصى)
        const maxSize = 200 * 1024 * 1024; // 200 ميجابايت
        if (selectedFile.size > maxSize) {
          alert('حجم الملف كبير جدًا. الحد الأقصى هو 200 ميجابايت.');
          resetFileInput();
          return;
        }

        // تحميل الملف إلى الخادم بدلاً من استخدام blob
        const formData = new FormData();
        formData.append('file', selectedFile);

        // إظهار مؤشر التحميل
        uploadFileName.textContent = 'جاري تحميل الملف...';

        // إرسال الملف إلى الخادم باستخدام Fetch API
        fetch('/upload-media', {
          method: 'POST',
          body: formData
        })
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            uploadedMediaPath = data.filePath;
            uploadFileName.textContent = `تم تحميل: ${selectedFile.name}`;

            // تطبيق الوسائط على خلفية الصفحة مباشرة
            applyMediaToBackground(
              uploadedMediaPath,
              defaultRenderMode.value,
              defaultMediaSize.value,
              parseInt(defaultOpacity.value, 10)
            );


          } else {
            alert('فشل تحميل الملف: ' + (data.error || 'خطأ غير معروف'));
            resetFileInput();
          }
        })
        .catch(error => {
          console.error('خطأ في تحميل الملف:', error);
          alert('حدث خطأ أثناء تحميل الملف');
          resetFileInput();
        });
      }
    });



    // دالة لإعادة ضبط حقل الملف
    function resetFileInput() {
      mediaUpload.value = '';
      uploadFileName.textContent = 'لم يتم اختيار ملف';
      selectedFile = null;
      uploadedMediaPath = '';
    }

    // دالة لتطبيق الوسائط على خلفية الصفحة
    function applyMediaToBackground(url, renderMode, size, opacity) {
      // إزالة الخلفية الحالية إن وجدت
      const existingBg = document.getElementById('settings-background');
      if (existingBg) {
        document.body.removeChild(existingBg);
      }

      // تجنب استخدام روابط blob
      if (!url || url.startsWith('blob:')) return;

      // إنشاء حاوية الخلفية
      const bgContainer = document.createElement('div');
      bgContainer.id = 'settings-background';
      bgContainer.style.position = 'fixed';
      bgContainer.style.top = '0';
      bgContainer.style.left = '0';
      bgContainer.style.width = '100%';
      bgContainer.style.height = '100%';
      bgContainer.style.zIndex = '-1';
      bgContainer.style.overflow = 'hidden';
      bgContainer.style.pointerEvents = 'none'; // لتجنب التداخل مع النقر

      // تحديد نوع الملف
      let fileType = '';
      if (url) {
        // تحقق من نوع الملف من خلال امتداده
        const fileExt = url.split('.').pop().toLowerCase();
        if (['mp4', 'webm'].includes(fileExt)) {
          fileType = 'video';
        } else if (['gif', 'webp', 'jpg', 'jpeg', 'png'].includes(fileExt)) {
          fileType = 'image';
        } else {
          // إذا لم يكن هناك امتداد واضح، حاول تخمين النوع من الرابط
          if (url.includes('mp4') || url.includes('webm')) {
            fileType = 'video';
          } else {
            fileType = 'image'; // افتراضي
          }
        }
      }

      // إنشاء العنصر المناسب حسب نوع الملف
      if (fileType === 'video' || renderMode === 'video') {
        // إنشاء عنصر فيديو
        const video = document.createElement('video');
        video.src = url;
        video.autoplay = true;
        video.loop = true;
        video.muted = true;
        video.playsInline = true; // مهم للأجهزة المحمولة
        video.style.width = '100%';
        video.style.height = '100%';
        video.style.objectFit = 'cover'; // الافتراضي

        // تطبيق حجم الفيديو
        if (size === 'cover' || size === 'contain') {
          video.style.objectFit = size;
        } else if (size === '100%') {
          video.style.objectFit = 'contain';
        } else if (size === '75%') {
          video.style.objectFit = 'contain';
          video.style.width = '75%';
          video.style.height = '75%';
          video.style.margin = 'auto';
          video.style.display = 'block';
        } else if (size === '50%') {
          video.style.objectFit = 'contain';
          video.style.width = '50%';
          video.style.height = '50%';
          video.style.margin = 'auto';
          video.style.display = 'block';
        }

        // تطبيق الشفافية
        video.style.opacity = opacity / 100;

        // التأكد من تشغيل الفيديو
        video.addEventListener('canplay', () => {
          video.play().catch(e => console.error('خطأ في تشغيل الفيديو:', e));
        });

        // معالجة أخطاء تحميل الفيديو
        video.addEventListener('error', () => {
          console.error('خطأ في تحميل الفيديو:', url);
          // محاولة استخدام صورة بدلاً من ذلك
          createImageElement();
        });

        // إضافة الفيديو إلى الحاوية
        bgContainer.appendChild(video);
      } else {
        // إنشاء عنصر صورة
        createImageElement();
      }

      // دالة لإنشاء عنصر صورة
      function createImageElement() {
        const img = document.createElement('img');
        img.src = url;
        img.style.width = '100%';
        img.style.height = '100%';
        img.style.objectFit = 'cover'; // الافتراضي

        // تطبيق حجم الصورة
        if (size === 'cover' || size === 'contain') {
          img.style.objectFit = size;
        } else if (size === '100%') {
          img.style.objectFit = 'contain';
        } else if (size === '75%') {
          img.style.objectFit = 'contain';
          img.style.width = '75%';
          img.style.height = '75%';
          img.style.margin = 'auto';
          img.style.display = 'block';
        } else if (size === '50%') {
          img.style.objectFit = 'contain';
          img.style.width = '50%';
          img.style.height = '50%';
          img.style.margin = 'auto';
          img.style.display = 'block';
        }

        // تطبيق الشفافية
        img.style.opacity = opacity / 100;

        // معالجة أخطاء تحميل الصورة
        img.addEventListener('error', () => {
          console.error('خطأ في تحميل الصورة:', url);
        });

        // إضافة الصورة إلى الحاوية
        bgContainer.appendChild(img);
      }

      // إضافة الحاوية إلى الصفحة
      document.body.insertBefore(bgContainer, document.body.firstChild);
    }

    // معالجة حدث النقر على زر التطبيق
    applyMediaBtn.addEventListener('click', () => {
      if (!hasActiveSubscription) {
        alert('هذه الميزة متاحة للمشتركين فقط. يرجى الاشتراك للوصول إلى إعدادات الفيديو والصور المتحركة.');
        return;
      }

      // حفظ إعدادات الوسائط
      const mediaSettings = {
        renderMode: defaultRenderMode.value,
        size: defaultMediaSize.value,
        opacity: parseInt(defaultOpacity.value, 10),
        mediaUrl: uploadedMediaPath || ''
      };

      // تطبيق الوسائط على خلفية الصفحة
      applyMediaToBackground(
        mediaSettings.mediaUrl,
        mediaSettings.renderMode,
        mediaSettings.size,
        mediaSettings.opacity
      );

      // تهيئة نظام إدارة الإعدادات
      SettingsManager.initialize().then(() => {
        // حفظ الإعدادات في نظام إدارة الإعدادات
        SettingsManager.set('media.renderMode', mediaSettings.renderMode);
        SettingsManager.set('media.size', mediaSettings.size);
        SettingsManager.set('media.opacity', mediaSettings.opacity);
        SettingsManager.set('media.url', mediaSettings.mediaUrl);

        // حفظ الإعدادات في مدير الخلفية
        BackgroundManager.saveSettings(mediaSettings);

        // إرسال الإعدادات إلى الخادم
        socket.emit('updateMediaSettings', { settings: mediaSettings });
      });
    });

    // معالجة حدث النقر على زر إعادة الضبط
    resetMediaBtn.addEventListener('click', () => {
      if (!hasActiveSubscription) {
        alert('هذه الميزة متاحة للمشتركين فقط. يرجى الاشتراك للوصول إلى إعدادات الفيديو والصور المتحركة.');
        return;
      }

      if (confirm('هل أنت متأكد من إعادة ضبط إعدادات الوسائط؟')) {
        // إعادة ضبط حقول الإدخال
        resetFileInput();
        defaultRenderMode.value = 'auto';
        defaultMediaSize.value = 'cover';
        defaultOpacity.value = 15;
        defaultOpacityValue.textContent = '15%';

        // إزالة الخلفية الحالية
        const existingBg = document.getElementById('settings-background');
        if (existingBg) {
          document.body.removeChild(existingBg);
        }

        // إعادة ضبط الإعدادات في نظام إدارة الإعدادات
        SettingsManager.initialize().then(() => {
          SettingsManager.set('media.renderMode', 'auto');
          SettingsManager.set('media.size', 'cover');
          SettingsManager.set('media.opacity', 15);
          SettingsManager.set('media.url', '');

          // إعداد كائن الإعدادات
          const defaultSettings = {
            renderMode: 'auto',
            size: 'cover',
            opacity: 15,
            mediaUrl: ''
          };

          // حفظ الإعدادات في مدير الخلفية
          BackgroundManager.saveSettings(defaultSettings);

          // إرسال الإعدادات إلى الخادم
          socket.emit('updateMediaSettings', {
            settings: defaultSettings
          });
        });
      }
    });



    // استرجاع الإعدادات المحفوظة عند تحميل الصفحة
    window.addEventListener('DOMContentLoaded', () => {
      // تحديث اختيار اللغة
      updateLanguageSelection();

      // تهيئة نظام إدارة الإعدادات
      SettingsManager.initialize().then(() => {
        // استرجاع إعدادات الوسائط فقط للمشتركين
        if (hasActiveSubscription) {
          const renderMode = SettingsManager.get('media.renderMode') || 'auto';
          const size = SettingsManager.get('media.size') || 'cover';
          const opacity = SettingsManager.get('media.opacity') !== undefined ? SettingsManager.get('media.opacity') : 15;
          const url = SettingsManager.get('media.url') || '';

          // تطبيق الإعدادات المحفوظة على عناصر التحكم
          defaultRenderMode.value = renderMode;
          defaultMediaSize.value = size;
          defaultOpacity.value = opacity;
          defaultOpacityValue.textContent = `${opacity}%`;

          // إذا كان هناك رابط محفوظ وليس من نوع blob، قم بتطبيقه
          if (url && !url.startsWith('blob:')) {
            uploadedMediaPath = url;

            // تطبيق الوسائط على خلفية الصفحة
            applyMediaToBackground(url, renderMode, size, opacity);
          } else if (url && url.startsWith('blob:')) {
            // إذا كان الرابط من نوع blob، قم بمسحه
            SettingsManager.set('media.url', '');
          }
        }
      });
    });
  </script>
  <script src="/js/global-text-direction.js"></script>
  <script src="/dark-mode.js"></script>
  <script src="/js/translation.js"></script>
</body>
</html>