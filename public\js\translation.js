// نظام الترجمة للتطبيق
class TranslationSystem {
  constructor() {
    this.currentLanguage = 'ar'; // اللغة الافتراضية العربية
    this.translations = {};
    this.isInitialized = false;

    // تحميل اللغة المحفوظة
    const savedLanguage = localStorage.getItem('app_language');
    if (savedLanguage) {
      this.currentLanguage = savedLanguage;
    }
  }

  // تهيئة النظام
  async initialize() {
    if (this.isInitialized) return;

    try {
      // تحميل ملفات الترجمة
      await this.loadTranslations();

      // تطبيق الترجمة الحالية
      this.applyTranslations();

      this.isInitialized = true;
      console.log('Translation system initialized successfully');
    } catch (error) {
      console.error('Failed to initialize translation system:', error);
    }
  }

  // تحميل ملفات الترجمة
  async loadTranslations() {
    try {
      // تحميل الترجمة العربية
      const arResponse = await fetch('/js/translations/ar.json');
      if (arResponse.ok) {
        this.translations.ar = await arResponse.json();
      } else {
        // إذا لم يوجد الملف، استخدم ترجمات افتراضية
        this.translations.ar = this.getDefaultArabicTranslations();
      }

      // تحميل الترجمة الإنجليزية
      const enResponse = await fetch('/js/translations/en.json');
      if (enResponse.ok) {
        this.translations.en = await enResponse.json();
      } else {
        // إذا لم يوجد الملف، استخدم ترجمات افتراضية
        this.translations.en = this.getDefaultEnglishTranslations();
      }
    } catch (error) {
      console.warn('Could not load translation files, using defaults:', error);
      // استخدام الترجمات الافتراضية
      this.translations.ar = this.getDefaultArabicTranslations();
      this.translations.en = this.getDefaultEnglishTranslations();
    }
  }

  // الحصول على الترجمات العربية الافتراضية
  getDefaultArabicTranslations() {
    return {
      // العناوين الرئيسية
      "الاتصال بـ TikTok Live": "الاتصال بـ TikTok Live",
      "ربط الهدايا بالإجراءات": "ربط الهدايا بالإجراءات",
      "قراءة التعليقات": "قراءة التعليقات",
      "الإعدادات": "الإعدادات",
      "الملفات الشخصية": "الملفات الشخصية",
      "اتصل بنا": "اتصل بنا",
      "فتح Overlay": "فتح Overlay",

      // أزرار عامة
      "حفظ": "حفظ",
      "إلغاء": "إلغاء",
      "تطبيق": "تطبيق",
      "إعادة تعيين": "إعادة تعيين",
      "اختبار": "اختبار",
      "إضافة": "إضافة",
      "تعديل": "تعديل",
      "حذف": "حذف",

      // رسائل عامة
      "تم الحفظ بنجاح": "تم الحفظ بنجاح",
      "حدث خطأ": "حدث خطأ",
      "جاري التحميل...": "جاري التحميل...",
      "لا توجد بيانات": "لا توجد بيانات"
    };
  }

  // الحصول على الترجمات الإنجليزية الافتراضية
  getDefaultEnglishTranslations() {
    return {
      // العناوين الرئيسية
      "الاتصال بـ TikTok Live": "Connect to TikTok Live",
      "ربط الهدايا بالإجراءات": "Gift to Action Mapping",
      "قراءة التعليقات": "Text-to-Speech Comments",
      "الإعدادات": "Settings",
      "الملفات الشخصية": "Profiles",
      "اتصل بنا": "Contact Us",
      "فتح Overlay": "Open Overlay",

      // أزرار عامة
      "حفظ": "Save",
      "إلغاء": "Cancel",
      "تطبيق": "Apply",
      "إعادة تعيين": "Reset",
      "اختبار": "Test",
      "إضافة": "Add",
      "تعديل": "Edit",
      "حذف": "Delete",

      // رسائل عامة
      "تم الحفظ بنجاح": "Saved Successfully",
      "حدث خطأ": "An Error Occurred",
      "جاري التحميل...": "Loading...",
      "لا توجد بيانات": "No Data Available"
    };
  }

  // تبديل اللغة (للاستخدام من الإعدادات)
  setLanguage(language) {
    if (language === this.currentLanguage) return;

    this.currentLanguage = language;

    // حفظ اللغة الجديدة
    localStorage.setItem('app_language', this.currentLanguage);

    // تطبيق الترجمة الجديدة
    this.applyTranslations();
  }

  // تطبيق الترجمات على الصفحة
  applyTranslations() {
    const currentTranslations = this.translations[this.currentLanguage] || {};

    // ترجمة النصوص المباشرة
    this.translateTextNodes(document.body, currentTranslations);

    // ترجمة العناصر ذات الخصائص المحددة
    this.translateAttributes(currentTranslations);
  }

  // ترجمة النصوص في العقد
  translateTextNodes(element, translations) {
    if (element.nodeType === Node.TEXT_NODE) {
      const text = element.textContent.trim();
      if (text && translations[text]) {
        element.textContent = translations[text];
      }
    } else {
      for (let child of element.childNodes) {
        this.translateTextNodes(child, translations);
      }
    }
  }

  // ترجمة الخصائص
  translateAttributes(translations) {
    // ترجمة العناوين
    document.querySelectorAll('[title]').forEach(element => {
      const title = element.getAttribute('title');
      if (translations[title]) {
        element.setAttribute('title', translations[title]);
      }
    });

    // ترجمة النصوص التوضيحية
    document.querySelectorAll('[placeholder]').forEach(element => {
      const placeholder = element.getAttribute('placeholder');
      if (translations[placeholder]) {
        element.setAttribute('placeholder', translations[placeholder]);
      }
    });
  }



  // الحصول على ترجمة نص محدد
  translate(key) {
    const currentTranslations = this.translations[this.currentLanguage] || {};
    return currentTranslations[key] || key;
  }

  // الحصول على اللغة الحالية
  getCurrentLanguage() {
    return this.currentLanguage;
  }

  // ترجمة المحتوى الديناميكي (للاستخدام مع الجداول والمحتوى المُنشأ بـ JavaScript)
  translateDynamicContent(element = document.body) {
    const currentTranslations = this.translations[this.currentLanguage] || {};
    this.translateTextNodes(element, currentTranslations);
    this.translateAttributes(currentTranslations);
  }

  // دالة مساعدة لترجمة نص واحد
  translateText(text) {
    const currentTranslations = this.translations[this.currentLanguage] || {};
    return currentTranslations[text] || text;
  }
}

// إنشاء مثيل عام لنظام الترجمة
const translationSystem = new TranslationSystem();

// تهيئة النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  translationSystem.initialize();
});

// تصدير النظام للاستخدام العام
window.TranslationSystem = translationSystem;
