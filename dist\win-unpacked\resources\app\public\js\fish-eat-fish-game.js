// Fish Eat Fish - لعبة الأسماك الاحترافية
// تطوير: Augment Agent

class FishEatFishGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.socket = null;
        this.isDemo = new URLSearchParams(window.location.search).has('demo');

        // إعدادات اللعبة - تصغير المساحة لتكبير الأسماك
        this.gameWidth = Math.min(1200, window.innerWidth);  // حد أقصى 1200 بكسل
        this.gameHeight = Math.min(800, window.innerHeight); // حد أقصى 800 بكسل
        this.canvas.width = this.gameWidth;
        this.canvas.height = this.gameHeight;

        // إعدادات الكاميرا والتكبير (محسنة لـ TikTok Live)
        this.camera = {
            x: 0,
            y: 0,
            zoom: 1.0,  // تكبير افتراضي 1x لرؤية أوسع في TikTok Live
            targetZoom: 1.0,
            followTarget: null,
            smoothing: 0.1,
            minZoom: 0.5,  // تم تقليل الحد الأدنى لدعم التكبير الأقل
            maxZoom: 5.0   // تم زيادة الحد الأقصى قليلاً
        };

        // حالة اللعبة
        this.gameState = {
            isRunning: false,
            isPaused: false,
            startTime: Date.now(),
            totalFish: 0,
            battles: 0,
            rosesReceived: 0,
            fps: 0,
            lastFpsUpdate: Date.now(),
            frameCount: 0,
            activeBattles: 0,
            totalDeaths: 0
        };

        // إعدادات معدل الإطارات
        this.frameRate = {
            targetFPS: 60,
            frameInterval: 1000 / 60, // 16.67ms للـ 60 FPS
            lastFrameTime: 0,
            deltaTime: 0
        };

        // مصفوفات الكائنات
        this.fish = [];
        this.particles = [];
        this.bubbles = [];
        this.players = new Map();
        this.achievements = [];

        // إعدادات الفيزياء
        this.physics = {
            gravity: 0.1,
            friction: 0.98,
            waterResistance: 0.95,
            currentStrength: 0.5
        };

        // إعدادات البيئة
        this.environment = {
            lightRays: [],
            seaweed: [],
            rocks: []
        };

        // إعدادات الصوت
        this.sounds = {
            enabled: true,
            volume: 0.5,
            audioContext: null,
            soundBuffers: new Map()
        };

        // إعدادات اللعبة - سيتم تحميلها من localStorage
        this.gameSettings = this.loadGameSettings();

        // متغيرات التحكم
        this.mouse = { x: 0, y: 0, isDown: false };
        this.touch = { x: 0, y: 0, isActive: false };

        // أنواع الأسماك المختلفة
        this.fishTypes = {
            // الأسماك العادية
            normal: {
                name: 'سمك عادي',
                colors: ['#4A90E2', '#7ED321', '#D0021B', '#F5A623'],
                speed: 1,
                aggression: 0.5,
                special: null,
                baseHealth: 100,
                attackPower: 15,
                defense: 5
            },
            // سمك القرش
            shark: {
                name: 'قرش',
                colors: ['#708090', '#2F4F4F', '#1C1C1C', '#000000'],
                speed: 1.5,
                aggression: 0.9,
                special: 'hunt_boost',
                baseHealth: 200,
                attackPower: 35,
                defense: 15
            },
            // سمك الراي
            ray: {
                name: 'راي لساع',
                colors: ['#DDA0DD', '#9370DB', '#8A2BE2', '#4B0082'],
                speed: 0.8,
                aggression: 0.3,
                special: 'electric_stun',
                baseHealth: 120,
                attackPower: 25,
                defense: 20
            },
            // سمك السلمون
            salmon: {
                name: 'سلمون ذهبي',
                colors: ['#FFD700', '#FFA500', '#FF8C00', '#FF4500'],
                speed: 1.2,
                aggression: 0.4,
                special: 'speed_burst',
                baseHealth: 90,
                attackPower: 18,
                defense: 8
            },
            // سمك الأنقليس
            eel: {
                name: 'أنقليس كهربائي',
                colors: ['#00CED1', '#20B2AA', '#008B8B', '#006666'],
                speed: 1.3,
                aggression: 0.7,
                special: 'electric_field',
                baseHealth: 110,
                attackPower: 22,
                defense: 12
            },
            // سمك الحوت
            whale: {
                name: 'حوت أزرق',
                colors: ['#4169E1', '#0000FF', '#0000CD', '#191970'],
                speed: 0.6,
                aggression: 0.2,
                special: 'massive_size',
                baseHealth: 500,
                attackPower: 50,
                defense: 30
            },
            // سمك الدولفين
            dolphin: {
                name: 'دولفين سريع',
                colors: ['#87CEEB', '#87CEFA', '#00BFFF', '#1E90FF'],
                speed: 2.0,
                aggression: 0.6,
                special: 'sonic_speed',
                baseHealth: 150,
                attackPower: 28,
                defense: 12
            },
            // قنديل البحر
            jellyfish: {
                name: 'قنديل البحر',
                colors: ['#ff6b9d', '#ff8fab', '#ffa8cc', '#ffb3d9'],
                speed: 0.5,
                aggression: 0.8,
                special: 'poison_sting',
                baseHealth: 80,
                attackPower: 30,
                defense: 5
            },
            // الأخطبوط
            octopus: {
                name: 'أخطبوط عملاق',
                colors: ['#6c5ce7', '#a29bfe', '#fd79a8', '#e84393'],
                speed: 1.1,
                aggression: 0.9,
                special: 'tentacle_grab',
                baseHealth: 180,
                attackPower: 32,
                defense: 18
            },
            // حصان البحر
            seahorse: {
                name: 'حصان البحر الملكي',
                colors: ['#fdcb6e', '#e17055', '#d63031', '#74b9ff'],
                speed: 0.7,
                aggression: 0.3,
                special: 'royal_grace',
                baseHealth: 70,
                attackPower: 12,
                defense: 15
            },
            // السلحفاة
            turtle: {
                name: 'سلحفاة البحر العملاقة',
                colors: ['#00b894', '#00cec9', '#55a3ff', '#2d3436'],
                speed: 0.4,
                aggression: 0.1,
                special: 'shell_defense',
                baseHealth: 300,
                attackPower: 20,
                defense: 40
            },
            // سرطان البحر
            crab: {
                name: 'سرطان البحر المحارب',
                colors: ['#e17055', '#d63031', '#fd79a8', '#fdcb6e'],
                speed: 0.8,
                aggression: 0.7,
                special: 'claw_attack',
                baseHealth: 130,
                attackPower: 25,
                defense: 25
            },
            // الكركند
            lobster: {
                name: 'كركند عملاق',
                colors: ['#e84393', '#fd79a8', '#fdcb6e', '#e17055'],
                speed: 0.9,
                aggression: 0.8,
                special: 'pincer_crush',
                baseHealth: 160,
                attackPower: 30,
                defense: 22
            },
            // نجمة البحر
            starfish: {
                name: 'نجمة البحر السحرية',
                colors: ['#fd79a8', '#fdcb6e', '#55a3ff', '#00b894'],
                speed: 0.3,
                aggression: 0.2,
                special: 'regeneration',
                baseHealth: 60,
                attackPower: 8,
                defense: 10
            },
            // سمكة الصياد
            anglerfish: {
                name: 'سمكة الصياد المرعبة',
                colors: ['#2d3436', '#636e72', '#00b894', '#0984e3'],
                speed: 0.6,
                aggression: 0.95,
                special: 'lure_trap',
                baseHealth: 220,
                attackPower: 40,
                defense: 20
            },
            // سمكة السيف
            swordfish: {
                name: 'سمكة السيف السريعة',
                colors: ['#74b9ff', '#0984e3', '#00cec9', '#2d3436'],
                speed: 2.2,
                aggression: 0.8,
                special: 'sword_strike',
                baseHealth: 140,
                attackPower: 35,
                defense: 10
            },
            // القرش المطرقة
            hammerhead: {
                name: 'القرش المطرقة العملاق',
                colors: ['#636e72', '#2d3436', '#00b894', '#0984e3'],
                speed: 1.8,
                aggression: 0.95,
                special: 'hammer_smash',
                baseHealth: 250,
                attackPower: 45,
                defense: 18
            }
        };

        // ألوان الأسماك حسب الحجم (للأسماك العادية)
        this.fishColors = {
            small: '#4A90E2',      // أزرق
            medium: '#7ED321',     // أخضر
            large: '#D0021B',      // أحمر
            giant: '#F5A623'       // ذهبي
        };

        // عداد اللاعبين لدعم 100+ لاعب
        this.maxPlayers = 150;
        this.playerColors = this.generatePlayerColors();

        // متغير لتتبع آخر إعدادات محفوظة
        let currentSettings = localStorage.getItem('fishGameSettings');
        if (!currentSettings) {
            currentSettings = localStorage.getItem('fishEatFishSettings');
        }
        this.lastSettingsString = currentSettings;

        // تحميل إعدادات اللعبة أولاً مع معالجة الأخطاء
        console.log('🔄 بدء تحميل إعدادات اللعبة...');
        try {
            this.gameSettings = this.loadGameSettings();
            console.log('✅ تم تحميل الإعدادات بنجاح');
        } catch (settingsError) {
            console.error('❌ خطأ في تحميل الإعدادات:', settingsError);
            console.log('🔄 استخدام الإعدادات الافتراضية الآمنة...');
            this.gameSettings = this.getDefaultSettings();
        }

        // عرض الإعدادات المطبقة للتشخيص
        console.log('🎯 الإعدادات النهائية المطبقة:');
        console.log('  - defaultZoom:', this.gameSettings.defaultZoom);
        console.log('  - autoFollowLargeFish:', this.gameSettings.autoFollowLargeFish);
        console.log('  - soundEnabled:', this.gameSettings.soundEnabled);
        console.log('  - musicEnabled:', this.gameSettings.musicEnabled);
        console.log('  - showActivePlayers:', this.gameSettings.showActivePlayers);

        this.init();

        // تطبيق إعدادات الكاميرا المحفوظة
        console.log('🎮 تطبيق إعدادات الكاميرا...');
        this.applyCameraSettings();

        // تطبيق إعداد معدل الإطارات
        console.log('🎯 تطبيق إعداد معدل الإطارات...');
        this.applyTargetFPS();

        // تطبيق إعداد دقة النافذة
        console.log('🖥️ تطبيق إعداد دقة النافذة...');
        this.applyGameResolution();

        // تأكيد تطبيق التكبير
        console.log('🔍 تم تطبيق التكبير:', this.camera.zoom + 'x');

        // مراقبة تحديث الإعدادات والأصوات المخصصة
        this.setupSettingsWatcher();
    }

    // مراقبة تحديث الإعدادات
    setupSettingsWatcher() {
        // مراقبة تغييرات localStorage للأصوات المخصصة
        window.addEventListener('storage', (e) => {
            if (e.key && e.key.startsWith('fishGameCustomSound_')) {
                console.log('🔄 تم اكتشاف تحديث في الأصوات المخصصة');
                this.loadCustomSounds();
            } else if (e.key === 'fishGameCustomSounds') {
                console.log('🔄 تم اكتشاف تحديث في قائمة الأصوات المخصصة');
                this.loadCustomSounds();
            }
        });

        // مراقبة تحديث الإعدادات كل ثانية (للتحديث من نفس النافذة)
        setInterval(() => {
            // البحث في المفتاح الجديد أولاً، ثم القديم
            let currentSettings = localStorage.getItem('fishGameSettings');
            if (!currentSettings) {
                currentSettings = localStorage.getItem('fishEatFishSettings');
            }

            if (currentSettings !== this.lastSettingsString) {
                this.lastSettingsString = currentSettings;
                this.gameSettings = this.loadGameSettings();
                this.applyUIVisibilitySettings();
                this.applyCameraSettings();
                this.applyTargetFPS();
                this.applyGameResolution();
                console.log('🔄 تم تحديث إعدادات اللعبة');
            }
        }, 1000);

        // مراقبة رسائل تحديث الأصوات من صفحة الإعدادات
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'customSoundUpdate') {
                console.log('📢 تم استلام إشعار تحديث الأصوات من صفحة الإعدادات');
                this.loadCustomSounds();
            }
        });

        // مراقبة تحديث الأصوات عبر localStorage
        this.lastSoundUpdate = localStorage.getItem('fishGameSoundUpdate');
        setInterval(() => {
            const currentSoundUpdate = localStorage.getItem('fishGameSoundUpdate');
            if (currentSoundUpdate !== this.lastSoundUpdate) {
                this.lastSoundUpdate = currentSoundUpdate;
                console.log('🔄 تم اكتشاف تحديث في الأصوات المخصصة');
                this.loadCustomSounds();
            }
        }, 500);
    }

    // تحميل إعدادات اللعبة من localStorage أو URL parameters
    loadGameSettings() {
        try {
            // أولاً: محاولة قراءة الإعدادات من URL parameters (للتوافق مع TikTok Live)
            const urlParams = new URLSearchParams(window.location.search);

            // طريقة 1: إعدادات مضغوطة (الطريقة الجديدة لـ TikTok Live)
            const compressedSettings = urlParams.get('s');
            if (compressedSettings) {
                try {
                    console.log('🔍 محاولة قراءة إعدادات مضغوطة من URL...');
                    const decodedData = atob(compressedSettings);
                    console.log('✅ تم فك تشفير البيانات بنجاح');

                    const compactSettings = JSON.parse(decodedData);
                    console.log('📂 تم تحميل إعدادات مضغوطة من URL لـ TikTok Live');
                    console.log('🔍 الإعدادات المضغوطة:', compactSettings);

                    // التحقق من صحة البيانات
                    if (typeof compactSettings !== 'object' || compactSettings === null) {
                        throw new Error('البيانات المضغوطة غير صحيحة');
                    }

                    // تحويل الإعدادات المضغوطة إلى تنسيق كامل مع قيم افتراضية آمنة
                    const expandedSettings = {
                        defaultZoom: (typeof compactSettings.zoom === 'number' && compactSettings.zoom > 0) ? compactSettings.zoom : 1.0,
                        autoFollowLargeFish: compactSettings.follow !== false,
                        soundEnabled: compactSettings.sound !== false,
                        musicEnabled: compactSettings.music !== false,
                        effectsVolume: (typeof compactSettings.vol === 'number') ? Math.max(0, Math.min(10, compactSettings.vol)) / 10 : 0.7,
                        showActivePlayers: compactSettings.ui?.players === true,
                        showGameStats: compactSettings.ui?.stats === true,
                        showControls: compactSettings.ui?.controls === true
                    };

                    // معالجة التعيينات المضغوطة
                    if (compactSettings.assignments && Array.isArray(compactSettings.assignments)) {
                        console.log('📋 معالجة التعيينات المضغوطة:', compactSettings.assignments.length, 'تعيين');

                        // تحويل التعيينات المضغوطة إلى تنسيق كامل
                        const expandedAssignments = compactSettings.assignments.map(a => ({
                            giftName: a.gift || 'Unknown',
                            fishType: a.type || 'normal',
                            fishSize: a.size || 'medium',
                            soundFile: a.sound === 'custom' ? 'custom_sound' : null,
                            id: Date.now() + Math.random() // معرف فريد
                        }));

                        // حفظ التعيينات في localStorage للاستخدام
                        localStorage.setItem('fishGameAssignments', JSON.stringify(expandedAssignments));
                        console.log('✅ تم حفظ', expandedAssignments.length, 'تعيين من الرابط المضغوط');
                    }

                    // معالجة الأصوات المخصصة المضغوطة
                    if (compactSettings.sounds && typeof compactSettings.sounds === 'object') {
                        console.log('🔊 معالجة الأصوات المخصصة المضغوطة:', Object.keys(compactSettings.sounds).length, 'صوت');

                        // حفظ الأصوات المخصصة في localStorage
                        for (const [soundName, soundData] of Object.entries(compactSettings.sounds)) {
                            try {
                                const soundKey = 'customSound_' + soundName;
                                localStorage.setItem(soundKey, soundData);
                                console.log('✅ تم حفظ صوت مخصص:', soundName);
                            } catch (error) {
                                console.warn('⚠️ فشل في حفظ الصوت:', soundName, error);
                            }
                        }

                        console.log('🔊 تم حفظ', Object.keys(compactSettings.sounds).length, 'صوت مخصص من الرابط المضغوط');

                        // إعادة تحميل الأصوات المخصصة
                        if (this.sounds.audioContext) {
                            this.loadCustomSoundsFromBase64();
                        }
                    }

                    const defaultSettings = this.getDefaultSettings();
                    const mergedSettings = { ...defaultSettings, ...expandedSettings };

                    console.log('✅ تم توسيع ودمج الإعدادات المضغوطة:');
                    console.log('  - defaultZoom:', mergedSettings.defaultZoom);
                    console.log('  - autoFollowLargeFish:', mergedSettings.autoFollowLargeFish);
                    console.log('  - soundEnabled:', mergedSettings.soundEnabled);
                    console.log('  - musicEnabled:', mergedSettings.musicEnabled);

                    // عرض معلومات التعيينات والأصوات
                    const assignmentsCount = compactSettings.assignments ? compactSettings.assignments.length : 0;
                    const soundsCount = compactSettings.sounds ? Object.keys(compactSettings.sounds).length : 0;
                    console.log('  - التعيينات المضمنة:', assignmentsCount, 'تعيين');
                    console.log('  - الأصوات المخصصة المضمنة:', soundsCount, 'صوت');

                    console.log('🎯 الإعدادات والتعيينات والأصوات جاهزة لـ TikTok Live!');

                    return mergedSettings;
                } catch (compressedError) {
                    console.error('❌ فشل في قراءة الإعدادات المضغوطة:', compressedError);
                    console.log('🔄 سيتم استخدام الإعدادات الافتراضية بدلاً من ذلك');
                }
            }

            // ملاحظة: تم حذف الطرق القديمة لقراءة الإعدادات لأن الطريقة المضغوطة تعمل بشكل مثالي

            // ثانياً: البحث في localStorage
            let savedSettings = localStorage.getItem('fishGameSettings');
            if (!savedSettings) {
                savedSettings = localStorage.getItem('fishEatFishSettings'); // للتوافق مع النسخة القديمة
            }

            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                console.log('📂 تم تحميل إعدادات اللعبة من صفحة الإعدادات');
                console.log('🔍 الإعدادات المحملة:', settings);

                // عرض بعض الإعدادات المهمة للتشخيص
                console.log('🎯 إعدادات مهمة:');
                console.log('  - defaultZoom:', settings.defaultZoom);
                console.log('  - autoFollowLargeFish:', settings.autoFollowLargeFish);
                console.log('  - soundEnabled:', settings.soundEnabled);
                console.log('  - showActivePlayers:', settings.showActivePlayers);

                // دمج الإعدادات المحفوظة مع الافتراضية (الأولوية للمحفوظة)
                const defaultSettings = this.getDefaultSettings();
                const mergedSettings = { ...defaultSettings, ...settings };

                console.log('✅ تم دمج الإعدادات المخصصة مع الافتراضية');
                console.log('🔧 الإعدادات النهائية المطبقة:');
                console.log('  - defaultZoom:', mergedSettings.defaultZoom);
                console.log('  - autoFollowLargeFish:', mergedSettings.autoFollowLargeFish);
                console.log('  - soundEnabled:', mergedSettings.soundEnabled);

                // إعادة تحميل الأصوات المخصصة عند تحديث الإعدادات
                if (this.sounds.audioContext) {
                    this.loadCustomSounds();
                }

                return mergedSettings;
            }
        } catch (error) {
            console.warn('⚠️ فشل في تحميل الإعدادات، استخدام الإعدادات الافتراضية');
            console.error('تفاصيل الخطأ:', error);
        }

        // الإعدادات الافتراضية
        console.log('❌ لم يتم العثور على إعدادات محفوظة - استخدام الإعدادات الافتراضية');

        // تم حذف نظام الإعدادات المخصصة القديم لأن النظام الجديد أفضل

        console.log('💡 لحل هذه المشكلة:');
        console.log('  1. افتح صفحة الإعدادات');
        console.log('  2. اضغط "🔧 تحديث الإعدادات الافتراضية"');
        console.log('  3. أو اضغط "📱 إنشاء رابط TikTok Live"');

        const defaultSettings = this.getDefaultSettings();
        console.log('🔧 الإعدادات الافتراضية المطبقة:');
        console.log('  - defaultZoom:', defaultSettings.defaultZoom);
        console.log('  - autoFollowLargeFish:', defaultSettings.autoFollowLargeFish);

        return defaultSettings;
    }

    // الحصول على الإعدادات الافتراضية
    getDefaultSettings() {
        return {
            giftToFishMapping: {
                'Rose': 'random',
                'Diamond': 'shark',
                'Crown': 'whale',
                'Lightning': 'eel',
                'Butterfly': 'ray',
                'Star': 'salmon'
            },
            soundEnabled: true,
            musicEnabled: true,
            effectsVolume: 0.7,
            musicVolume: 0.3,
            ambientSounds: true,
            ambientVolume: 0.3,
            scaryEnabled: true,
            realisticEating: true,
            growthSpeed: 'normal',
            fishAggression: 1,
            maxFish: 150,
            autoBattles: true,
            graphicsQuality: 'medium',
            particleCount: 1,
            glowEffects: true,
            showPlayerNames: true,
            targetFPS: '60',
            gameResolution: '1920x1080',
            performanceMode: false,
            autoSave: true,
            autoDeleteEnabled: false,  // الحذف التلقائي معطل افتراضياً
            fishLifetime: 60,          // مدة بقاء السمكة بالثواني
            maxFishLimit: 100,         // الحد الأقصى للأسماك
            showActivePlayers: false,  // مخفي افتراضياً لـ TikTok Live
            showGameStats: false,      // مخفي افتراضياً لـ TikTok Live
            showControls: false,       // مخفي افتراضياً لـ TikTok Live
            showAchievements: false,   // مخفي افتراضياً لـ TikTok Live

            // إعدادات الكاميرا الافتراضية (محسنة لـ TikTok Live)
            defaultZoom: 1.0,              // تكبير أقل لرؤية أوسع
            autoFollowLargeFish: true,      // متابعة تلقائية مفعلة
            autoFollowThreshold: 20,        // حد المتابعة (أقل لتشمل المخلوقات المتوسطة)
            cameraSpeed: 0.08,              // سرعة الكاميرا
            mouseWheelZoom: true            // تكبير بعجلة الماوس
        };
    }

    // تهيئة نظام الصوت
    async initAudioSystem() {
        try {
            this.sounds.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            await this.loadSounds();
            console.log('🔊 تم تهيئة نظام الصوت بنجاح');
        } catch (error) {
            console.warn('⚠️ فشل في تهيئة نظام الصوت:', error);
            this.sounds.enabled = false;
        }
    }

    // تحميل الأصوات (تم إلغاء جميع الأصوات الافتراضية)
    async loadSounds() {
        // تم حذف جميع الأصوات الافتراضية نهائياً
        // ستعمل الأصوات المخصصة فقط
        console.log('🔇 تم إلغاء جميع الأصوات الافتراضية - ستعمل الأصوات المخصصة فقط');

        // تحميل الأصوات المخصصة من localStorage
        await this.loadCustomSounds();

        // إعداد مراقب لتحديثات الأصوات المخصصة
        this.setupCustomSoundListener();
    }

    // تحميل الأصوات المخصصة من localStorage
    async loadCustomSounds() {
        try {
            console.log('🔄 بدء تحميل الأصوات المخصصة...');

            // إنشاء قائمة منفصلة للأصوات المخصصة
            if (!this.customSoundBuffers) {
                this.customSoundBuffers = new Map();
            }

            // البحث عن جميع الأصوات المخصصة في localStorage
            const customSoundKeys = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith('fishGameCustomSound_')) {
                    const soundName = key.replace('fishGameCustomSound_', '');
                    customSoundKeys.push(soundName);
                }
            }

            if (customSoundKeys.length === 0) {
                console.log('📂 لا توجد أصوات مخصصة محفوظة');
                return;
            }

            console.log('🎵 تم العثور على أصوات مخصصة:', customSoundKeys);

            // تحميل كل صوت مخصص محفوظ
            for (const soundName of customSoundKeys) {
                const audioData = localStorage.getItem(`fishGameCustomSound_${soundName}`);
                if (audioData) {
                    try {
                        console.log(`⏳ تحميل صوت مخصص: ${soundName}...`);

                        // تحويل البيانات المحفوظة إلى ArrayBuffer بطريقة آمنة
                        const binaryString = atob(audioData);
                        const arrayBuffer = new ArrayBuffer(binaryString.length);
                        const uint8Array = new Uint8Array(arrayBuffer);

                        // نسخ البيانات بطريقة آمنة
                        for (let i = 0; i < binaryString.length; i++) {
                            uint8Array[i] = binaryString.charCodeAt(i);
                        }

                        // إنشاء نسخة للفك تشفير
                        const arrayBufferCopy = arrayBuffer.slice();

                        // فك تشفير الصوت
                        const audioBuffer = await this.sounds.audioContext.decodeAudioData(arrayBufferCopy);

                        // حفظ في قائمة الأصوات المخصصة المنفصلة
                        this.customSoundBuffers.set(soundName, audioBuffer);

                        console.log(`✅ تم تحميل صوت مخصص بنجاح: ${soundName} (${(audioData.length / 1024).toFixed(1)} KB)`);
                    } catch (error) {
                        console.warn(`⚠️ فشل في تحميل الصوت المخصص: ${soundName}`, error);
                        // حذف البيانات التالفة
                        localStorage.removeItem(`fishGameCustomSound_${soundName}`);
                    }
                }
            }

            console.log(`🎵 تم تحميل ${this.customSoundBuffers.size} صوت مخصص بنجاح`);

            // تحديث قائمة الأصوات المخصصة في localStorage
            this.updateCustomSoundsList();

        } catch (error) {
            console.error('⚠️ فشل في تحميل الأصوات المخصصة:', error);
        }
    }

    // تحديث قائمة الأصوات المخصصة
    updateCustomSoundsList() {
        try {
            const customSounds = {};

            // إنشاء قائمة محدثة من الأصوات المحملة
            if (this.customSoundBuffers) {
                for (const soundName of this.customSoundBuffers.keys()) {
                    customSounds[soundName] = {
                        fileName: `${soundName}_custom.audio`,
                        size: 0,
                        timestamp: Date.now()
                    };
                }
            }

            // حفظ القائمة المحدثة
            localStorage.setItem('fishGameCustomSounds', JSON.stringify(customSounds));
            console.log('📝 تم تحديث قائمة الأصوات المخصصة:', Object.keys(customSounds));

        } catch (error) {
            console.warn('فشل في تحديث قائمة الأصوات المخصصة:', error);
        }
    }

    // إعداد مراقب لتحديثات الأصوات المخصصة
    setupCustomSoundListener() {
        // مراقبة تغييرات localStorage
        window.addEventListener('storage', (event) => {
            if (event.key === 'fishGameSoundUpdate') {
                console.log('🔄 تم اكتشاف تحديث في الأصوات المخصصة، إعادة تحميل...');
                this.loadCustomSounds();
            }
        });

        // مراقبة رسائل النوافذ الأخرى
        window.addEventListener('message', (event) => {
            if (event.data && event.data.type === 'customSoundUpdate') {
                console.log('📨 تم استلام إشعار تحديث الأصوات من نافذة أخرى');
                this.loadCustomSounds();
            }
        });

        console.log('👂 تم إعداد مراقب تحديثات الأصوات المخصصة');
    }

    // تحميل الأصوات المخصصة من Base64 (للاستخدام مع TikTok Live)
    async loadCustomSoundsFromBase64() {
        if (!this.sounds.audioContext) return;

        console.log('🔊 بدء تحميل الأصوات المخصصة من Base64...');

        // البحث عن الأصوات المحفوظة بالطريقة الجديدة
        let soundNames = ['eat_small', 'eat_large', 'shark_roar', 'whale_call', 'spawn', 'ocean_waves', 'underwater_bubbles', 'deep_sea', 'whale_song'];

        // إضافة أصوات من التعيينات
        try {
            const assignments = JSON.parse(localStorage.getItem('fishGameAssignments') || '[]');
            for (const assignment of assignments) {
                if (assignment.soundFile && assignment.soundFile !== 'default' && !soundNames.includes(assignment.soundFile)) {
                    soundNames.push(assignment.soundFile);
                }
            }
        } catch (error) {
            console.warn('⚠️ فشل في قراءة التعيينات للأصوات:', error);
        }

        for (const soundName of soundNames) {
            try {
                const soundKey = 'customSound_' + soundName;
                const base64Data = localStorage.getItem(soundKey);

                if (base64Data) {
                    // تحويل Base64 إلى ArrayBuffer
                    const response = await fetch(base64Data);
                    const arrayBuffer = await response.arrayBuffer();

                    // فك تشفير الصوت
                    const audioBuffer = await this.sounds.audioContext.decodeAudioData(arrayBuffer);

                    // حفظ في ذاكرة الأصوات
                    if (!this.customSoundBuffers) {
                        this.customSoundBuffers = new Map();
                    }
                    this.customSoundBuffers.set(soundName, audioBuffer);

                    console.log('✅ تم تحميل صوت مخصص من Base64:', soundName);
                }
            } catch (error) {
                console.warn('⚠️ فشل في تحميل الصوت المخصص:', soundName, error);
            }
        }

        console.log('🔊 اكتمل تحميل الأصوات المخصصة من Base64');
    }

    // تطبيق إعداد معدل الإطارات المستهدف
    applyTargetFPS() {
        const targetFPS = parseInt(this.gameSettings.targetFPS) || 60;

        // تحديث إعدادات معدل الإطارات
        this.frameRate.targetFPS = targetFPS;
        this.frameRate.frameInterval = 1000 / targetFPS;

        console.log(`🎯 تم تطبيق معدل الإطارات المستهدف: ${targetFPS} FPS (${this.frameRate.frameInterval.toFixed(2)}ms لكل إطار)`);
    }

    // تطبيق إعداد دقة النافذة
    applyGameResolution() {
        const resolution = this.gameSettings.gameResolution || '1920x1080';

        if (resolution === 'auto') {
            // استخدام دقة الشاشة الحالية
            this.gameWidth = Math.min(window.innerWidth, window.screen.width);
            this.gameHeight = Math.min(window.innerHeight, window.screen.height);
        } else {
            // تطبيق الدقة المحددة
            const [width, height] = resolution.split('x').map(Number);
            this.gameWidth = width;
            this.gameHeight = height;
        }

        // تطبيق الدقة على الكانفاس
        if (this.canvas) {
            this.canvas.width = this.gameWidth;
            this.canvas.height = this.gameHeight;

            // تحديث حجم الكانفاس في CSS أيض<|im_start|>
            this.canvas.style.width = this.gameWidth + 'px';
            this.canvas.style.height = this.gameHeight + 'px';
        }

        console.log(`🖥️ تم تطبيق دقة النافذة: ${this.gameWidth}x${this.gameHeight} (${resolution})`);
    }

    // تطبيق إعدادات الكاميرا من localStorage
    applyCameraSettings() {
        const settings = this.gameSettings;

        // تطبيق التكبير الافتراضي
        if (settings.defaultZoom) {
            this.camera.zoom = settings.defaultZoom;
            this.camera.targetZoom = settings.defaultZoom;
        }

        // تطبيق سرعة الكاميرا
        if (settings.cameraSpeed) {
            this.camera.smoothing = settings.cameraSpeed;
        }

        // تطبيق إعدادات المتابعة التلقائية (مفعلة افتراضياً)
        this.autoFollowEnabled = settings.autoFollowLargeFish !== false; // true إذا لم يتم تعطيلها صراحة
        this.autoFollowThreshold = settings.autoFollowThreshold || 20;

        // تأكيد التفعيل الافتراضي للمتابعة التلقائية
        if (settings.autoFollowLargeFish === undefined) {
            this.autoFollowEnabled = true; // مفعلة افتراضياً عند عدم وجود إعداد
            console.log('🎯 تم تفعيل متابعة الأسماك الكبيرة افتراضياً');
        }

        // رسالة تشخيصية لحالة المتابعة التلقائية
        console.log(`🔍 حالة المتابعة التلقائية: ${this.autoFollowEnabled ? 'مفعلة' : 'معطلة'} | الحد: ${this.autoFollowThreshold}`);
        console.log(`📊 إعدادات المتابعة: autoFollowLargeFish = ${settings.autoFollowLargeFish}`);

        // إضافة فحص دوري للمتابعة التلقائية كل 3 ثوان
        if (this.autoFollowEnabled) {
            setInterval(() => {
                this.checkAutoFollowLargeFish();
            }, 3000);
        }

        // تطبيق إعدادات عجلة الماوس
        this.mouseWheelEnabled = settings.mouseWheelZoom !== false;

        console.log(`🔍 تم تطبيق إعدادات الكاميرا: تكبير=${this.camera.zoom}x، سرعة=${this.camera.smoothing}، متابعة تلقائية=${this.autoFollowEnabled}`);
    }

    // تطبيق إعدادات إخفاء/إظهار القوائم
    applyUIVisibilitySettings() {
        const settings = this.gameSettings;

        // قائمة اللاعبين النشطين (players-panel)
        const activePlayersPanel = document.querySelector('.players-panel');
        if (activePlayersPanel) {
            activePlayersPanel.style.display = settings.showActivePlayers ? 'block' : 'none';
            console.log(`👥 قائمة اللاعبين النشطين: ${settings.showActivePlayers ? 'مرئية' : 'مخفية'}`);
        }

        // إحصائيات اللعبة (stats-panel)
        const gameStatsPanel = document.querySelector('.stats-panel');
        if (gameStatsPanel) {
            gameStatsPanel.style.display = settings.showGameStats ? 'block' : 'none';
            console.log(`📊 إحصائيات اللعبة: ${settings.showGameStats ? 'مرئية' : 'مخفية'}`);
        }

        // قائمة التحكم (controls-panel)
        const controlsPanel = document.querySelector('.controls-panel');
        if (controlsPanel) {
            controlsPanel.style.display = settings.showControls ? 'block' : 'none';
            console.log(`🎮 قائمة التحكم: ${settings.showControls ? 'مرئية' : 'مخفية'}`);
        }

        // قائمة الإنجازات (achievements-panel)
        const achievementsPanel = document.querySelector('.achievements-panel');
        if (achievementsPanel) {
            achievementsPanel.style.display = settings.showAchievements ? 'block' : 'none';
            console.log(`🏅 قائمة الإنجازات: ${settings.showAchievements ? 'مرئية' : 'مخفية'}`);
        }

        console.log('👁️ تم تطبيق إعدادات إخفاء/إظهار القوائم');
    }

    // بدء نظام الحذف التلقائي للأسماك
    startAutoDeleteSystem() {
        // تشغيل فحص الحذف التلقائي كل 5 ثوان
        setInterval(() => {
            this.checkAutoDeleteFish();
        }, 5000);

        console.log('🗑️ تم بدء نظام الحذف التلقائي للأسماك');
    }

    // فحص وحذف الأسماك القديمة
    checkAutoDeleteFish() {
        const settings = this.gameSettings;

        // التحقق من تفعيل الحذف التلقائي
        if (!settings.autoDeleteEnabled) {
            return;
        }

        const currentTime = Date.now();
        const lifetimeMs = (settings.fishLifetime || 60) * 1000; // تحويل إلى ميلي ثانية
        const maxFish = settings.maxFishLimit || 100;

        let deletedCount = 0;

        // حذف الأسماك القديمة
        this.fish = this.fish.filter(fish => {
            const age = currentTime - fish.createdAt;
            if (age > lifetimeMs) {
                deletedCount++;
                return false; // حذف السمكة
            }
            return true; // الاحتفاظ بالسمكة
        });

        // حذف الأسماك الزائدة إذا تجاوز العدد الحد الأقصى
        if (this.fish.length > maxFish) {
            const excessCount = this.fish.length - maxFish;
            // حذف الأسماك الأقدم أولاً
            this.fish.sort((a, b) => a.createdAt - b.createdAt);
            this.fish.splice(0, excessCount);
            deletedCount += excessCount;
        }

        if (deletedCount > 0) {
            console.log(`🗑️ تم حذف ${deletedCount} سمكة تلقائياً (العمر: ${settings.fishLifetime}ث، الحد الأقصى: ${maxFish})`);
        }
    }

    // توليد أصوات الأكل الواقعية
    generateEatingSound(size) {
        const audioContext = this.sounds.audioContext;
        const sampleRate = audioContext.sampleRate;
        const duration = size === 'large' ? 1.5 : size === 'medium' ? 0.8 : 0.4;
        const buffer = audioContext.createBuffer(1, sampleRate * duration, sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < buffer.length; i++) {
            const t = i / sampleRate;
            let sound = 0;

            if (size === 'large') {
                // صوت مضغ قوي وواقعي للأسماك الكبيرة
                sound += Math.sin(2 * Math.PI * 50 * t) * Math.exp(-t * 1.2) * 0.5;
                sound += Math.sin(2 * Math.PI * 100 * t) * Math.exp(-t * 1.8) * 0.4;

                // صوت تكسير العظام
                if (t > 0.2 && t < 1.0) {
                    const crunchIntensity = Math.exp(-(t - 0.6) * 6);
                    sound += (Math.random() - 0.5) * 0.3 * crunchIntensity;
                }

                // صوت بلع
                if (t > 1.0) {
                    sound += Math.sin(2 * Math.PI * 30 * t) * Math.exp(-(t - 1.0) * 8) * 0.3;
                }

            } else if (size === 'medium') {
                // صوت مضغ متوسط
                sound += Math.sin(2 * Math.PI * 120 * t) * Math.exp(-t * 2.5) * 0.4;
                sound += Math.sin(2 * Math.PI * 180 * t) * Math.exp(-t * 3) * 0.3;

                // صوت مضغ خفيف
                sound += (Math.random() - 0.5) * 0.15 * Math.exp(-t * 2);

            } else {
                // صوت أكل صغير وسريع
                sound += Math.sin(2 * Math.PI * 250 * t) * Math.exp(-t * 5) * 0.3;
                sound += (Math.random() - 0.5) * 0.1 * Math.exp(-t * 4);
            }

            data[i] = sound * 0.6;
        }

        return buffer;
    }

    // توليد أصوات مرعبة للأسماك الكبيرة
    generateScarySound(fishType) {
        const audioContext = this.sounds.audioContext;
        const sampleRate = audioContext.sampleRate;
        const duration = 2.5;
        const buffer = audioContext.createBuffer(1, sampleRate * duration, sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < buffer.length; i++) {
            const t = i / sampleRate;
            let sound = 0;

            switch (fishType) {
                case 'shark':
                    // زئير قرش مرعب ومخيف
                    sound += Math.sin(2 * Math.PI * 35 * t) * Math.exp(-t * 0.5) * 0.7;
                    sound += Math.sin(2 * Math.PI * 70 * t) * Math.exp(-t * 0.8) * 0.5;
                    sound += Math.sin(2 * Math.PI * 20 * t) * Math.exp(-t * 0.6) * 0.4;

                    // إضافة تشويه مرعب
                    if (t > 0.5 && t < 1.5) {
                        sound += Math.sin(2 * Math.PI * 45 * t) * Math.exp(-(t - 1.0) * 2) * 0.6;
                    }

                    // صوت أنفاس مخيفة
                    sound += (Math.random() - 0.5) * 0.2 * Math.exp(-t * 1.5);
                    break;

                case 'whale':
                    // نداء حوت عميق ومرعب
                    const whaleFreq = 15 + 8 * Math.sin(t * 2.5);
                    sound += Math.sin(2 * Math.PI * whaleFreq * t) * Math.exp(-t * 0.3) * 0.8;
                    sound += Math.sin(2 * Math.PI * whaleFreq * 2 * t) * Math.exp(-t * 0.5) * 0.4;

                    // صدى مخيف
                    if (t > 1.0) {
                        sound += Math.sin(2 * Math.PI * whaleFreq * 0.5 * t) * Math.exp(-(t - 1.0) * 1.5) * 0.3;
                    }
                    break;

                case 'eel':
                    // صوت كهربائي مرعب
                    const electricIntensity = Math.sin(t * 8) * 0.5 + 0.5;
                    sound += (Math.random() - 0.5) * electricIntensity * Math.exp(-t * 1.5) * 0.6;

                    // شرارات كهربائية
                    if (Math.random() < 0.1) {
                        sound += Math.sin(2 * Math.PI * (800 + Math.random() * 400) * t) * Math.exp(-t * 6) * 0.4;
                    }

                    // صوت طنين كهربائي
                    sound += Math.sin(2 * Math.PI * 120 * t) * electricIntensity * Math.exp(-t * 2) * 0.3;
                    break;

                case 'jellyfish':
                    // صوت لسعة قنديل البحر - صوت حاد ومؤلم
                    sound += Math.sin(2 * Math.PI * 1200 * t) * Math.exp(-t * 3) * 0.5;
                    sound += Math.sin(2 * Math.PI * 800 * t) * Math.exp(-t * 2) * 0.4;

                    // صوت سائل سام
                    const poisonWave = Math.sin(t * 6) * 0.3 + 0.7;
                    sound += (Math.random() - 0.5) * poisonWave * Math.exp(-t * 1.8) * 0.3;

                    // صوت تموج مائي مخيف
                    sound += Math.sin(2 * Math.PI * 200 * t * (1 + 0.3 * Math.sin(t * 4))) * Math.exp(-t * 1.2) * 0.4;
                    break;

                case 'octopus':
                    // صوت إمساك الأخطبوط - صوت عميق ومخيف
                    sound += Math.sin(2 * Math.PI * 50 * t) * Math.exp(-t * 0.8) * 0.6;
                    sound += Math.sin(2 * Math.PI * 100 * t) * Math.exp(-t * 1.2) * 0.5;

                    // صوت مصاصات الأذرع
                    if (t > 0.5 && t < 1.5) {
                        const suckingSound = Math.sin(2 * Math.PI * 150 * t) * Math.exp(-(t - 1.0) * 3) * 0.4;
                        sound += suckingSound;
                    }

                    // صوت حركة الأذرع في الماء
                    sound += (Math.random() - 0.5) * 0.2 * Math.exp(-t * 1.5);
                    break;

                case 'crab':
                    // صوت مخالب السرطان - صوت قطع وكسر
                    sound += Math.sin(2 * Math.PI * 800 * t) * Math.exp(-t * 4) * 0.5;
                    sound += Math.sin(2 * Math.PI * 400 * t) * Math.exp(-t * 2) * 0.4;

                    // صوت حركة الأرجل على الرمل
                    const scratchingNoise = (Math.random() - 0.5) * Math.exp(-t * 2) * 0.3;
                    sound += scratchingNoise;

                    // صوت قطع المخالب
                    if (t < 0.3) {
                        sound += Math.sin(2 * Math.PI * 1200 * t) * Math.exp(-t * 8) * 0.6;
                    }
                    break;

                case 'lobster':
                    // صوت مقص الكركند - صوت سحق قوي
                    sound += Math.sin(2 * Math.PI * 300 * t) * Math.exp(-t * 1.5) * 0.6;
                    sound += Math.sin(2 * Math.PI * 150 * t) * Math.exp(-t * 1) * 0.5;

                    // صوت السحق
                    if (t > 0.2 && t < 0.8) {
                        const crushSound = Math.sin(2 * Math.PI * 600 * t) * Math.exp(-(t - 0.5) * 4) * 0.7;
                        sound += crushSound;
                    }

                    // صوت فقاعات الماء
                    sound += (Math.random() - 0.5) * 0.15 * Math.exp(-t * 1.8);
                    break;

                case 'anglerfish':
                    // صوت سمكة الصياد - صوت مخيف وجذب
                    sound += Math.sin(2 * Math.PI * 80 * t) * Math.exp(-t * 0.6) * 0.7;
                    sound += Math.sin(2 * Math.PI * 160 * t) * Math.exp(-t * 1.2) * 0.5;

                    // صوت المصباح الكهربائي
                    const electricHum = Math.sin(2 * Math.PI * 60 * t) * Math.exp(-t * 2) * 0.3;
                    sound += electricHum;

                    // صوت فتح الفم المرعب
                    if (t > 0.5 && t < 1.2) {
                        sound += Math.sin(2 * Math.PI * 200 * t) * Math.exp(-(t - 0.8) * 3) * 0.6;
                    }
                    break;

                case 'swordfish':
                    // صوت سمكة السيف - صوت قطع سريع
                    sound += Math.sin(2 * Math.PI * 1500 * t) * Math.exp(-t * 6) * 0.6;
                    sound += Math.sin(2 * Math.PI * 800 * t) * Math.exp(-t * 3) * 0.4;

                    // صوت اختراق الماء بسرعة
                    const waterCut = Math.sin(2 * Math.PI * 2000 * t) * Math.exp(-t * 8) * 0.5;
                    sound += waterCut;

                    // صوت الرياح من السرعة
                    sound += (Math.random() - 0.5) * Math.exp(-t * 4) * 0.2;
                    break;

                case 'hammerhead':
                    // صوت القرش المطرقة - صوت ضربة قوية
                    sound += Math.sin(2 * Math.PI * 40 * t) * Math.exp(-t * 0.5) * 0.8;
                    sound += Math.sin(2 * Math.PI * 80 * t) * Math.exp(-t * 1) * 0.6;

                    // صوت الضربة بالمطرقة
                    if (t < 0.4) {
                        const hammerHit = Math.sin(2 * Math.PI * 300 * t) * Math.exp(-t * 5) * 0.8;
                        sound += hammerHit;
                    }

                    // صوت اهتزاز الماء
                    sound += Math.sin(2 * Math.PI * 120 * t * (1 + 0.1 * Math.sin(t * 10))) * Math.exp(-t * 1.5) * 0.4;
                    break;
            }

            data[i] = sound * 0.7;
        }

        return buffer;
    }

    // توليد صوت الظهور
    generateSpawnSound() {
        const audioContext = this.sounds.audioContext;
        const sampleRate = audioContext.sampleRate;
        const duration = 0.6;
        const buffer = audioContext.createBuffer(1, sampleRate * duration, sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < buffer.length; i++) {
            const t = i / sampleRate;
            const freq = 400 + t * 200; // تصاعد في التردد
            const sound = Math.sin(2 * Math.PI * freq * t) * Math.exp(-t * 2);
            data[i] = sound * 0.2;
        }

        return buffer;
    }

    // توليد صوت المتابعة
    generateFollowSound() {
        const audioContext = this.sounds.audioContext;
        const sampleRate = audioContext.sampleRate;
        const duration = 0.4;
        const buffer = audioContext.createBuffer(1, sampleRate * duration, sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < buffer.length; i++) {
            const t = i / sampleRate;
            const sound = Math.sin(2 * Math.PI * 600 * t) * Math.exp(-t * 5) +
                         Math.sin(2 * Math.PI * 800 * t) * Math.exp(-t * 3) * 0.5;
            data[i] = sound * 0.25;
        }

        return buffer;
    }

    // توليد صوت الإعجاب
    generateLikeSound() {
        const audioContext = this.sounds.audioContext;
        const sampleRate = audioContext.sampleRate;
        const duration = 0.3;
        const buffer = audioContext.createBuffer(1, sampleRate * duration, sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < buffer.length; i++) {
            const t = i / sampleRate;
            const sound = Math.sin(2 * Math.PI * 800 * t) * Math.exp(-t * 8) +
                         Math.sin(2 * Math.PI * 1200 * t) * Math.exp(-t * 6) * 0.3;
            data[i] = sound * 0.2;
        }

        return buffer;
    }

    // توليد أصوات المحيط المتقدمة
    generateAmbientSound(type) {
        const audioContext = this.sounds.audioContext;
        const sampleRate = audioContext.sampleRate;
        const duration = type === 'waves' ? 8 : type === 'deep' ? 12 : 6; // مدة أطول للأصوات المحيطة
        const length = sampleRate * duration;
        const buffer = audioContext.createBuffer(2, length, sampleRate); // ستيريو للمحيط

        for (let channel = 0; channel < 2; channel++) {
            const channelData = buffer.getChannelData(channel);

            for (let i = 0; i < length; i++) {
                const t = i / sampleRate;
                let sound = 0;

                switch (type) {
                    case 'waves':
                        // صوت أمواج البحر
                        const waveFreq = 0.3 + Math.sin(t * 0.1) * 0.1;
                        const waveNoise = (Math.random() - 0.5) * 0.4;
                        const wavePattern = Math.sin(2 * Math.PI * waveFreq * t) * 0.3;
                        sound = (waveNoise + wavePattern) * Math.sin(t * 0.5) * 0.6;

                        // إضافة صوت رغوة الأمواج
                        if (Math.random() < 0.02) {
                            sound += (Math.random() - 0.5) * 0.3;
                        }
                        break;

                    case 'bubbles':
                        // صوت فقاعات تحت الماء
                        if (Math.random() < 0.05) {
                            const bubbleFreq = 800 + Math.random() * 1200;
                            const bubbleDecay = Math.exp(-t * 8);
                            sound += Math.sin(2 * Math.PI * bubbleFreq * t) * bubbleDecay * 0.2;
                        }

                        // ضوضاء مائية خفيفة
                        sound += (Math.random() - 0.5) * 0.1;

                        // صوت تيار مائي
                        sound += Math.sin(2 * Math.PI * 60 * t) * 0.05;
                        break;

                    case 'deep':
                        // صوت أعماق البحر المخيف
                        const deepFreq = 20 + Math.sin(t * 0.2) * 10;
                        sound += Math.sin(2 * Math.PI * deepFreq * t) * 0.4;

                        // أصوات غامضة
                        if (Math.random() < 0.01) {
                            const mysteryFreq = 100 + Math.random() * 200;
                            sound += Math.sin(2 * Math.PI * mysteryFreq * t) * Math.exp(-t * 2) * 0.3;
                        }

                        // ضغط المياه
                        sound += (Math.random() - 0.5) * 0.15;
                        break;

                    case 'reef':
                        // صوت الشعاب المرجانية النشطة
                        if (Math.random() < 0.08) {
                            const reefFreq = 400 + Math.random() * 800;
                            sound += Math.sin(2 * Math.PI * reefFreq * t) * Math.exp(-t * 3) * 0.15;
                        }

                        // أصوات الحياة البحرية الصغيرة
                        sound += (Math.random() - 0.5) * 0.2;

                        // تيارات لطيفة
                        sound += Math.sin(2 * Math.PI * 0.5 * t) * 0.1;
                        break;

                    case 'current':
                        // صوت التيارات المائية
                        const currentNoise = (Math.random() - 0.5) * 0.5;
                        const currentFlow = Math.sin(2 * Math.PI * 0.8 * t) * 0.3;
                        sound = currentNoise + currentFlow;

                        // تدفق متغير
                        sound *= (1 + Math.sin(t * 0.3) * 0.3);
                        break;

                    case 'whale_song':
                        // أغنية الحيتان البعيدة
                        const whaleFreq = 200 + Math.sin(t * 0.5) * 100;
                        sound += Math.sin(2 * Math.PI * whaleFreq * t) * Math.exp(-Math.abs(t - 3) * 0.5) * 0.4;

                        // نغمات متداخلة
                        sound += Math.sin(2 * Math.PI * (whaleFreq * 1.5) * t) * Math.exp(-Math.abs(t - 4) * 0.3) * 0.2;
                        break;

                    case 'dolphin_clicks':
                        // أصوات الدلافين
                        if (Math.random() < 0.03) {
                            const clickFreq = 2000 + Math.random() * 3000;
                            sound += Math.sin(2 * Math.PI * clickFreq * t) * Math.exp(-t * 15) * 0.3;
                        }

                        // صفير الدلافين
                        if (Math.random() < 0.01) {
                            const whistleFreq = 1000 + Math.random() * 2000;
                            sound += Math.sin(2 * Math.PI * whistleFreq * t) * Math.exp(-t * 2) * 0.2;
                        }
                        break;

                    default:
                        // صوت محيط افتراضي
                        sound += Math.sin(2 * Math.PI * 0.1 * t) * 0.1;
                        sound += Math.sin(2 * Math.PI * 0.05 * t) * 0.05;
                        if (Math.random() < 0.001) {
                            sound += Math.sin(2 * Math.PI * (200 + Math.random() * 400) * t) * 0.02;
                        }
                        break;
                }

                // تطبيق تأثير ستيريو
                const stereoEffect = channel === 0 ? 1 - Math.sin(t * 0.1) * 0.2 : 1 + Math.sin(t * 0.1) * 0.2;
                channelData[i] = sound * stereoEffect * 0.1;
            }
        }

        return buffer;
    }

    // تشغيل الصوت
    playSound(soundName, volume = 1) {
        if (!this.sounds.enabled || !this.sounds.audioContext || !this.gameSettings.soundEnabled) {
            return;
        }

        // إذا كان هناك أي أصوات مخصصة، تشغيل المخصصة فقط
        if (this.customSoundBuffers && this.customSoundBuffers.size > 0) {
            // تشغيل فقط إذا كان هذا الصوت مخصص
            if (this.customSoundBuffers.has(soundName)) {
                const buffer = this.customSoundBuffers.get(soundName);
                try {
                    const source = this.sounds.audioContext.createBufferSource();
                    const gainNode = this.sounds.audioContext.createGain();

                    source.buffer = buffer;
                    gainNode.gain.value = volume * this.sounds.volume * this.gameSettings.effectsVolume;

                    source.connect(gainNode);
                    gainNode.connect(this.sounds.audioContext.destination);

                    source.start();
                    console.log(`🎵 تشغيل صوت مخصص فقط: ${soundName}`);
                } catch (error) {
                    console.error(`❌ فشل في تشغيل الصوت المخصص ${soundName}:`, error);
                }
            } else {
                // لا تشغل أي صوت افتراضي إذا كان هناك أصوات مخصصة
                console.log(`🔇 تم إغلاق الصوت الافتراضي: ${soundName} (يوجد أصوات مخصصة)`);
            }
            return;
        }

        // لا توجد أصوات افتراضية - تم حذفها نهائياً
        console.log(`🔇 لا يوجد صوت مخصص لـ: ${soundName} - صمت كامل (تم حذف الأصوات الافتراضية)`);
    }

    // التحقق من وجود صوت مخصص
    hasCustomSound(soundName) {
        return this.customSoundBuffers && this.customSoundBuffers.has(soundName);
    }

    // التحقق من وجود أي أصوات مخصصة في النظام
    hasAnyCustomSounds() {
        return this.customSoundBuffers && this.customSoundBuffers.size > 0;
    }

    // بدء أصوات المحيط
    startAmbientSounds() {
        if (!this.gameSettings.soundEnabled || !this.sounds.enabled || !this.gameSettings.ambientSounds) {
            return;
        }

        console.log('🌊 بدء أصوات المحيط...');

        // تشغيل أصوات المحيط المختلفة بشكل دوري
        this.ambientSoundTimers = {
            waves: null,
            bubbles: null,
            deep: null,
            reef: null,
            current: null,
            whale_song: null,
            dolphin_clicks: null
        };

        // أمواج البحر - كل 8 ثوان
        this.ambientSoundTimers.waves = setInterval(() => {
            if (this.gameSettings.soundEnabled && Math.random() < 0.7) {
                this.playAmbientSound('ocean_waves', 0.3);
            }
        }, 8000);

        // فقاعات - كل 5 ثوان
        this.ambientSoundTimers.bubbles = setInterval(() => {
            if (this.gameSettings.soundEnabled && Math.random() < 0.8) {
                this.playAmbientSound('underwater_bubbles', 0.2);
            }
        }, 5000);

        // أعماق البحر - كل 15 ثانية
        this.ambientSoundTimers.deep = setInterval(() => {
            if (this.gameSettings.soundEnabled && Math.random() < 0.4) {
                this.playAmbientSound('deep_sea', 0.25);
            }
        }, 15000);

        // الشعاب المرجانية - كل 12 ثانية
        this.ambientSoundTimers.reef = setInterval(() => {
            if (this.gameSettings.soundEnabled && Math.random() < 0.5) {
                this.playAmbientSound('coral_reef', 0.15);
            }
        }, 12000);

        // التيارات المائية - كل 10 ثوان
        this.ambientSoundTimers.current = setInterval(() => {
            if (this.gameSettings.soundEnabled && Math.random() < 0.6) {
                this.playAmbientSound('sea_current', 0.2);
            }
        }, 10000);

        // أغنية الحيتان - كل 30 ثانية
        this.ambientSoundTimers.whale_song = setInterval(() => {
            if (this.gameSettings.soundEnabled && Math.random() < 0.3) {
                this.playAmbientSound('whale_song', 0.4);
            }
        }, 30000);

        // أصوات الدلافين - كل 20 ثانية
        this.ambientSoundTimers.dolphin_clicks = setInterval(() => {
            if (this.gameSettings.soundEnabled && Math.random() < 0.4) {
                this.playAmbientSound('dolphin_clicks', 0.3);
            }
        }, 20000);

        // تشغيل فوري لبعض الأصوات
        setTimeout(() => {
            this.playAmbientSound('ocean_waves', 0.3);
        }, 2000);

        setTimeout(() => {
            this.playAmbientSound('underwater_bubbles', 0.2);
        }, 4000);

        console.log('✅ تم بدء أصوات المحيط بنجاح');
    }

    // تشغيل صوت محيط محدد (الأصوات المخصصة فقط)
    playAmbientSound(soundName, volume = 0.3) {
        if (!this.sounds.enabled || !this.sounds.audioContext || !this.gameSettings.soundEnabled) {
            return;
        }

        console.log(`🎯 محاولة تشغيل صوت محيط مخصص: ${soundName}`);

        // تشغيل الأصوات المخصصة فقط
        if (this.customSoundBuffers && this.customSoundBuffers.has(soundName)) {
            const buffer = this.customSoundBuffers.get(soundName);
            try {
                const source = this.sounds.audioContext.createBufferSource();
                const gainNode = this.sounds.audioContext.createGain();

                source.buffer = buffer;
                gainNode.gain.value = volume * this.sounds.volume * this.gameSettings.ambientVolume;

                source.connect(gainNode);
                gainNode.connect(this.sounds.audioContext.destination);

                source.start(0);

                // تنظيف المراجع بعد انتهاء الصوت
                source.onended = () => {
                    source.disconnect();
                    gainNode.disconnect();
                    console.log(`✅ انتهى تشغيل صوت المحيط المخصص: ${soundName}`);
                };

                console.log(`🎵 تم بدء تشغيل صوت محيط مخصص: ${soundName}`);
            } catch (error) {
                console.error(`❌ فشل في تشغيل الصوت المحيط المخصص ${soundName}:`, error);
            }
        } else {
            // لا توجد أصوات افتراضية - صمت كامل
            console.log(`🔇 لا يوجد صوت محيط مخصص لـ: ${soundName} - صمت كامل`);
        }
    }

    // إيقاف أصوات المحيط
    stopAmbientSounds() {
        if (this.ambientSoundTimers) {
            Object.values(this.ambientSoundTimers).forEach(timer => {
                if (timer) clearInterval(timer);
            });
            this.ambientSoundTimers = null;
            console.log('🔇 تم إيقاف أصوات المحيط');
        }
    }

    // تشغيل أصوات المعارك الجديدة
    playBattleSound() {
        // أصوات بداية المعركة
        const battleSounds = ['battle_start', 'clash', 'fight_begin'];
        const randomSound = battleSounds[Math.floor(Math.random() * battleSounds.length)];

        // إذا لم يكن هناك صوت مخصص، توليد صوت ديناميكي
        if (!this.customSoundBuffers || !this.customSoundBuffers.has(randomSound)) {
            this.generateAndPlayBattleSound();
        } else {
            this.playSound(randomSound, 0.6);
        }
    }

    playAttackSound(attackerType, damage) {
        // أصوات الهجوم حسب نوع السمكة
        const attackSounds = {
            'shark': 'shark_attack',
            'whale': 'whale_slam',
            'swordfish': 'sword_strike',
            'hammerhead': 'hammer_smash',
            'octopus': 'tentacle_whip',
            'crab': 'claw_snap',
            'lobster': 'pincer_crush',
            'anglerfish': 'bite_crunch',
            'default': 'attack_hit'
        };

        const soundName = attackSounds[attackerType] || attackSounds['default'];
        const volume = Math.min(0.8, 0.3 + (damage / 50)); // حجم الصوت يعتمد على الضرر

        // إذا لم يكن هناك صوت مخصص، توليد صوت ديناميكي
        if (!this.customSoundBuffers || !this.customSoundBuffers.has(soundName)) {
            this.generateAndPlayAttackSound(attackerType, damage);
        } else {
            this.playSound(soundName, volume);
        }
    }

    // توليد وتشغيل صوت معركة ديناميكي
    generateAndPlayBattleSound() {
        if (!this.sounds.audioContext) return;

        const audioContext = this.sounds.audioContext;
        const sampleRate = audioContext.sampleRate;
        const duration = 1.2;
        const buffer = audioContext.createBuffer(1, sampleRate * duration, sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < buffer.length; i++) {
            const t = i / sampleRate;
            let sound = 0;

            // صوت صدام معدني
            sound += Math.sin(2 * Math.PI * 150 * t) * Math.exp(-t * 3) * 0.6;
            sound += Math.sin(2 * Math.PI * 300 * t) * Math.exp(-t * 4) * 0.4;

            // إضافة ضوضاء للواقعية
            sound += (Math.random() - 0.5) * 0.3 * Math.exp(-t * 2);

            // صدى
            if (t > 0.3) {
                sound += Math.sin(2 * Math.PI * 100 * (t - 0.3)) * Math.exp(-(t - 0.3) * 5) * 0.2;
            }

            data[i] = sound * 0.5;
        }

        // تشغيل الصوت المولد
        try {
            const source = audioContext.createBufferSource();
            const gainNode = audioContext.createGain();

            source.buffer = buffer;
            gainNode.gain.value = 0.6 * this.sounds.volume * this.gameSettings.effectsVolume;

            source.connect(gainNode);
            gainNode.connect(audioContext.destination);
            source.start();
        } catch (error) {
            console.warn('فشل في تشغيل صوت المعركة المولد:', error);
        }
    }

    // توليد وتشغيل صوت هجوم ديناميكي
    generateAndPlayAttackSound(attackerType, damage) {
        if (!this.sounds.audioContext) return;

        const audioContext = this.sounds.audioContext;
        const sampleRate = audioContext.sampleRate;
        const duration = 0.4 + (damage / 100); // مدة أطول للضرر الأكبر
        const buffer = audioContext.createBuffer(1, sampleRate * duration, sampleRate);
        const data = buffer.getChannelData(0);

        for (let i = 0; i < buffer.length; i++) {
            const t = i / sampleRate;
            let sound = 0;

            // أصوات مختلفة حسب نوع المهاجم
            switch (attackerType) {
                case 'shark':
                case 'hammerhead':
                    // عضة قوية
                    sound += Math.sin(2 * Math.PI * 80 * t) * Math.exp(-t * 5) * 0.7;
                    sound += Math.sin(2 * Math.PI * 160 * t) * Math.exp(-t * 6) * 0.5;
                    break;

                case 'swordfish':
                    // طعنة سريعة
                    sound += Math.sin(2 * Math.PI * 400 * t) * Math.exp(-t * 8) * 0.6;
                    sound += Math.sin(2 * Math.PI * 800 * t) * Math.exp(-t * 10) * 0.4;
                    break;

                case 'octopus':
                    // ضربة مجسات
                    sound += Math.sin(2 * Math.PI * 200 * t) * Math.exp(-t * 4) * 0.5;
                    sound += (Math.random() - 0.5) * 0.4 * Math.exp(-t * 3);
                    break;

                case 'crab':
                case 'lobster':
                    // قرصة مخالب
                    sound += Math.sin(2 * Math.PI * 300 * t) * Math.exp(-t * 6) * 0.6;
                    sound += Math.sin(2 * Math.PI * 150 * t) * Math.exp(-t * 4) * 0.4;
                    break;

                default:
                    // هجوم عادي
                    sound += Math.sin(2 * Math.PI * 250 * t) * Math.exp(-t * 5) * 0.5;
                    sound += (Math.random() - 0.5) * 0.2 * Math.exp(-t * 4);
                    break;
            }

            // تكثيف الصوت حسب قوة الضرر
            const damageMultiplier = 1 + (damage / 50);
            data[i] = sound * damageMultiplier * 0.4;
        }

        // تشغيل الصوت المولد
        try {
            const source = audioContext.createBufferSource();
            const gainNode = audioContext.createGain();

            source.buffer = buffer;
            const volume = Math.min(0.8, 0.3 + (damage / 50));
            gainNode.gain.value = volume * this.sounds.volume * this.gameSettings.effectsVolume;

            source.connect(gainNode);
            gainNode.connect(audioContext.destination);
            source.start();
        } catch (error) {
            console.warn('فشل في تشغيل صوت الهجوم المولد:', error);
        }
    }

    // تشغيل صوت الأكل
    playEatingSound(fishType, fishSize) {
        if (!this.gameSettings.realisticEating) return;

        let soundName;

        if (fishSize > 60) {
            soundName = 'eat_large';
        } else if (fishSize > 30) {
            soundName = 'eat_medium';
        } else {
            soundName = 'eat_small';
        }

        this.playSound(soundName);

        // تشغيل صوت مرعب للأسماك الكبيرة
        const scaryTypes = ['shark', 'whale', 'eel', 'jellyfish', 'octopus', 'crab', 'lobster', 'anglerfish', 'swordfish', 'hammerhead'];
        if (this.gameSettings.scaryEnabled && scaryTypes.includes(fishType) && fishSize > 50) {
            setTimeout(() => {
                const soundMap = {
                    'shark': 'shark_roar',
                    'whale': 'whale_call',
                    'eel': 'eel_electric',
                    'jellyfish': 'jellyfish_sting',
                    'octopus': 'octopus_grab',
                    'crab': 'crab_claws',
                    'lobster': 'lobster_crush',
                    'anglerfish': 'anglerfish_lure',
                    'swordfish': 'swordfish_strike',
                    'hammerhead': 'hammerhead_smash'
                };
                const scarySound = soundMap[fishType] || 'shark_roar';
                this.playSound(scarySound, 0.8);
            }, 300);
        }
    }

    // تشغيل صوت مرعب عند ظهور سمكة قوية
    playScarySpawnSound(fishType, fishSize) {
        if (!this.gameSettings.scaryEnabled) return;

        // تشغيل صوت مرعب للأسماك القوية عند الظهور
        const isLargeFish = fishSize > 40;
        const scaryTypes = ['shark', 'whale', 'eel', 'jellyfish', 'octopus', 'crab', 'lobster', 'anglerfish', 'swordfish', 'hammerhead'];
        const isPowerfulType = scaryTypes.includes(fishType);

        if (isLargeFish && isPowerfulType) {
            setTimeout(() => {
                const soundMap = {
                    'shark': 'shark_roar',
                    'whale': 'whale_call',
                    'eel': 'eel_electric',
                    'jellyfish': 'jellyfish_sting',
                    'octopus': 'octopus_grab',
                    'crab': 'crab_claws',
                    'lobster': 'lobster_crush',
                    'anglerfish': 'anglerfish_lure',
                    'swordfish': 'swordfish_strike',
                    'hammerhead': 'hammerhead_smash'
                };
                const scarySound = soundMap[fishType] || 'shark_roar';
                this.playSound(scarySound, 0.6);

                console.log(`👹 صوت مرعب: ${fishType} ظهر بحجم ${fishSize.toFixed(1)}`);
            }, 500);
        }
    }

    async init() {
        console.log('🐟 بدء تحميل Fish Eat Fish...');

        // إظهار شاشة التحميل
        this.showLoadingScreen();

        // تحميل الموارد
        await this.loadResources();

        // تهيئة نظام الصوت
        await this.initAudioSystem();

        // إعداد الأحداث
        this.setupEventListeners();

        // إعداد البيئة
        this.setupEnvironment();

        // اتصال Socket.IO (إذا لم يكن وضع التجربة)
        if (!this.isDemo) {
            this.setupSocketConnection();
        } else {
            this.setupDemoMode();
        }

        // بدء اللعبة
        this.startGame();

        // بدء أصوات المحيط
        this.startAmbientSounds();

        // بدء نظام الحذف التلقائي للأسماك
        this.startAutoDeleteSystem();

        console.log('✅ تم تحميل Fish Eat Fish بنجاح!');
    }

    showLoadingScreen() {
        const loadingBar = document.getElementById('loadingBar');
        let progress = 0;

        const updateProgress = () => {
            progress += Math.random() * 15;
            if (progress > 100) progress = 100;

            loadingBar.style.width = progress + '%';

            if (progress < 100) {
                setTimeout(updateProgress, 100 + Math.random() * 200);
            } else {
                setTimeout(() => {
                    document.getElementById('loadingScreen').style.display = 'none';
                }, 500);
            }
        };

        updateProgress();
    }

    async loadResources() {
        // محاكاة تحميل الموارد
        return new Promise(resolve => {
            setTimeout(resolve, 1500);
        });
    }

    setupEventListeners() {
        // أحداث الماوس
        this.canvas.addEventListener('mousemove', (e) => {
            const rect = this.canvas.getBoundingClientRect();
            this.mouse.x = e.clientX - rect.left;
            this.mouse.y = e.clientY - rect.top;
        });

        this.canvas.addEventListener('mousedown', (e) => {
            this.mouse.isDown = true;
        });

        this.canvas.addEventListener('mouseup', (e) => {
            this.mouse.isDown = false;
        });

        // إضافة تحكم بالتكبير بعجلة الماوس (حسب الإعدادات)
        this.canvas.addEventListener('wheel', (e) => {
            if (this.mouseWheelEnabled !== false) {
                e.preventDefault();
                const zoomFactor = e.deltaY > 0 ? 0.9 : 1.1;
                const newZoom = this.camera.targetZoom * zoomFactor;
                this.setZoom(newZoom);
            }
        });

        // أحداث اللمس للجوال
        this.canvas.addEventListener('touchstart', (e) => {
            e.preventDefault();
            const rect = this.canvas.getBoundingClientRect();
            const touch = e.touches[0];
            this.touch.x = touch.clientX - rect.left;
            this.touch.y = touch.clientY - rect.top;
            this.touch.isActive = true;
        });

        this.canvas.addEventListener('touchmove', (e) => {
            e.preventDefault();
            const rect = this.canvas.getBoundingClientRect();
            const touch = e.touches[0];
            this.touch.x = touch.clientX - rect.left;
            this.touch.y = touch.clientY - rect.top;
        });

        this.canvas.addEventListener('touchend', (e) => {
            e.preventDefault();
            this.touch.isActive = false;
        });

        // تغيير حجم النافذة
        window.addEventListener('resize', () => {
            // تحديث الدقة إذا كانت في وضع تلقائي
            if (this.gameSettings.gameResolution === 'auto') {
                this.applyGameResolution();
            } else {
                this.resizeCanvas();
            }
        });

        // منع القائمة السياقية
        this.canvas.addEventListener('contextmenu', (e) => {
            e.preventDefault();
        });
    }

    generatePlayerColors() {
        const colors = [];
        for (let i = 0; i < this.maxPlayers; i++) {
            const hue = (i * 137.508) % 360; // Golden angle for even distribution
            colors.push(`hsl(${hue}, 70%, 60%)`);
        }
        return colors;
    }

    setupEnvironment() {
        // إنشاء أشعة الضوء المحسنة
        for (let i = 0; i < 8; i++) {
            this.environment.lightRays.push({
                x: Math.random() * this.gameWidth,
                y: 0,
                width: 15 + Math.random() * 50,
                height: this.gameHeight,
                opacity: 0.05 + Math.random() * 0.15,
                speed: 0.3 + Math.random() * 1.2,
                angle: Math.random() * 0.2 - 0.1
            });
        }

        // إنشاء الفقاعات المحسنة
        for (let i = 0; i < 30; i++) {
            this.createBubble();
        }

        // إنشاء الأعشاب البحرية المتنوعة - في الأرضية
        for (let i = 0; i < 15; i++) {
            this.environment.seaweed.push({
                x: Math.random() * this.gameWidth,
                y: this.gameHeight - 5, // في الأرضية تماماً
                height: 40 + Math.random() * 120,
                sway: Math.random() * Math.PI * 2,
                swaySpeed: 0.015 + Math.random() * 0.025,
                type: Math.random() > 0.5 ? 'kelp' : 'coral',
                color: Math.random() > 0.7 ? '#FF6B6B' : '#2E8B57'
            });
        }

        // إنشاء الشعاب المرجانية - في الأرضية
        this.environment.corals = [];
        for (let i = 0; i < 10; i++) {
            this.environment.corals.push({
                x: Math.random() * this.gameWidth,
                y: this.gameHeight - 10, // في الأرضية تماماً
                width: 30 + Math.random() * 70,
                height: 20 + Math.random() * 50,
                color: this.getRandomCoralColor(),
                type: Math.floor(Math.random() * 3),
                sway: Math.random() * Math.PI * 2
            });
        }

        // إنشاء الصخور والكهوف - في الأرضية
        this.environment.rocks = [];
        for (let i = 0; i < 8; i++) {
            this.environment.rocks.push({
                x: Math.random() * this.gameWidth,
                y: this.gameHeight - 15, // في الأرضية تماماً
                width: 50 + Math.random() * 100,
                height: 30 + Math.random() * 60,
                color: '#696969',
                type: Math.floor(Math.random() * 2)
            });
        }

        // إنشاء الكنوز المدفونة - في الأرضية
        this.environment.treasures = [];
        for (let i = 0; i < 6; i++) {
            this.environment.treasures.push({
                x: Math.random() * this.gameWidth,
                y: this.gameHeight - 25, // في الأرضية تماماً
                size: 15 + Math.random() * 25,
                glow: Math.random() * Math.PI * 2,
                glowSpeed: 0.05 + Math.random() * 0.03,
                discovered: false
            });
        }

        // إنشاء الرمال المتحركة - في الأرضية
        this.environment.sandParticles = [];
        for (let i = 0; i < 80; i++) {
            this.environment.sandParticles.push({
                x: Math.random() * this.gameWidth,
                y: this.gameHeight - Math.random() * 30, // في الأرضية السفلى
                size: 1 + Math.random() * 2,
                speed: 0.1 + Math.random() * 0.2,
                opacity: 0.2 + Math.random() * 0.3
            });
        }
    }

    getRandomCoralColor() {
        const coralColors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
            '#FFEAA7', '#DDA0DD', '#F0A500', '#FF7675'
        ];
        return coralColors[Math.floor(Math.random() * coralColors.length)];
    }

    setupSocketConnection() {
        this.socket = io();

        this.socket.on('connect', () => {
            console.log('🔌 متصل بالخادم');
            this.socket.emit('joinGameRoom', 'fish-eat-fish');
        });

        this.socket.on('disconnect', () => {
            console.log('❌ انقطع الاتصال بالخادم');
        });

        // استقبال الهدايا (الورود)
        this.socket.on('gift', (data) => {
            this.handleGiftReceived(data);
        });

        // استقبال التعليقات
        this.socket.on('chat', (data) => {
            this.handleCommentReceived(data);
        });

        // استقبال الإعجابات
        this.socket.on('like', (data) => {
            this.handleLikeReceived(data);
        });

        // استقبال المتابعات
        this.socket.on('follow', (data) => {
            this.handleFollowReceived(data);
        });

        // استقبال الانضمام
        this.socket.on('member', (data) => {
            this.handleMemberJoined(data);
        });
    }

    setupDemoMode() {
        console.log('🧪 تشغيل وضع التجربة المحلي');

        // إنشاء لوحة التجربة
        this.createDemoPanel();

        // إضافة بعض الأسماك التجريبية
        setTimeout(() => {
            this.addDemoFish();
        }, 2000);
    }

    createDemoPanel() {
        const demoPanel = document.createElement('div');
        demoPanel.className = 'demo-panel';
        demoPanel.id = 'demoPanel';

        demoPanel.innerHTML = `
            <div class="demo-header" style="cursor: move; padding: 10px; margin: -20px -20px 15px -20px; background: rgba(231, 76, 60, 0.3); border-radius: 15px 15px 0 0;">
                <button class="close-demo" onclick="window.close()" style="position: absolute; top: 5px; right: 10px;">❌</button>
                <h2 style="margin: 0; font-size: 18px;">🧪 وضع التجربة المحلي الكامل</h2>
                <p style="margin: 5px 0 0 0; font-size: 12px; opacity: 0.9;">اختبر جميع مميزات اللعبة مع دعم 100+ لاعب وأنواع أسماك مختلفة</p>
            </div>

            <div class="demo-controls">
                <button class="demo-btn" onclick="window.game.simulateRose(1)">🌹 وردة واحدة</button>
                <button class="demo-btn" onclick="window.game.simulateRose(5)">🌹 5 ورود</button>
                <button class="demo-btn" onclick="window.game.simulateRose(10)">🌹 10 ورود</button>
                <button class="demo-btn" onclick="window.game.simulateRose(25)">🌹 25 وردة</button>
                <button class="demo-btn" onclick="window.game.simulateRose(50)">🌹 50 وردة</button>
                <button class="demo-btn" onclick="window.game.simulateRose(100)">🌹 100 وردة</button>

                <button class="demo-btn" onclick="window.game.simulateComment('هجوم')">💬 تعليق هجوم</button>
                <button class="demo-btn" onclick="window.game.simulateComment('سرعة')">💬 تعليق سرعة</button>
                <button class="demo-btn" onclick="window.game.simulateComment('متوحش')">💬 تعليق متوحش</button>

                <button class="demo-btn" onclick="window.game.simulateLikes(25)">❤️ 25 إعجاب</button>
                <button class="demo-btn" onclick="window.game.simulateJoin()">👋 انضمام</button>
                <button class="demo-btn" onclick="window.game.simulateFollow()">🔔 متابعة</button>
                <button class="demo-btn" onclick="window.game.simulateShare()">📤 مشاركة</button>

                <button class="demo-btn" onclick="window.game.addRandomPlayers(10)">👥 10 لاعبين</button>
                <button class="demo-btn" onclick="window.game.addRandomPlayers(25)">👥 25 لاعب</button>
                <button class="demo-btn" onclick="window.game.addRandomPlayers(50)">👥 50 لاعب</button>
                <button class="demo-btn" onclick="window.game.addRandomPlayers(100)">👥 100 لاعب</button>

                <button class="demo-btn" onclick="window.game.createSpecificFish('shark')">🦈 قرش</button>
                <button class="demo-btn" onclick="window.game.createSpecificFish('whale')">🐋 حوت</button>
                <button class="demo-btn" onclick="window.game.createSpecificFish('octopus')">🐙 أخطبوط</button>
                <button class="demo-btn" onclick="window.game.createSpecificFish('jellyfish')">🎐 قنديل</button>
                <button class="demo-btn" onclick="window.game.createSpecificFish('seahorse')">🐴 حصان البحر</button>
                <button class="demo-btn" onclick="window.game.createSpecificFish('turtle')">🐢 سلحفاة</button>
                <button class="demo-btn" onclick="window.game.createSpecificFish('crab')">🦀 سرطان</button>
                <button class="demo-btn" onclick="window.game.createSpecificFish('lobster')">🦞 كركند</button>
                <button class="demo-btn" onclick="window.game.createSpecificFish('starfish')">⭐ نجمة البحر</button>
                <button class="demo-btn" onclick="window.game.createSpecificFish('anglerfish')">🎣 سمكة الصياد</button>
                <button class="demo-btn" onclick="window.game.createSpecificFish('swordfish')">⚔️ سمكة السيف</button>
                <button class="demo-btn" onclick="window.game.createSpecificFish('hammerhead')">🔨 القرش المطرقة</button>

                <button class="demo-btn" onclick="window.game.testAllGifts()">🎁 اختبار جميع الهدايا</button>
                <button class="demo-btn" onclick="window.game.testGiftAssignments()">⚙️ اختبار التعيينات</button>
                <button class="demo-btn" onclick="window.game.testAllCreatures()">🐟 اختبار جميع المخلوقات</button>
                <button class="demo-btn" onclick="window.game.testScarySound()">🔊 اختبار الأصوات</button>
                <button class="demo-btn" onclick="window.game.testAmbientSounds()">🌊 اختبار أصوات المحيط</button>
                <button class="demo-btn" onclick="window.game.testCustomSounds()">🎵 اختبار الأصوات المخصصة</button>
                <button class="demo-btn" onclick="window.game.testSpecialEffects()">✨ اختبار التأثيرات</button>
                <button class="demo-btn" onclick="window.game.startFishBattle()">⚔️ بدء معركة</button>

                <div style="margin: 10px 0; padding: 10px; background: rgba(0,0,0,0.3); border-radius: 5px;">
                    <h5 style="color: #00ff7f; margin: 0 0 8px 0;">🔍 التحكم في الكاميرا:</h5>
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 5px; margin-bottom: 8px;">
                        <button class="demo-btn" onclick="window.game.setZoom(1.5)">🔍- تكبير أقل</button>
                        <button class="demo-btn" onclick="window.game.setZoom(2.0)">🔍 تكبير عادي</button>
                        <button class="demo-btn" onclick="window.game.setZoom(3.0)">🔍+ تكبير أكثر</button>
                        <button class="demo-btn" onclick="window.game.setZoom(4.0)">🔍++ تكبير أقصى</button>
                    </div>
                    <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 5px;">
                        <button class="demo-btn" onclick="window.game.followRandomFish()">👁️ متابعة عشوائية</button>
                        <button class="demo-btn" onclick="window.game.followLargestFish()">🔍 متابعة الأكبر</button>
                        <button class="demo-btn" onclick="window.game.stopFollowing()">🎯 إلغاء المتابعة</button>
                        <button class="demo-btn" onclick="window.game.centerCamera()">🏠 توسيط الكاميرا</button>
                    </div>
                    <p style="font-size: 10px; color: #aaa; margin: 5px 0 0 0;">💡 استخدم عجلة الماوس للتكبير/التصغير</p>
                </div>

                <button class="demo-btn danger" onclick="window.game.clearAllFish()">🗑️ مسح الكل</button>
                <button class="demo-btn success" onclick="window.game.resetDemo()">🔄 إعادة تشغيل</button>
            </div>

            <div style="margin-top: 15px; padding: 10px; background: rgba(0,0,0,0.3); border-radius: 8px;">
                <h4 style="color: #00ff7f; margin: 0 0 10px 0;">📊 إحصائيات التجربة:</h4>
                <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; font-size: 11px;">
                    <div>🐟 أسماك: <span id="demoFishCount">0</span></div>
                    <div>👥 لاعبين: <span id="demoPlayerCount">0</span></div>
                    <div>⚔️ معارك: <span id="demoBattleCount">0</span></div>
                    <div>🔥 نشطة: <span id="demoActiveBattles">0</span></div>
                    <div>💀 وفيات: <span id="demoTotalDeaths">0</span></div>
                    <div>🌹 ورود: <span id="demoRoseCount">0</span></div>
                    <div>🎮 أنواع: <span id="demoFishTypes">0</span></div>
                    <div>🏆 إنجازات: <span id="demoAchievements">0</span></div>
                    <div>🔍 تكبير: <span id="demoZoomLevel">2.0x</span></div>
                    <div>👁️ متابعة: <span id="demoFollowTarget">لا شيء</span></div>
                    <div>📏 أكبر سمكة: <span id="demoLargestFish">0</span></div>
                    <div>🎯 FPS: <span id="demoFPS">60/60</span></div>
                    <div>🖥️ دقة: <span id="demoResolution">1920x1080</span></div>
                    <div>⚡ أداء: <span id="demoPerformance">ممتاز</span></div>
                </div>
            </div>

            <p style="font-size: 12px; color: #cccccc; margin-top: 15px;">
                💡 نصيحة: جرب إضافة 100 لاعب ومراقبة الأداء والتفاعلات المعقدة
            </p>
        `;

        document.body.appendChild(demoPanel);

        // إضافة السحب للوحة التجربة
        setTimeout(() => {
            const header = demoPanel.querySelector('.demo-header');
            if (header) {
                header.addEventListener('mousedown', (e) => {
                    if (e.target.classList.contains('close-demo')) return;
                    this.startDemoDrag(e, demoPanel);
                });
                header.addEventListener('touchstart', (e) => {
                    if (e.target.classList.contains('close-demo')) return;
                    this.startDemoDrag(e, demoPanel);
                });
            }
        }, 100);

        // تحديث إحصائيات التجربة كل ثانية
        this.demoStatsInterval = setInterval(() => {
            this.updateDemoStats();
        }, 1000);

        // إخفاء اللوحة بعد 15 ثانية
        setTimeout(() => {
            if (demoPanel.style.display !== 'none') {
                demoPanel.style.opacity = '0.8';
            }
        }, 15000);
    }

    startDemoDrag(e, panel) {
        e.preventDefault();
        e.stopPropagation();

        this.isDemoDragging = true;
        this.currentDemoPanel = panel;

        const clientX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
        const clientY = e.type === 'touchstart' ? e.touches[0].clientY : e.clientY;

        const rect = panel.getBoundingClientRect();
        this.demoDragOffset = {
            x: clientX - rect.left,
            y: clientY - rect.top
        };

        panel.classList.add('dragging');
        panel.style.transition = 'none';
        panel.style.zIndex = '1001';

        // إضافة مستمعي الأحداث
        document.addEventListener('mousemove', this.handleDemoDrag.bind(this));
        document.addEventListener('touchmove', this.handleDemoDrag.bind(this));
        document.addEventListener('mouseup', this.stopDemoDrag.bind(this));
        document.addEventListener('touchend', this.stopDemoDrag.bind(this));
    }

    handleDemoDrag(e) {
        if (!this.isDemoDragging || !this.currentDemoPanel) return;

        e.preventDefault();
        e.stopPropagation();

        const clientX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX;
        const clientY = e.type === 'touchmove' ? e.touches[0].clientY : e.clientY;

        let newLeft = clientX - this.demoDragOffset.x;
        let newTop = clientY - this.demoDragOffset.y;

        // التأكد من بقاء اللوحة داخل الشاشة
        const margin = 10;
        const maxLeft = window.innerWidth - this.currentDemoPanel.offsetWidth - margin;
        const maxTop = window.innerHeight - this.currentDemoPanel.offsetHeight - margin;

        newLeft = Math.max(margin, Math.min(maxLeft, newLeft));
        newTop = Math.max(margin, Math.min(maxTop, newTop));

        this.currentDemoPanel.style.left = newLeft + 'px';
        this.currentDemoPanel.style.top = newTop + 'px';
    }

    stopDemoDrag() {
        if (!this.isDemoDragging || !this.currentDemoPanel) return;

        this.isDemoDragging = false;
        this.currentDemoPanel.classList.remove('dragging');
        this.currentDemoPanel.style.transition = '';
        this.currentDemoPanel.style.zIndex = '100';

        // إزالة مستمعي الأحداث
        document.removeEventListener('mousemove', this.handleDemoDrag.bind(this));
        document.removeEventListener('touchmove', this.handleDemoDrag.bind(this));
        document.removeEventListener('mouseup', this.stopDemoDrag.bind(this));
        document.removeEventListener('touchend', this.stopDemoDrag.bind(this));

        this.currentDemoPanel = null;
    }

    updateDemoStats() {
        const fishTypes = new Set(this.fish.map(f => f.type)).size;
        const largestFish = this.fish.length > 0 ?
            Math.max(...this.fish.map(f => f.size)).toFixed(1) : '0';

        document.getElementById('demoFishCount').textContent = this.fish.length;
        document.getElementById('demoPlayerCount').textContent = this.players.size;
        document.getElementById('demoBattleCount').textContent = this.gameState.battles;
        document.getElementById('demoRoseCount').textContent = this.gameState.rosesReceived;
        document.getElementById('demoFishTypes').textContent = fishTypes;
        document.getElementById('demoAchievements').textContent = this.achievements.length;

        // تحديث إحصائيات المعارك الجديدة في التجربة
        const demoActiveBattlesElement = document.getElementById('demoActiveBattles');
        const demoTotalDeathsElement = document.getElementById('demoTotalDeaths');

        if (demoActiveBattlesElement) {
            demoActiveBattlesElement.textContent = this.gameState.activeBattles;
        }
        if (demoTotalDeathsElement) {
            demoTotalDeathsElement.textContent = this.gameState.totalDeaths;
        }

        // معلومات الكاميرا
        document.getElementById('demoZoomLevel').textContent = `${this.camera.zoom.toFixed(1)}x`;

        // عرض معلومات المتابعة مع التحقق من صحة المخلوق
        const followTargetElement = document.getElementById('demoFollowTarget');
        if (followTargetElement) {
            if (this.camera.followTarget &&
                this.fish.includes(this.camera.followTarget) &&
                !this.camera.followTarget.isDead) {
                const target = this.camera.followTarget;
                const size = target.size ? target.size.toFixed(1) : '?';
                followTargetElement.textContent = `${target.playerName} (${target.typeName || target.type}) - ${size}`;

                // لون حسب حجم المخلوق
                if (target.size >= 50) {
                    followTargetElement.style.color = '#ff6b6b'; // أحمر للعملاق
                } else if (target.size >= 20) {
                    followTargetElement.style.color = '#4ecdc4'; // أزرق للكبير
                } else if (target.size >= 10) {
                    followTargetElement.style.color = '#45b7d1'; // أزرق فاتح للمتوسط
                } else {
                    followTargetElement.style.color = '#96ceb4'; // أخضر فاتح للصغير
                }
            } else {
                followTargetElement.textContent = `لا شيء (حد: ${this.autoFollowThreshold})`;
                followTargetElement.style.color = '#666'; // رمادي لعدم المتابعة
            }
        }

        document.getElementById('demoLargestFish').textContent = largestFish;

        // عرض معدل الإطارات المستهدف والحالي
        const fpsElement = document.getElementById('demoFPS');
        if (fpsElement) {
            fpsElement.textContent = `${this.gameState.fps}/${this.frameRate.targetFPS} FPS`;
        }

        // عرض دقة النافذة الحالية
        const resolutionElement = document.getElementById('demoResolution');
        if (resolutionElement) {
            resolutionElement.textContent = `${this.gameWidth}x${this.gameHeight}`;
        }

        // عرض مؤشر الأداء
        const performanceElement = document.getElementById('demoPerformance');
        if (performanceElement) {
            const fpsRatio = this.gameState.fps / this.frameRate.targetFPS;
            let performanceText = 'ممتاز';
            let performanceColor = '#00ff7f';

            if (fpsRatio < 0.5) {
                performanceText = 'ضعيف';
                performanceColor = '#ff4757';
            } else if (fpsRatio < 0.7) {
                performanceText = 'متوسط';
                performanceColor = '#ffa502';
            } else if (fpsRatio < 0.9) {
                performanceText = 'جيد';
                performanceColor = '#fffa65';
            }

            performanceElement.textContent = performanceText;
            performanceElement.style.color = performanceColor;
        }
    }

    startGame() {
        this.gameState.isRunning = true;
        this.gameState.startTime = Date.now();

        // تطبيق إعدادات إخفاء/إظهار القوائم
        this.applyUIVisibilitySettings();

        // بدء حلقة اللعبة
        this.gameLoop();

        // بدء تحديث الواجهة
        this.updateUI();

        console.log('🎮 بدأت اللعبة!');
    }

    gameLoop(currentTime = 0) {
        if (!this.gameState.isRunning) return;

        // حساب الوقت المنقضي
        this.frameRate.deltaTime = currentTime - this.frameRate.lastFrameTime;

        // تحقق من معدل الإطارات المستهدف
        if (this.frameRate.deltaTime >= this.frameRate.frameInterval) {
            // تحديث الكاميرا
            this.updateCamera();

            // حفظ حالة الكانفاس وتطبيق تحويلات الكاميرا
            this.ctx.save();
            this.applyCameraTransform();

            // مسح الشاشة
            this.clearCanvas();

            // رسم البيئة
            this.drawEnvironment();

            // تحديث ورسم الكائنات
            this.updateAndDrawBubbles();
            this.updateAndDrawFish();
            this.updateAndDrawParticles();

            // استعادة حالة الكانفاس
            this.ctx.restore();

            // تحديث الفيزياء
            this.updatePhysics();

            // تحديث FPS
            this.updateFPS();

            // تحديث وقت الإطار الأخير
            this.frameRate.lastFrameTime = currentTime - (this.frameRate.deltaTime % this.frameRate.frameInterval);
        }

        // الإطار التالي
        requestAnimationFrame((time) => this.gameLoop(time));
    }

    // تحديث الكاميرا
    updateCamera() {
        // تحديث التكبير التدريجي
        this.camera.zoom += (this.camera.targetZoom - this.camera.zoom) * this.camera.smoothing;

        // فحص المتابعة التلقائية للأسماك الكبيرة
        this.checkAutoFollowLargeFish();

        // إذا كان هناك هدف للمتابعة
        if (this.camera.followTarget && this.fish.includes(this.camera.followTarget)) {
            const target = this.camera.followTarget;

            // التحقق من صحة المخلوق المتابع
            if (target && typeof target.x === 'number' && typeof target.y === 'number' && !target.isDead) {
                const targetX = target.x - this.gameWidth / (2 * this.camera.zoom);
                const targetY = target.y - this.gameHeight / (2 * this.camera.zoom);

                this.camera.x += (targetX - this.camera.x) * this.camera.smoothing;
                this.camera.y += (targetY - this.camera.y) * this.camera.smoothing;
            } else {
                // إذا كان المخلوق المتابع غير صالح، أوقف المتابعة
                console.log('🎯 المخلوق المتابع غير صالح، إيقاف المتابعة...');
                this.camera.followTarget = null;
            }
        } else {
            // إذا لم يكن هناك هدف صالح، ركز على وسط الشاشة
            const centerX = this.gameWidth / 2 - this.gameWidth / (2 * this.camera.zoom);
            const centerY = this.gameHeight / 2 - this.gameHeight / (2 * this.camera.zoom);

            this.camera.x += (centerX - this.camera.x) * this.camera.smoothing * 0.5;
            this.camera.y += (centerY - this.camera.y) * this.camera.smoothing * 0.5;

            // إذا كان هناك هدف متابعة لكنه لم يعد في القائمة، امسحه
            if (this.camera.followTarget && !this.fish.includes(this.camera.followTarget)) {
                console.log('🎯 المخلوق المتابع لم يعد موجود، إيقاف المتابعة...');
                this.camera.followTarget = null;
            }
        }

        // تحديد حدود الكاميرا
        const maxX = this.gameWidth - this.gameWidth / this.camera.zoom;
        const maxY = this.gameHeight - this.gameHeight / this.camera.zoom;

        this.camera.x = Math.max(0, Math.min(maxX, this.camera.x));
        this.camera.y = Math.max(0, Math.min(maxY, this.camera.y));
    }

    // تطبيق تحويلات الكاميرا
    applyCameraTransform() {
        this.ctx.scale(this.camera.zoom, this.camera.zoom);
        this.ctx.translate(-this.camera.x, -this.camera.y);
    }

    // تعيين مستوى التكبير
    setZoom(zoom) {
        this.camera.targetZoom = Math.max(this.camera.minZoom, Math.min(this.camera.maxZoom, zoom));
    }

    // متابعة مخلوق معين
    followFish(fish) {
        // التحقق من صحة المخلوق قبل المتابعة
        if (fish &&
            typeof fish.x === 'number' &&
            typeof fish.y === 'number' &&
            fish.playerName &&
            !fish.isDead &&
            this.fish.includes(fish)) {
            this.camera.followTarget = fish;
            console.log(`🎯 بدء متابعة: ${fish.playerName} (${fish.typeName || fish.type}) - حجم: ${fish.size ? fish.size.toFixed(1) : 'غير محدد'}`);
        } else {
            console.log('⚠️ لا يمكن متابعة مخلوق غير صالح');
            this.camera.followTarget = null;
        }
    }

    // إلغاء المتابعة
    stopFollowing() {
        this.camera.followTarget = null;
    }

    // متابعة مخلوق عشوائي
    followRandomFish() {
        // تصفية المخلوقات الصالحة للمتابعة
        const validFish = this.fish.filter(fish =>
            fish &&
            typeof fish.x === 'number' &&
            typeof fish.y === 'number' &&
            fish.playerName &&
            !fish.isDead
        );

        if (validFish.length > 0) {
            const randomFish = validFish[Math.floor(Math.random() * validFish.length)];
            this.followFish(randomFish);
        } else {
            console.log('⚠️ لا توجد مخلوقات صالحة للمتابعة');
        }
    }

    // متابعة أكبر مخلوق
    followLargestFish() {
        // تصفية المخلوقات الصالحة للمتابعة
        const validFish = this.fish.filter(fish =>
            fish &&
            typeof fish.x === 'number' &&
            typeof fish.y === 'number' &&
            typeof fish.size === 'number' &&
            fish.playerName &&
            !fish.isDead
        );

        if (validFish.length > 0) {
            const largestFish = validFish.reduce((largest, current) => {
                if (!largest) return current;
                return current.size > largest.size ? current : largest;
            });
            this.followFish(largestFish);
        } else {
            console.log('⚠️ لا توجد مخلوقات صالحة للمتابعة');
        }
    }

    // توسيط الكاميرا
    centerCamera() {
        this.stopFollowing();
        this.camera.x = 0;
        this.camera.y = 0;
        console.log('🏠 تم توسيط الكاميرا');
    }

    // فحص المتابعة التلقائية للمخلوقات (جميع الأحجام)
    checkAutoFollowLargeFish() {
        // التحقق من تفعيل المتابعة التلقائية
        if (!this.autoFollowEnabled) {
            return;
        }

        // إذا كانت الكاميرا تتابع مخلوق بالفعل وما زال موجود وصالح
        if (this.camera.followTarget &&
            this.fish.includes(this.camera.followTarget) &&
            !this.camera.followTarget.isDead) {

            // إذا كان المخلوق المتابع ما زال أكبر من الحد الأدنى أو لا يوجد مخلوق أكبر بكثير
            const currentSize = this.camera.followTarget.size || 0;

            // البحث عن مخلوق أكبر بشكل ملحوظ (على الأقل 50% أكبر)
            const significantlyLargerFish = this.fish.find(fish =>
                fish &&
                fish !== this.camera.followTarget &&
                typeof fish.size === 'number' &&
                fish.size > currentSize * 1.5 && // 50% أكبر على الأقل
                fish.x !== undefined &&
                fish.y !== undefined &&
                fish.playerName &&
                !fish.isDead
            );

            // إذا لم يوجد مخلوق أكبر بشكل ملحوظ، استمر في المتابعة
            if (!significantlyLargerFish) {
                return;
            } else {
                console.log(`🎯 وجد مخلوق أكبر بكثير: ${significantlyLargerFish.playerName} (${significantlyLargerFish.size.toFixed(1)} vs ${currentSize.toFixed(1)})`);
            }
        }

        // البحث عن أفضل مخلوق للمتابعة (أولوية للكبار، لكن يشمل الجميع)
        const validFish = this.fish.filter(fish => {
            return fish &&
                   typeof fish.size === 'number' &&
                   fish.x !== undefined &&
                   fish.y !== undefined &&
                   fish.playerName &&
                   !fish.isDead;
        });

        if (validFish.length > 0) {
            // ترتيب المخلوقات حسب الأولوية
            let targetFish = null;

            // أولاً: البحث عن مخلوقات كبيرة (أكبر من الحد المطلوب)
            const largeFish = validFish.filter(fish => fish.size >= this.autoFollowThreshold);
            if (largeFish.length > 0) {
                targetFish = largeFish.reduce((largest, current) =>
                    current.size > largest.size ? current : largest
                );
                console.log(`🎯 متابعة مخلوق كبير: ${targetFish.playerName} - حجم: ${targetFish.size.toFixed(1)}`);
            } else {
                // ثانياً: إذا لم توجد مخلوقات كبيرة، اختر أكبر المتاح
                targetFish = validFish.reduce((largest, current) =>
                    current.size > largest.size ? current : largest
                );
                console.log(`🎯 متابعة أكبر مخلوق متاح: ${targetFish.playerName} - حجم: ${targetFish.size.toFixed(1)}`);
            }

            // بدء المتابعة
            if (targetFish) {
                this.followFish(targetFish);
            }
        } else {
            console.log('⚠️ لا توجد مخلوقات صالحة للمتابعة');
        }
    }

    clearCanvas() {
        // تدرج الخلفية المائية - أكبر لتغطية المنطقة المكبرة
        const gradient = this.ctx.createLinearGradient(0, 0, 0, this.gameHeight);
        gradient.addColorStop(0, '#87CEEB');    // أزرق فاتح في الأعلى
        gradient.addColorStop(0.2, '#4682B4');  // أزرق متوسط
        gradient.addColorStop(0.4, '#1E90FF');  // أزرق
        gradient.addColorStop(0.6, '#0066CC');  // أزرق داكن
        gradient.addColorStop(0.8, '#003366');  // أزرق داكن جداً
        gradient.addColorStop(1, '#001122');    // أزرق أسود في الأسفل

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(-this.gameWidth, -this.gameHeight, this.gameWidth * 3, this.gameHeight * 3);
    }

    drawEnvironment() {
        // رسم الرمال المتحركة في القاع
        this.drawSandParticles();

        // رسم الصخور والكهوف
        this.drawRocks();

        // رسم الشعاب المرجانية
        this.drawCorals();

        // رسم الأعشاب البحرية المحسنة
        this.drawSeaweed();

        // رسم الكنوز المدفونة
        this.drawTreasures();

        // رسم أشعة الضوء المحسنة
        this.drawLightRays();
    }

    drawSandParticles() {
        this.environment.sandParticles.forEach(particle => {
            this.ctx.save();
            this.ctx.globalAlpha = particle.opacity;
            this.ctx.fillStyle = '#F4A460';

            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fill();

            this.ctx.restore();

            // تحريك الرمال
            particle.x += particle.speed;
            if (particle.x > this.gameWidth) {
                particle.x = -10;
                particle.y = this.gameHeight - Math.random() * 100;
            }
        });
    }

    drawRocks() {
        this.environment.rocks.forEach(rock => {
            this.ctx.save();
            this.ctx.fillStyle = rock.color;
            this.ctx.strokeStyle = this.darkenColor(rock.color, 0.3);
            this.ctx.lineWidth = 2;

            if (rock.type === 0) {
                // صخرة دائرية
                this.ctx.beginPath();
                this.ctx.ellipse(rock.x, rock.y, rock.width/2, rock.height/2, 0, 0, Math.PI * 2);
                this.ctx.fill();
                this.ctx.stroke();
            } else {
                // كهف
                this.ctx.beginPath();
                this.ctx.arc(rock.x, rock.y, rock.width/2, Math.PI, 0);
                this.ctx.closePath();
                this.ctx.fill();
                this.ctx.stroke();

                // ظل الكهف
                this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
                this.ctx.beginPath();
                this.ctx.ellipse(rock.x, rock.y - 10, rock.width/3, rock.height/4, 0, 0, Math.PI * 2);
                this.ctx.fill();
            }

            this.ctx.restore();
        });
    }

    drawCorals() {
        this.environment.corals.forEach(coral => {
            this.ctx.save();
            this.ctx.fillStyle = coral.color;
            this.ctx.strokeStyle = this.darkenColor(coral.color, 0.2);
            this.ctx.lineWidth = 1;

            const swayOffset = Math.sin(coral.sway) * 5;

            switch (coral.type) {
                case 0:
                    // شعاب مرجانية متفرعة
                    this.drawBranchedCoral(coral, swayOffset);
                    break;
                case 1:
                    // شعاب مرجانية دائرية
                    this.drawRoundCoral(coral, swayOffset);
                    break;
                case 2:
                    // شعاب مرجانية طويلة
                    this.drawTallCoral(coral, swayOffset);
                    break;
            }

            this.ctx.restore();
            coral.sway += 0.02;
        });
    }

    drawBranchedCoral(coral, swayOffset) {
        const branches = 5;
        for (let i = 0; i < branches; i++) {
            const angle = (i / branches) * Math.PI * 2;
            const branchLength = coral.height * (0.5 + Math.random() * 0.5);

            this.ctx.beginPath();
            this.ctx.moveTo(coral.x, coral.y);
            this.ctx.lineTo(
                coral.x + Math.cos(angle) * branchLength + swayOffset,
                coral.y - Math.sin(angle) * branchLength
            );
            this.ctx.stroke();

            // فروع صغيرة
            const subBranches = 3;
            for (let j = 0; j < subBranches; j++) {
                const subAngle = angle + (j - 1) * 0.5;
                const subLength = branchLength * 0.3;

                this.ctx.beginPath();
                this.ctx.moveTo(
                    coral.x + Math.cos(angle) * branchLength * 0.7 + swayOffset,
                    coral.y - Math.sin(angle) * branchLength * 0.7
                );
                this.ctx.lineTo(
                    coral.x + Math.cos(subAngle) * (branchLength * 0.7 + subLength) + swayOffset,
                    coral.y - Math.sin(subAngle) * (branchLength * 0.7 + subLength)
                );
                this.ctx.stroke();
            }
        }
    }

    drawRoundCoral(coral, swayOffset) {
        const circles = 8;
        for (let i = 0; i < circles; i++) {
            const angle = (i / circles) * Math.PI * 2;
            const radius = coral.width / 6;
            const distance = coral.width / 4;

            this.ctx.beginPath();
            this.ctx.arc(
                coral.x + Math.cos(angle) * distance + swayOffset,
                coral.y + Math.sin(angle) * distance,
                radius,
                0,
                Math.PI * 2
            );
            this.ctx.fill();
            this.ctx.stroke();
        }

        // المركز
        this.ctx.beginPath();
        this.ctx.arc(coral.x + swayOffset, coral.y, coral.width / 8, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();
    }

    drawTallCoral(coral, swayOffset) {
        const segments = 6;
        const segmentHeight = coral.height / segments;

        for (let i = 0; i < segments; i++) {
            const width = coral.width * (1 - i / segments * 0.5);
            const y = coral.y - i * segmentHeight;

            this.ctx.beginPath();
            this.ctx.ellipse(
                coral.x + swayOffset * (i / segments),
                y,
                width / 2,
                segmentHeight / 2,
                0,
                0,
                Math.PI * 2
            );
            this.ctx.fill();
            this.ctx.stroke();
        }
    }

    drawSeaweed() {
        this.environment.seaweed.forEach(seaweed => {
            this.ctx.save();
            this.ctx.strokeStyle = seaweed.color;
            this.ctx.lineWidth = seaweed.type === 'kelp' ? 4 : 2;
            this.ctx.lineCap = 'round';

            const segments = seaweed.type === 'kelp' ? 10 : 6;
            const segmentHeight = seaweed.height / segments;

            for (let i = 0; i < segments; i++) {
                const swayAmount = seaweed.type === 'kelp' ? i * 3 : i * 1.5;
                const startX = seaweed.x + Math.sin(seaweed.sway + i * 0.5) * swayAmount;
                const startY = seaweed.y - i * segmentHeight;
                const endX = seaweed.x + Math.sin(seaweed.sway + (i + 1) * 0.5) * (swayAmount + 1.5);
                const endY = seaweed.y - (i + 1) * segmentHeight;

                this.ctx.beginPath();
                this.ctx.moveTo(startX, startY);
                this.ctx.lineTo(endX, endY);
                this.ctx.stroke();

                // أوراق للأعشاب البحرية
                if (seaweed.type === 'kelp' && i % 2 === 0) {
                    this.ctx.fillStyle = seaweed.color;
                    this.ctx.beginPath();
                    this.ctx.ellipse(startX + 10, startY - 5, 8, 15, Math.PI / 4, 0, Math.PI * 2);
                    this.ctx.fill();
                }
            }

            this.ctx.restore();

            // تحديث التمايل
            seaweed.sway += seaweed.swaySpeed;
        });
    }

    drawTreasures() {
        this.environment.treasures.forEach(treasure => {
            if (treasure.discovered) return;

            this.ctx.save();

            // توهج الكنز
            const glowIntensity = 0.5 + Math.sin(treasure.glow) * 0.3;
            this.ctx.shadowColor = '#FFD700';
            this.ctx.shadowBlur = treasure.size * glowIntensity;

            // صندوق الكنز
            this.ctx.fillStyle = '#8B4513';
            this.ctx.fillRect(
                treasure.x - treasure.size/2,
                treasure.y - treasure.size/2,
                treasure.size,
                treasure.size * 0.7
            );

            // غطاء الصندوق
            this.ctx.fillStyle = '#A0522D';
            this.ctx.fillRect(
                treasure.x - treasure.size/2,
                treasure.y - treasure.size/2,
                treasure.size,
                treasure.size * 0.3
            );

            // قفل ذهبي
            this.ctx.fillStyle = '#FFD700';
            this.ctx.beginPath();
            this.ctx.arc(treasure.x, treasure.y - treasure.size * 0.2, treasure.size * 0.1, 0, Math.PI * 2);
            this.ctx.fill();

            // جواهر متناثرة
            for (let i = 0; i < 3; i++) {
                const gemX = treasure.x + (Math.random() - 0.5) * treasure.size;
                const gemY = treasure.y + treasure.size * 0.2 + Math.random() * 10;

                this.ctx.fillStyle = i === 0 ? '#FF0000' : i === 1 ? '#00FF00' : '#0000FF';
                this.ctx.beginPath();
                this.ctx.arc(gemX, gemY, 3, 0, Math.PI * 2);
                this.ctx.fill();
            }

            this.ctx.restore();

            treasure.glow += treasure.glowSpeed;
        });
    }

    drawLightRays() {
        this.environment.lightRays.forEach(ray => {
            this.ctx.save();
            this.ctx.globalAlpha = ray.opacity;

            // تحسين أشعة الضوء مع زاوية
            this.ctx.translate(ray.x, 0);
            this.ctx.rotate(ray.angle);

            const gradient = this.ctx.createLinearGradient(0, 0, 0, this.gameHeight);
            gradient.addColorStop(0, 'rgba(255, 255, 255, 0.9)');
            gradient.addColorStop(0.2, 'rgba(255, 255, 255, 0.6)');
            gradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.3)');
            gradient.addColorStop(1, 'rgba(255, 255, 255, 0)');

            this.ctx.fillStyle = gradient;
            this.ctx.fillRect(-ray.width/2, 0, ray.width, this.gameHeight);

            this.ctx.restore();

            // تحريك أشعة الضوء
            ray.x += ray.speed;
            if (ray.x > this.gameWidth + ray.width) {
                ray.x = -ray.width;
                ray.angle = Math.random() * 0.2 - 0.1; // زاوية جديدة
            }
        });
    }

    createBubble() {
        this.bubbles.push({
            x: Math.random() * this.gameWidth,
            y: this.gameHeight + 20,
            radius: 2 + Math.random() * 8,
            speed: 0.5 + Math.random() * 2,
            opacity: 0.3 + Math.random() * 0.4,
            wobble: Math.random() * Math.PI * 2,
            wobbleSpeed: 0.02 + Math.random() * 0.03
        });
    }

    updateAndDrawBubbles() {
        // إضافة فقاعات جديدة
        if (Math.random() < 0.1) {
            this.createBubble();
        }

        // تحديث ورسم الفقاعات
        for (let i = this.bubbles.length - 1; i >= 0; i--) {
            const bubble = this.bubbles[i];

            // تحديث الموقع
            bubble.y -= bubble.speed;
            bubble.x += Math.sin(bubble.wobble) * 0.5;
            bubble.wobble += bubble.wobbleSpeed;

            // رسم الفقاعة
            this.ctx.save();
            this.ctx.globalAlpha = bubble.opacity;
            this.ctx.strokeStyle = '#87CEEB';
            this.ctx.fillStyle = 'rgba(135, 206, 235, 0.1)';
            this.ctx.lineWidth = 1;

            this.ctx.beginPath();
            this.ctx.arc(bubble.x, bubble.y, bubble.radius, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.stroke();
            this.ctx.restore();

            // إزالة الفقاعات التي خرجت من الشاشة
            if (bubble.y < -20) {
                this.bubbles.splice(i, 1);
            }
        }
    }

    createFish(playerName, roseCount = 1) {
        // تحديد نوع السمكة بناءً على عدد الورود والعشوائية
        const fishType = this.determineFishType(roseCount);
        const size = this.calculateFishSize(roseCount);
        const color = this.getFishColor(size, fishType);
        const playerIndex = Array.from(this.players.keys()).indexOf(playerName);
        const playerColor = this.playerColors[playerIndex % this.maxPlayers];

        // الحصول على إحصائيات نوع السمكة
        const fishTypeData = this.fishTypes[fishType];
        const baseHealth = fishTypeData.baseHealth || 100;
        const attackPower = fishTypeData.attackPower || 15;
        const defense = fishTypeData.defense || 5;

        const fish = {
            id: Date.now() + Math.random(),
            playerName: playerName,
            playerColor: playerColor,
            x: Math.random() * (this.gameWidth - 100) + 50,
            y: Math.random() * (this.gameHeight - 200) + 100,
            vx: (Math.random() - 0.5) * 2,
            vy: (Math.random() - 0.5) * 2,
            size: size,
            baseSize: size,
            color: color,

            // نظام الصحة والقتال الجديد
            health: baseHealth + (size * 2), // الصحة تعتمد على النوع والحجم
            maxHealth: baseHealth + (size * 2),
            attackPower: attackPower + Math.floor(size / 10), // القوة تزيد مع الحجم
            defense: defense + Math.floor(size / 15), // الدفاع يزيد مع الحجم
            isInBattle: false,
            battleTarget: null,
            lastAttackTime: 0,
            attackCooldown: 1000, // ثانية واحدة بين الهجمات

            energy: 100,
            maxEnergy: 100,
            age: 0,
            roseCount: roseCount,
            kills: 0,

            // نوع السمكة وخصائصها
            type: fishType,
            typeName: this.fishTypes[fishType].name,
            baseSpeed: this.fishTypes[fishType].speed,
            aggression: this.fishTypes[fishType].aggression,
            special: this.fishTypes[fishType].special,
            specialCooldown: 0,

            // خصائص الحركة المحسنة
            targetX: 0,
            targetY: 0,
            speed: (1 + Math.random() * 2) * this.fishTypes[fishType].speed,
            maxSpeed: (3 + size * 0.1) * this.fishTypes[fishType].speed,

            // خصائص بصرية محسنة
            tailAngle: 0,
            finAngle: 0,
            eyeSize: size * 0.15,
            bodyPattern: Math.floor(Math.random() * 3),

            // حالات خاصة محسنة
            isHunting: false,
            isWild: false,
            speedBoost: 1,
            speedBoostTime: 0,
            isStunned: false,
            stunTime: 0,

            // ذكاء اصطناعي محسن
            behavior: 'wander',
            behaviorTimer: 0,
            nearbyFish: [],
            lastAttackTime: 0,

            // تأثيرات محسنة
            glowIntensity: 0,
            particles: [],
            trail: [],

            // إحصائيات متقدمة
            distanceTraveled: 0,
            timeAlive: 0,
            foodEaten: 0,

            // وقت الإنشاء للحذف التلقائي
            createdAt: Date.now()
        };

        // تحديد الهدف الأولي
        fish.targetX = Math.random() * this.gameWidth;
        fish.targetY = Math.random() * this.gameHeight;

        // تطبيق خصائص خاصة حسب النوع
        this.applyFishTypeSpecials(fish);

        this.fish.push(fish);
        this.gameState.totalFish++;

        // إضافة تأثير ظهور محسن
        this.createSpawnEffect(fish.x, fish.y, fishType);

        // تشغيل صوت مرعب للأسماك القوية
        this.playScarySpawnSound(fish.type, fish.size);

        // تحديث إحصائيات اللاعب
        this.updatePlayerStats(playerName, roseCount);

        console.log(`🐟 تم إنشاء ${fish.typeName} جديدة للاعب ${playerName} (${roseCount} ورود)`);

        // فحص المتابعة التلقائية للسمكة الجديدة إذا كانت كبيرة
        if (this.autoFollowEnabled && fish.size >= this.autoFollowThreshold) {
            // إذا لم تكن هناك سمكة متابعة حالياً، ابدأ متابعة هذه السمكة
            if (!this.camera.followTarget || !this.fish.includes(this.camera.followTarget)) {
                this.followFish(fish);
                console.log(`🎯 متابعة تلقائية فورية: ${fish.playerName} (${fish.typeName}) - حجم: ${fish.size.toFixed(1)}`);
            }
        }

        return fish;
    }

    determineFishType(roseCount) {
        // تحديد نوع السمكة بناءً على عدد الورود والعشوائية
        const random = Math.random();

        if (roseCount >= 50) {
            // أسماك نادرة للورود الكثيرة
            if (random < 0.3) return 'whale';
            if (random < 0.6) return 'shark';
            if (random < 0.8) return 'eel';
            return 'dolphin';
        } else if (roseCount >= 20) {
            // أسماك متوسطة النادرة
            if (random < 0.4) return 'shark';
            if (random < 0.7) return 'salmon';
            if (random < 0.9) return 'ray';
            return 'eel';
        } else if (roseCount >= 10) {
            // أسماك شائعة نسبياً
            if (random < 0.3) return 'salmon';
            if (random < 0.6) return 'dolphin';
            if (random < 0.8) return 'ray';
            return 'normal';
        } else {
            // أسماك عادية للورود القليلة
            if (random < 0.8) return 'normal';
            if (random < 0.95) return 'salmon';
            return 'ray';
        }
    }

    applyFishTypeSpecials(fish) {
        switch (fish.type) {
            case 'whale':
                fish.size *= 1.5;
                fish.maxHealth *= 2;
                fish.health = fish.maxHealth;
                break;
            case 'shark':
                fish.maxSpeed *= 1.2;
                fish.aggression *= 1.5;
                break;
            case 'dolphin':
                fish.maxSpeed *= 1.8;
                fish.energy *= 1.3;
                break;
            case 'eel':
                fish.maxHealth *= 1.3;
                break;
            case 'ray':
                fish.size *= 0.8;
                fish.maxSpeed *= 0.9;
                break;
            case 'salmon':
                fish.energy *= 1.2;
                fish.maxSpeed *= 1.1;
                break;
        }
    }

    calculateFishSize(roseCount) {
        // أحجام أكبر لتكون أوضح مع التكبير الجديد
        if (roseCount <= 4) return 25 + roseCount * 3;      // صغيرة: 28-37
        if (roseCount <= 9) return 40 + (roseCount - 4) * 4; // متوسطة: 44-60
        if (roseCount <= 19) return 65 + (roseCount - 9) * 3; // كبيرة: 68-95
        return 100 + Math.min(roseCount - 19, 30) * 2;       // عملاقة: 100-160
    }

    getFishColor(size, fishType = 'normal') {
        if (fishType !== 'normal') {
            // استخدام ألوان خاصة بنوع السمكة
            const typeColors = this.fishTypes[fishType].colors;
            if (size <= 25) return typeColors[0];
            if (size <= 40) return typeColors[1];
            if (size <= 65) return typeColors[2];
            return typeColors[3];
        }

        // ألوان الأسماك العادية
        if (size <= 25) return this.fishColors.small;
        if (size <= 40) return this.fishColors.medium;
        if (size <= 65) return this.fishColors.large;
        return this.fishColors.giant;
    }

    updateAndDrawFish() {
        for (let i = this.fish.length - 1; i >= 0; i--) {
            const fish = this.fish[i];

            // تحديث السمكة
            this.updateFishBehavior(fish);
            this.updateFishMovement(fish);
            this.updateFishStats(fish);

            // رسم السمكة
            this.drawFish(fish);

            // فحص الموت
            if (fish.health <= 0) {
                this.killFish(i);
            }
        }

        // فحص التصادمات والمعارك
        this.checkFishCollisions();
    }

    updateFishBehavior(fish) {
        fish.age++;
        fish.behaviorTimer++;

        // العثور على الأسماك القريبة
        fish.nearbyFish = this.fish.filter(other =>
            other !== fish && this.getDistance(fish, other) < 100
        );

        // تحديد السلوك
        if (fish.behaviorTimer > 120) { // تغيير السلوك كل ثانيتين
            fish.behaviorTimer = 0;

            const smallerFish = fish.nearbyFish.filter(other => other.size < fish.size * 0.8);
            const largerFish = fish.nearbyFish.filter(other => other.size > fish.size * 1.2);

            if (smallerFish.length > 0 && fish.energy > 50) {
                fish.behavior = 'hunt';
                const target = smallerFish[Math.floor(Math.random() * smallerFish.length)];
                fish.targetX = target.x;
                fish.targetY = target.y;
            } else if (largerFish.length > 0) {
                fish.behavior = 'flee';
                const threat = largerFish[0];
                fish.targetX = fish.x + (fish.x - threat.x) * 2;
                fish.targetY = fish.y + (fish.y - threat.y) * 2;
            } else {
                fish.behavior = 'wander';
                fish.targetX = Math.random() * this.gameWidth;
                fish.targetY = Math.random() * this.gameHeight;
            }
        }

        // تطبيق السلوك
        switch (fish.behavior) {
            case 'hunt':
                fish.speed = fish.maxSpeed * 1.5;
                fish.isHunting = true;
                break;
            case 'flee':
                fish.speed = fish.maxSpeed * 1.8;
                fish.isHunting = false;
                break;
            default:
                fish.speed = fish.maxSpeed * 0.8;
                fish.isHunting = false;
        }

        // تطبيق التعزيزات
        if (fish.speedBoostTime > 0) {
            fish.speedBoostTime--;
            fish.speed *= fish.speedBoost;
        }

        // تحديث الطاقة
        fish.energy = Math.max(0, fish.energy - 0.1);
        if (fish.energy < 20) {
            fish.speed *= 0.5; // بطء عند انخفاض الطاقة
        }
    }

    updateFishMovement(fish) {
        // حساب الاتجاه نحو الهدف
        const dx = fish.targetX - fish.x;
        const dy = fish.targetY - fish.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance > 5) {
            // تحديث السرعة
            fish.vx += (dx / distance) * 0.2;
            fish.vy += (dy / distance) * 0.2;
        }

        // تطبيق الحد الأقصى للسرعة
        const currentSpeed = Math.sqrt(fish.vx * fish.vx + fish.vy * fish.vy);
        if (currentSpeed > fish.speed) {
            fish.vx = (fish.vx / currentSpeed) * fish.speed;
            fish.vy = (fish.vy / currentSpeed) * fish.speed;
        }

        // تطبيق مقاومة الماء
        fish.vx *= this.physics.waterResistance;
        fish.vy *= this.physics.waterResistance;

        // تحديث الموقع
        fish.x += fish.vx;
        fish.y += fish.vy;

        // الحفاظ على السمكة داخل الشاشة
        if (fish.x < fish.size) {
            fish.x = fish.size;
            fish.vx = Math.abs(fish.vx);
        }
        if (fish.x > this.gameWidth - fish.size) {
            fish.x = this.gameWidth - fish.size;
            fish.vx = -Math.abs(fish.vx);
        }
        if (fish.y < fish.size) {
            fish.y = fish.size;
            fish.vy = Math.abs(fish.vy);
        }
        if (fish.y > this.gameHeight - fish.size) {
            fish.y = this.gameHeight - fish.size;
            fish.vy = -Math.abs(fish.vy);
        }

        // تحديث زوايا الحركة للرسم
        fish.tailAngle += 0.2;
        fish.finAngle += 0.15;
    }

    updateFishStats(fish) {
        // تجديد الطاقة تدريجياً
        if (fish.energy < fish.maxEnergy) {
            fish.energy += 0.05;
        }

        // تجديد الصحة تدريجياً إذا كانت الطاقة عالية
        if (fish.energy > 80 && fish.health < fish.maxHealth) {
            fish.health += 0.1;
        }

        // تحديث التوهج للأسماك المتوحشة
        if (fish.isWild) {
            fish.glowIntensity = 0.5 + Math.sin(fish.age * 0.1) * 0.3;
        }
    }

    drawFish(fish) {
        this.ctx.save();

        // حساب زاوية الاتجاه
        const angle = Math.atan2(fish.vy, fish.vx);

        // الانتقال إلى موقع السمكة
        this.ctx.translate(fish.x, fish.y);
        this.ctx.rotate(angle);

        // رسم التوهج للأسماك المتوحشة أو الخاصة أو في المعركة
        if ((fish.isWild && fish.glowIntensity > 0) || fish.type !== 'normal' || fish.isInBattle) {
            this.drawFishGlow(fish);
        }

        // رسم الظل
        this.drawFishShadow(fish);

        // رسم حسب نوع السمكة
        switch (fish.type) {
            case 'shark':
                this.drawShark(fish);
                break;
            case 'whale':
                this.drawWhale(fish);
                break;
            case 'dolphin':
                this.drawDolphin(fish);
                break;
            case 'eel':
                this.drawEel(fish);
                break;
            case 'ray':
                this.drawRay(fish);
                break;
            case 'salmon':
                this.drawSalmon(fish);
                break;
            case 'jellyfish':
                this.drawJellyfish(fish);
                break;
            case 'octopus':
                this.drawOctopus(fish);
                break;
            case 'seahorse':
                this.drawSeahorse(fish);
                break;
            case 'turtle':
                this.drawTurtle(fish);
                break;
            case 'crab':
                this.drawCrab(fish);
                break;
            case 'lobster':
                this.drawLobster(fish);
                break;
            case 'starfish':
                this.drawStarfish(fish);
                break;
            case 'anglerfish':
                this.drawAnglerfish(fish);
                break;
            case 'swordfish':
                this.drawSwordfish(fish);
                break;
            case 'hammerhead':
                this.drawHammerhead(fish);
                break;
            default:
                this.drawNormalFish(fish);
        }

        // رسم اسم اللاعب
        this.drawPlayerName(fish);

        // رسم شريط الصحة
        this.drawHealthBar(fish);

        this.ctx.restore();
    }

    drawFishGlow(fish) {
        this.ctx.save();

        let glowColor = '#ff0000';
        let glowIntensity = fish.glowIntensity || 0.3;

        // توهج خاص للأسماك في المعركة
        if (fish.isInBattle) {
            glowColor = '#ff0000'; // أحمر للمعركة
            glowIntensity = 0.6 + Math.sin(fish.age * 0.3) * 0.4; // توهج نابض سريع

            // إضافة هالة حمراء للمعركة
            this.ctx.shadowColor = glowColor;
            this.ctx.shadowBlur = fish.size * glowIntensity;
            this.ctx.globalAlpha = 0.3;

            // رسم دائرة توهج للمعركة
            this.ctx.fillStyle = glowColor;
            this.ctx.beginPath();
            this.ctx.arc(0, 0, fish.size * 1.2, 0, Math.PI * 2);
            this.ctx.fill();

            this.ctx.globalAlpha = 1;
            this.ctx.shadowBlur = 0;
        } else {
            // ألوان توهج مختلفة حسب نوع السمكة (للأسماك غير المتقاتلة)
            switch (fish.type) {
                case 'shark':
                    glowColor = '#ff4444';
                    break;
                case 'whale':
                    glowColor = '#4444ff';
                    break;
                case 'dolphin':
                    glowColor = '#44ffff';
                    break;
                case 'eel':
                    glowColor = '#ffff44';
                    glowIntensity = 0.5 + Math.sin(fish.age * 0.2) * 0.3; // توهج كهربائي
                    break;
                case 'ray':
                    glowColor = '#ff44ff';
                    break;
                case 'salmon':
                    glowColor = '#ff8844';
                    break;
            }
        }

        this.ctx.restore();
    }

    drawFishShadow(fish) {
        // وظيفة فارغة للتوافق - لا تفعل شيء
    }

    // رسم السمكة العادية
    drawNormalFish(fish) {
        this.drawFishBody(fish);
        this.drawFishTail(fish);
        this.drawFishFins(fish);
        this.drawFishEyes(fish);
        this.drawFishPattern(fish);
    }

    // رسم القرش - تصميم كرتوني لطيف
    drawShark(fish) {
        // قرش كرتوني لطيف وبسيط
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.2);
        this.ctx.lineWidth = 2;

        // الجسم الرئيسي - شكل بيضاوي بسيط
        this.ctx.beginPath();
        this.ctx.ellipse(0, 0, fish.size, fish.size * 0.5, 0, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // البطن الأبيض
        this.ctx.fillStyle = '#ffffff';
        this.ctx.beginPath();
        this.ctx.ellipse(0, fish.size * 0.15, fish.size * 0.7, fish.size * 0.3, 0, 0, Math.PI * 2);
        this.ctx.fill();

        // الزعنفة الظهرية - مثلث بسيط
        this.ctx.fillStyle = this.darkenColor(fish.color, 0.1);
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.2);
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.2, -fish.size * 0.5);
        this.ctx.lineTo(fish.size * 0.4, -fish.size * 0.8);
        this.ctx.lineTo(fish.size * 0.6, -fish.size * 0.4);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();

        // الزعانف الصدرية - بيضاوية بسيطة
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.1);
        this.ctx.beginPath();
        this.ctx.ellipse(fish.size * 0.3, -fish.size * 0.2, fish.size * 0.2, fish.size * 0.1, -Math.PI/4, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        this.ctx.beginPath();
        this.ctx.ellipse(fish.size * 0.3, fish.size * 0.2, fish.size * 0.2, fish.size * 0.1, Math.PI/4, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // الخياشيم - خطوط بسيطة
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;
        for (let i = 0; i < 3; i++) {
            this.ctx.beginPath();
            this.ctx.moveTo(fish.size * (0.4 + i * 0.08), -fish.size * 0.1);
            this.ctx.lineTo(fish.size * (0.35 + i * 0.08), fish.size * 0.1);
            this.ctx.stroke();
        }

        // ذيل القرش
        this.drawSharkTail(fish);

        // عيون القرش
        this.drawSharkEyes(fish);

        // أسنان القرش
        this.drawSharkTeeth(fish);
    }

    // رسم الحوت - تصميم كرتوني لطيف
    drawWhale(fish) {
        // حوت كرتوني لطيف وبسيط - حجم أصغر
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.2);
        this.ctx.lineWidth = 2;

        // الجسم الرئيسي - حجم أصغر ومناسب
        this.ctx.beginPath();
        this.ctx.ellipse(0, 0, fish.size * 0.8, fish.size * 0.45, 0, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // البطن الأبيض - أصغر
        this.ctx.fillStyle = '#ffffff';
        this.ctx.beginPath();
        this.ctx.ellipse(0, fish.size * 0.15, fish.size * 0.55, fish.size * 0.25, 0, 0, Math.PI * 2);
        this.ctx.fill();

        // خطوط البطن - خطوط بسيطة أصغر
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;
        for (let i = 0; i < 4; i++) {
            this.ctx.beginPath();
            this.ctx.moveTo(fish.size * (0.25 - i * 0.12), fish.size * 0.08);
            this.ctx.lineTo(fish.size * (0.2 - i * 0.12), fish.size * 0.3);
            this.ctx.stroke();
        }

        // الزعنفة الظهرية - مثلث صغير
        this.ctx.fillStyle = this.darkenColor(fish.color, 0.1);
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.2);
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 0.05, -fish.size * 0.45);
        this.ctx.lineTo(fish.size * 0.1, -fish.size * 0.65);
        this.ctx.lineTo(fish.size * 0.2, -fish.size * 0.4);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();

        // الزعانف الصدرية - زعنفة واحدة فقط
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.1);

        // الزعنفة السفلية فقط - أبعد وأجمل
        this.ctx.beginPath();
        this.ctx.ellipse(fish.size * 0.25, fish.size * 0.3, fish.size * 0.25, fish.size * 0.08, Math.PI/4, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // ذيل الحوت - متصل بالجسم ومتناسب
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.2);
        this.ctx.lineWidth = 2;

        // الذيل العلوي
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 0.8, 0);
        this.ctx.lineTo(-fish.size * 1.1, -fish.size * 0.35);
        this.ctx.lineTo(-fish.size * 0.9, -fish.size * 0.15);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();

        // الذيل السفلي
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 0.8, 0);
        this.ctx.lineTo(-fish.size * 1.1, fish.size * 0.35);
        this.ctx.lineTo(-fish.size * 0.9, fish.size * 0.15);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();

        // عيون الحوت
        this.drawWhaleEyes(fish);

        // نافورة الماء
        this.drawWhaleSpout(fish);
    }

    // رسم الدولفين
    drawDolphin(fish) {
        // جسم الدولفين الواقعي والأنيق
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;

        // جسم الدولفين الانسيابي
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.9, 0); // الرأس
        this.ctx.quadraticCurveTo(fish.size * 0.4, -fish.size * 0.6, -fish.size * 0.1, -fish.size * 0.5); // الظهر
        this.ctx.quadraticCurveTo(-fish.size * 0.7, -fish.size * 0.2, -fish.size, 0); // نهاية الجسم
        this.ctx.quadraticCurveTo(-fish.size * 0.7, fish.size * 0.2, -fish.size * 0.1, fish.size * 0.5); // البطن
        this.ctx.quadraticCurveTo(fish.size * 0.4, fish.size * 0.6, fish.size * 0.9, 0); // العودة للرأس
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();

        // البطن الفاتح المميز للدولفين
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.4);
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.7, 0);
        this.ctx.quadraticCurveTo(fish.size * 0.2, fish.size * 0.4, -fish.size * 0.4, fish.size * 0.3);
        this.ctx.quadraticCurveTo(-fish.size * 0.6, 0, -fish.size * 0.4, -fish.size * 0.3);
        this.ctx.quadraticCurveTo(fish.size * 0.2, -fish.size * 0.4, fish.size * 0.7, 0);
        this.ctx.fill();

        // منقار الدولفين الطويل والمميز
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.2);
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.4);
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.9, 0);
        this.ctx.quadraticCurveTo(fish.size * 1.2, -fish.size * 0.1, fish.size * 1.3, 0);
        this.ctx.quadraticCurveTo(fish.size * 1.2, fish.size * 0.1, fish.size * 0.9, 0);
        this.ctx.fill();
        this.ctx.stroke();

        // خط الفم المبتسم
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.6);
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();
        this.ctx.arc(fish.size * 1.1, fish.size * 0.03, fish.size * 0.12, 0.2, Math.PI - 0.2);
        this.ctx.stroke();

        // زعنفة ظهرية منحنية مميزة
        this.ctx.fillStyle = this.darkenColor(fish.color, 0.1);
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.1, -fish.size * 0.5);
        this.ctx.quadraticCurveTo(
            fish.size * 0.3, -fish.size * 1.0,
            fish.size * 0.5, -fish.size * 0.8
        );
        this.ctx.quadraticCurveTo(
            fish.size * 0.4, -fish.size * 0.4,
            fish.size * 0.1, -fish.size * 0.5
        );
        this.ctx.fill();
        this.ctx.stroke();

        // الزعانف الصدرية الطويلة
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.2);
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.4, -fish.size * 0.1);
        this.ctx.quadraticCurveTo(
            fish.size * 0.8, -fish.size * 0.6,
            fish.size * 1.0, -fish.size * 0.2
        );
        this.ctx.quadraticCurveTo(
            fish.size * 0.8, fish.size * 0.1,
            fish.size * 0.4, fish.size * 0.1
        );
        this.ctx.quadraticCurveTo(
            fish.size * 0.5, 0,
            fish.size * 0.4, -fish.size * 0.1
        );
        this.ctx.fill();
        this.ctx.stroke();

        // ذيل الدولفين
        this.drawDolphinTail(fish);

        // عيون الدولفين
        this.drawDolphinEyes(fish);
    }

    // رسم الأنقليس
    drawEel(fish) {
        // جسم الأنقليس الواقعي والمتموج
        const segments = 12;
        const segmentLength = fish.size / segments;

        // رسم الجسم الأساسي
        this.ctx.strokeStyle = fish.color;
        this.ctx.lineWidth = fish.size * 0.4;
        this.ctx.lineCap = 'round';

        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size, 0);

        for (let i = 0; i < segments; i++) {
            const x = -fish.size + (i * segmentLength * 2);
            const y = Math.sin(fish.age * 0.1 + i * 0.5) * fish.size * 0.12;
            this.ctx.lineTo(x, y);
        }
        this.ctx.stroke();

        // الخط الظهري الداكن
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.4);
        this.ctx.lineWidth = fish.size * 0.15;
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size, 0);

        for (let i = 0; i < segments; i++) {
            const x = -fish.size + (i * segmentLength * 2);
            const y = Math.sin(fish.age * 0.1 + i * 0.5) * fish.size * 0.12 - fish.size * 0.1;
            this.ctx.lineTo(x, y);
        }
        this.ctx.stroke();

        // الخط البطني الفاتح
        this.ctx.strokeStyle = this.lightenColor(fish.color, 0.4);
        this.ctx.lineWidth = fish.size * 0.1;
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size, 0);

        for (let i = 0; i < segments; i++) {
            const x = -fish.size + (i * segmentLength * 2);
            const y = Math.sin(fish.age * 0.1 + i * 0.5) * fish.size * 0.12 + fish.size * 0.08;
            this.ctx.lineTo(x, y);
        }
        this.ctx.stroke();

        // رأس الأنقليس المدبب
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.4);
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.9, 0);
        this.ctx.quadraticCurveTo(fish.size * 0.6, -fish.size * 0.2, fish.size * 0.3, -fish.size * 0.15);
        this.ctx.quadraticCurveTo(fish.size * 0.1, 0, fish.size * 0.3, fish.size * 0.15);
        this.ctx.quadraticCurveTo(fish.size * 0.6, fish.size * 0.2, fish.size * 0.9, 0);
        this.ctx.fill();
        this.ctx.stroke();

        // الفك السفلي
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.3);
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.8, 0);
        this.ctx.quadraticCurveTo(fish.size * 0.5, fish.size * 0.15, fish.size * 0.3, fish.size * 0.1);
        this.ctx.quadraticCurveTo(fish.size * 0.4, fish.size * 0.05, fish.size * 0.8, 0);
        this.ctx.fill();

        // نمط على الجسم - بقع
        this.ctx.fillStyle = this.darkenColor(fish.color, 0.3);
        for (let i = 1; i < segments - 1; i++) {
            const x = -fish.size + (i * segmentLength * 2);
            const y = Math.sin(fish.age * 0.1 + i * 0.5) * fish.size * 0.12;

            // بقع على الجانبين
            this.ctx.beginPath();
            this.ctx.ellipse(x, y - fish.size * 0.08, fish.size * 0.03, fish.size * 0.02, 0, 0, Math.PI * 2);
            this.ctx.fill();

            this.ctx.beginPath();
            this.ctx.ellipse(x, y + fish.size * 0.08, fish.size * 0.03, fish.size * 0.02, 0, 0, Math.PI * 2);
            this.ctx.fill();
        }

        // عيون الأنقليس
        this.drawEelEyes(fish);

        // شرارات كهربائية
        if (fish.type === 'eel') {
            this.drawElectricSparks(fish);
        }
    }

    // رسم الراي
    drawRay(fish) {
        // جسم الراي الواقعي - شكل مسطح مع أجنحة متوسطة
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.2);
        this.ctx.lineWidth = 1;

        // الجسم المركزي المسطح
        this.ctx.beginPath();
        this.ctx.ellipse(0, 0, fish.size * 0.6, fish.size * 0.3, 0, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // الأجنحة الجانبية - حجم متوسط وواقعي
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.1);
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);

        // الجناح الأيسر
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 0.2, -fish.size * 0.1);
        this.ctx.quadraticCurveTo(
            -fish.size * 0.6, -fish.size * 0.4,
            -fish.size * 0.8, -fish.size * 0.1
        );
        this.ctx.quadraticCurveTo(
            -fish.size * 0.7, fish.size * 0.15,
            -fish.size * 0.5, fish.size * 0.25
        );
        this.ctx.quadraticCurveTo(
            -fish.size * 0.3, fish.size * 0.2,
            -fish.size * 0.2, fish.size * 0.1
        );
        this.ctx.quadraticCurveTo(
            -fish.size * 0.25, 0,
            -fish.size * 0.2, -fish.size * 0.1
        );
        this.ctx.fill();
        this.ctx.stroke();

        // الجناح الأيمن
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.2, -fish.size * 0.1);
        this.ctx.quadraticCurveTo(
            fish.size * 0.6, -fish.size * 0.4,
            fish.size * 0.8, -fish.size * 0.1
        );
        this.ctx.quadraticCurveTo(
            fish.size * 0.7, fish.size * 0.15,
            fish.size * 0.5, fish.size * 0.25
        );
        this.ctx.quadraticCurveTo(
            fish.size * 0.3, fish.size * 0.2,
            fish.size * 0.2, fish.size * 0.1
        );
        this.ctx.quadraticCurveTo(
            fish.size * 0.25, 0,
            fish.size * 0.2, -fish.size * 0.1
        );
        this.ctx.fill();
        this.ctx.stroke();

        // البطن الفاتح
        this.ctx.fillStyle = '#f5f5f5';
        this.ctx.beginPath();
        this.ctx.ellipse(0, fish.size * 0.05, fish.size * 0.4, fish.size * 0.15, 0, 0, Math.PI * 2);
        this.ctx.fill();

        // خطوط الأجنحة للتفاصيل
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 0.5;
        for (let i = 0; i < 3; i++) {
            // خطوط الجناح الأيسر
            this.ctx.beginPath();
            this.ctx.moveTo(-fish.size * (0.3 + i * 0.15), -fish.size * 0.05);
            this.ctx.lineTo(-fish.size * (0.5 + i * 0.1), fish.size * 0.1);
            this.ctx.stroke();

            // خطوط الجناح الأيمن
            this.ctx.beginPath();
            this.ctx.moveTo(fish.size * (0.3 + i * 0.15), -fish.size * 0.05);
            this.ctx.lineTo(fish.size * (0.5 + i * 0.1), fish.size * 0.1);
            this.ctx.stroke();
        }

        // الذيل الطويل
        this.drawRayTail(fish);

        // عيون الراي
        this.drawRayEyes(fish);

        // نقاط على الجسم
        this.drawRaySpots(fish);
    }

    // رسم السلمون
    drawSalmon(fish) {
        // جسم السلمون الواقعي والقوي
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;

        // جسم السلمون الانسيابي
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.8, 0); // الرأس
        this.ctx.quadraticCurveTo(fish.size * 0.4, -fish.size * 0.6, 0, -fish.size * 0.5); // الظهر
        this.ctx.quadraticCurveTo(-fish.size * 0.6, -fish.size * 0.3, -fish.size, 0); // نهاية الجسم
        this.ctx.quadraticCurveTo(-fish.size * 0.6, fish.size * 0.3, 0, fish.size * 0.5); // البطن
        this.ctx.quadraticCurveTo(fish.size * 0.4, fish.size * 0.6, fish.size * 0.8, 0); // العودة للرأس
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();

        // البطن الفضي المميز للسلمون
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.5);
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.6, 0);
        this.ctx.quadraticCurveTo(fish.size * 0.2, fish.size * 0.4, -fish.size * 0.3, fish.size * 0.3);
        this.ctx.quadraticCurveTo(-fish.size * 0.5, 0, -fish.size * 0.3, -fish.size * 0.3);
        this.ctx.quadraticCurveTo(fish.size * 0.2, -fish.size * 0.4, fish.size * 0.6, 0);
        this.ctx.fill();

        // الخط الجانبي المميز للسلمون
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.5);
        this.ctx.lineWidth = 2;
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 0.7, 0);
        this.ctx.quadraticCurveTo(0, -fish.size * 0.05, fish.size * 0.6, 0);
        this.ctx.stroke();

        // نمط القشور الواقعي
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 0.5;
        for (let i = -3; i <= 2; i++) {
            for (let j = -1; j <= 1; j++) {
                const x = i * fish.size * 0.2;
                const y = j * fish.size * 0.15;

                // قشور متداخلة
                this.ctx.beginPath();
                this.ctx.moveTo(x + fish.size * 0.08, y);
                this.ctx.quadraticCurveTo(x, y - fish.size * 0.05, x - fish.size * 0.08, y);
                this.ctx.quadraticCurveTo(x, y + fish.size * 0.05, x + fish.size * 0.08, y);
                this.ctx.stroke();
            }
        }

        // زعنفة ظهرية مميزة للسلمون
        this.ctx.fillStyle = this.darkenColor(fish.color, 0.2);
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.4);
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.1, -fish.size * 0.5);
        this.ctx.quadraticCurveTo(
            fish.size * 0.3, -fish.size * 0.8,
            fish.size * 0.5, -fish.size * 0.6
        );
        this.ctx.quadraticCurveTo(
            fish.size * 0.4, -fish.size * 0.4,
            fish.size * 0.1, -fish.size * 0.5
        );
        this.ctx.fill();
        this.ctx.stroke();

        // الزعنفة الدهنية الصغيرة
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.1);
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 0.2, -fish.size * 0.4);
        this.ctx.quadraticCurveTo(
            -fish.size * 0.1, -fish.size * 0.5,
            0, -fish.size * 0.4
        );
        this.ctx.quadraticCurveTo(
            -fish.size * 0.1, -fish.size * 0.3,
            -fish.size * 0.2, -fish.size * 0.4
        );
        this.ctx.fill();
        this.ctx.stroke();

        // نقاط السلمون
        this.drawSalmonSpots(fish);

        // ذيل السلمون
        this.drawSalmonTail(fish);

        // زعانف السلمون
        this.drawSalmonFins(fish);

        // عيون السلمون
        this.drawSalmonEyes(fish);
    }

    drawFishBody(fish) {
        // جسم السمكة الواقعي
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;

        // الجسم الرئيسي - شكل سمكة واقعي
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.8, 0); // الرأس المدبب
        this.ctx.quadraticCurveTo(fish.size * 0.4, -fish.size * 0.6, 0, -fish.size * 0.5); // الظهر
        this.ctx.quadraticCurveTo(-fish.size * 0.6, -fish.size * 0.3, -fish.size, 0); // نهاية الجسم
        this.ctx.quadraticCurveTo(-fish.size * 0.6, fish.size * 0.3, 0, fish.size * 0.5); // البطن
        this.ctx.quadraticCurveTo(fish.size * 0.4, fish.size * 0.6, fish.size * 0.8, 0); // العودة للرأس
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();

        // البطن الفاتح
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.4);
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.6, 0);
        this.ctx.quadraticCurveTo(fish.size * 0.2, fish.size * 0.4, -fish.size * 0.3, fish.size * 0.2);
        this.ctx.quadraticCurveTo(-fish.size * 0.5, 0, -fish.size * 0.3, -fish.size * 0.2);
        this.ctx.quadraticCurveTo(fish.size * 0.2, -fish.size * 0.4, fish.size * 0.6, 0);
        this.ctx.fill();

        // الخط الجانبي المميز
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.5);
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 0.7, 0);
        this.ctx.quadraticCurveTo(0, -fish.size * 0.1, fish.size * 0.5, 0);
        this.ctx.stroke();

        // قشور صغيرة
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 0.5;
        for (let i = -2; i <= 2; i++) {
            for (let j = -1; j <= 1; j++) {
                const x = i * fish.size * 0.2;
                const y = j * fish.size * 0.15;
                this.ctx.beginPath();
                this.ctx.ellipse(x, y, fish.size * 0.05, fish.size * 0.03, 0, 0, Math.PI * 2);
                this.ctx.stroke();
            }
        }
    }

    drawFishTail(fish, angle) {
        const tailSize = fish.size * 0.7;
        const tailSway = Math.sin(fish.tailAngle) * 0.2;

        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;

        // ذيل واقعي مشقوق
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size, 0);

        // الجزء العلوي من الذيل
        this.ctx.quadraticCurveTo(
            -fish.size - tailSize * 0.3, -tailSize * 0.4 + tailSway,
            -fish.size - tailSize, -tailSize * 0.7 + tailSway
        );

        // منحنى العودة للوسط
        this.ctx.quadraticCurveTo(
            -fish.size - tailSize * 0.8, -tailSize * 0.1 + tailSway,
            -fish.size - tailSize * 0.6, 0 + tailSway
        );

        // الجزء السفلي من الذيل
        this.ctx.quadraticCurveTo(
            -fish.size - tailSize * 0.8, tailSize * 0.1 + tailSway,
            -fish.size - tailSize, tailSize * 0.7 + tailSway
        );

        this.ctx.quadraticCurveTo(
            -fish.size - tailSize * 0.3, tailSize * 0.4 + tailSway,
            -fish.size, 0
        );

        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();

        // خطوط الذيل للتفاصيل
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.4);
        this.ctx.lineWidth = 1;
        for (let i = 0; i < 3; i++) {
            this.ctx.beginPath();
            this.ctx.moveTo(-fish.size - tailSize * 0.2, -tailSize * 0.3 + i * tailSize * 0.3);
            this.ctx.lineTo(-fish.size - tailSize * 0.8, -tailSize * 0.4 + i * tailSize * 0.4 + tailSway);
            this.ctx.stroke();
        }
    }

    drawFishFins(fish) {
        const finSize = fish.size * 0.3;
        const finSway = Math.sin(fish.finAngle) * 0.15;

        this.ctx.fillStyle = this.lightenColor(fish.color, 0.2);
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 1;

        // الزعنفة الظهرية - واقعية ومدببة
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.1, -fish.size * 0.5);
        this.ctx.quadraticCurveTo(
            fish.size * 0.3, -fish.size * 0.9 + finSway,
            fish.size * 0.5, -fish.size * 0.7 + finSway
        );
        this.ctx.quadraticCurveTo(
            fish.size * 0.4, -fish.size * 0.4 + finSway,
            fish.size * 0.1, -fish.size * 0.5
        );
        this.ctx.fill();
        this.ctx.stroke();

        // الزعنفة الشرجية - صغيرة
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 0.2, fish.size * 0.4);
        this.ctx.quadraticCurveTo(
            -fish.size * 0.1, fish.size * 0.7 - finSway,
            fish.size * 0.1, fish.size * 0.6 - finSway
        );
        this.ctx.quadraticCurveTo(
            0, fish.size * 0.3 - finSway,
            -fish.size * 0.2, fish.size * 0.4
        );
        this.ctx.fill();
        this.ctx.stroke();

        // الزعانف الصدرية - شكل مروحي واقعي
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.4, -fish.size * 0.1);
        this.ctx.quadraticCurveTo(
            fish.size * 0.8, -fish.size * 0.4 + finSway,
            fish.size * 0.9, -fish.size * 0.1 + finSway
        );
        this.ctx.quadraticCurveTo(
            fish.size * 0.8, fish.size * 0.2 + finSway,
            fish.size * 0.4, fish.size * 0.1
        );
        this.ctx.quadraticCurveTo(
            fish.size * 0.5, 0,
            fish.size * 0.4, -fish.size * 0.1
        );
        this.ctx.fill();
        this.ctx.stroke();

        // الزعانف البطنية - صغيرة ومدببة
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.2, fish.size * 0.3);
        this.ctx.quadraticCurveTo(
            fish.size * 0.4, fish.size * 0.6 - finSway,
            fish.size * 0.5, fish.size * 0.4 - finSway
        );
        this.ctx.quadraticCurveTo(
            fish.size * 0.3, fish.size * 0.2 - finSway,
            fish.size * 0.2, fish.size * 0.3
        );
        this.ctx.fill();
        this.ctx.stroke();

        // خطوط الزعانف للتفاصيل
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.4);
        this.ctx.lineWidth = 0.5;

        // خطوط الزعنفة الظهرية
        for (let i = 0; i < 4; i++) {
            this.ctx.beginPath();
            this.ctx.moveTo(fish.size * (0.15 + i * 0.08), -fish.size * 0.5);
            this.ctx.lineTo(fish.size * (0.25 + i * 0.06), -fish.size * 0.8 + finSway);
            this.ctx.stroke();
        }

        // خطوط الزعنفة الصدرية
        for (let i = 0; i < 3; i++) {
            this.ctx.beginPath();
            this.ctx.moveTo(fish.size * 0.5, -fish.size * 0.05 + i * fish.size * 0.07);
            this.ctx.lineTo(fish.size * 0.85, -fish.size * 0.3 + i * fish.size * 0.15 + finSway);
            this.ctx.stroke();
        }
    }

    drawFishEyes(fish) {
        const eyeSize = fish.eyeSize;

        // العين الرئيسية
        this.ctx.fillStyle = '#ffffff';
        this.ctx.strokeStyle = '#000000';
        this.ctx.lineWidth = 1;

        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.3, -fish.size * 0.2, eyeSize, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // بؤبؤ العين
        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.35, -fish.size * 0.2, eyeSize * 0.6, 0, Math.PI * 2);
        this.ctx.fill();

        // بريق العين
        this.ctx.fillStyle = '#ffffff';
        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.4, -fish.size * 0.25, eyeSize * 0.3, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawPlayerName(fish) {
        if (!fish.playerName) return;

        this.ctx.save();
        this.ctx.rotate(-Math.atan2(fish.vy, fish.vx)); // إلغاء دوران النص

        this.ctx.fillStyle = '#ffffff';
        this.ctx.strokeStyle = '#000000';
        this.ctx.lineWidth = 2;
        this.ctx.font = `${Math.max(10, fish.size * 0.3)}px Tajawal`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';

        const text = fish.playerName;
        const y = -fish.size - 15;

        this.ctx.strokeText(text, 0, y);
        this.ctx.fillText(text, 0, y);

        this.ctx.restore();
    }

    drawHealthBar(fish) {
        this.ctx.save();
        this.ctx.rotate(-Math.atan2(fish.vy, fish.vx)); // إلغاء دوران شريط الصحة

        const barWidth = fish.size * 1.2;
        const barHeight = 6; // زيادة سماكة الشريط قليلاً
        const y = fish.size + 12;

        // خلفية الشريط
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.5)';
        this.ctx.fillRect(-barWidth/2, y, barWidth, barHeight);

        // الصحة الحالية - ألوان متدرجة
        const healthPercent = fish.health / fish.maxHealth;
        let healthColor;
        if (healthPercent > 0.7) {
            healthColor = '#00ff00'; // أخضر
        } else if (healthPercent > 0.4) {
            healthColor = '#ffff00'; // أصفر
        } else if (healthPercent > 0.2) {
            healthColor = '#ff8800'; // برتقالي
        } else {
            healthColor = '#ff0000'; // أحمر
        }

        this.ctx.fillStyle = healthColor;
        const healthWidth = healthPercent * barWidth;
        this.ctx.fillRect(-barWidth/2, y, healthWidth, barHeight);

        // إطار الشريط
        this.ctx.strokeStyle = '#ffffff';
        this.ctx.lineWidth = 1;
        this.ctx.strokeRect(-barWidth/2, y, barWidth, barHeight);

        // عرض رقم الصحة للأسماك في المعركة
        if (fish.isInBattle) {
            this.ctx.fillStyle = '#ffffff';
            this.ctx.font = `${Math.max(8, fish.size * 0.15)}px Arial`;
            this.ctx.textAlign = 'center';
            this.ctx.fillText(
                `${Math.ceil(fish.health)}/${fish.maxHealth}`,
                0,
                y + barHeight + 12
            );
        }

        this.ctx.restore();
    }

    checkFishCollisions() {
        for (let i = 0; i < this.fish.length; i++) {
            for (let j = i + 1; j < this.fish.length; j++) {
                const fish1 = this.fish[i];
                const fish2 = this.fish[j];

                const distance = this.getDistance(fish1, fish2);
                const minDistance = (fish1.size + fish2.size) * 0.8;

                if (distance < minDistance) {
                    this.handleFishCollision(fish1, fish2);
                }
            }
        }

        // تحديث المعارك النشطة
        this.updateActiveBattles();
    }

    handleFishCollision(fish1, fish2) {
        // التحقق من أن الأسماك تنتمي لمستخدمين مختلفين
        const samePlayer = fish1.playerName === fish2.playerName;

        if (samePlayer) {
            // أسماك نفس المستخدم - تصادم ودي فقط
            this.handleFriendlyCollision(fish1, fish2);
            return;
        }

        // أسماك مستخدمين مختلفين - قتال ديناميكي!
        const sizeDifference = Math.abs(fish1.size - fish2.size);
        const minSize = Math.min(fish1.size, fish2.size);

        // إذا كان الفرق كبير جداً، أكل فوري
        if (sizeDifference > minSize * 0.5) {
            const predator = fish1.size > fish2.size ? fish1 : fish2;
            const prey = fish1.size > fish2.size ? fish2 : fish1;
            this.eatFish(predator, prey);
        } else {
            // قتال ديناميكي بين أسماك متقاربة الحجم
            this.startBattle(fish1, fish2);
        }
    }

    // التصادم الودي بين أسماك نفس المستخدم
    handleFriendlyCollision(fish1, fish2) {
        // دفع بسيط وودي
        const dx = fish2.x - fish1.x;
        const dy = fish2.y - fish1.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance > 0) {
            const pushForce = 1; // قوة أقل للتصادم الودي
            const pushX = (dx / distance) * pushForce;
            const pushY = (dy / distance) * pushForce;

            fish1.vx -= pushX;
            fish1.vy -= pushY;
            fish2.vx += pushX;
            fish2.vy += pushY;
        }
    }

    // بدء معركة ديناميكية
    startBattle(fish1, fish2) {
        // التحقق من أن الأسماك ليست في معركة بالفعل
        if (fish1.isInBattle || fish2.isInBattle) return;

        // بدء المعركة
        fish1.isInBattle = true;
        fish2.isInBattle = true;
        fish1.battleTarget = fish2;
        fish2.battleTarget = fish1;

        // تأثيرات بصرية للمعركة
        this.createBattleEffect(fish1.x, fish1.y);
        this.createBattleEffect(fish2.x, fish2.y);

        // تشغيل صوت المعركة
        if (this.gameSettings.soundEnabled) {
            this.playBattleSound();
        }

        // تحديث الإحصائيات
        this.gameState.activeBattles++;

        console.log(`⚔️ معركة بدأت بين ${fish1.playerName} (${fish1.typeName}) و ${fish2.playerName} (${fish2.typeName})`);
    }

    // تحديث المعارك النشطة
    updateActiveBattles() {
        for (let fish of this.fish) {
            if (fish.isInBattle && fish.battleTarget) {
                this.updateBattle(fish);
            }
        }
    }

    // تحديث معركة واحدة
    updateBattle(fish) {
        const target = fish.battleTarget;

        // التحقق من أن الهدف ما زال موجود
        if (!target || !this.fish.includes(target) || target.health <= 0) {
            this.endBattle(fish);
            return;
        }

        // التحقق من المسافة - إنهاء المعركة إذا ابتعدا كثيراً
        const distance = this.getDistance(fish, target);
        if (distance > (fish.size + target.size) * 2) {
            this.endBattle(fish);
            this.endBattle(target);
            return;
        }

        // تنفيذ هجوم إذا حان الوقت
        const currentTime = Date.now();
        if (currentTime - fish.lastAttackTime > fish.attackCooldown) {
            this.performAttack(fish, target);
            fish.lastAttackTime = currentTime;
        }
    }

    // تنفيذ هجوم
    performAttack(attacker, defender) {
        // حساب الضرر
        const baseDamage = attacker.attackPower;
        const defense = defender.defense;
        const actualDamage = Math.max(1, baseDamage - defense + Math.random() * 10 - 5);

        // تطبيق الضرر
        defender.health -= actualDamage;

        // تأثيرات بصرية للهجوم
        this.createAttackEffect(defender.x, defender.y, actualDamage);

        // تشغيل صوت الهجوم
        if (this.gameSettings.soundEnabled) {
            this.playAttackSound(attacker.type, actualDamage);
        }

        console.log(`⚔️ ${attacker.playerName} هاجم ${defender.playerName} بضرر ${actualDamage.toFixed(1)}`);

        // فحص الموت
        if (defender.health <= 0) {
            this.handleBattleDeath(attacker, defender);
        }
    }

    // التعامل مع الموت في المعركة
    handleBattleDeath(winner, loser) {
        // إنهاء المعركة
        this.endBattle(winner);
        this.endBattle(loser);

        // أكل الخاسر
        this.eatFish(winner, loser);

        // تحديث الإحصائيات
        this.gameState.totalDeaths++;

        console.log(`💀 ${loser.playerName} مات في المعركة! ${winner.playerName} انتصر!`);
    }

    // إنهاء المعركة
    endBattle(fish) {
        if (fish.isInBattle) {
            fish.isInBattle = false;
            fish.battleTarget = null;
            this.gameState.activeBattles = Math.max(0, this.gameState.activeBattles - 1);
        }
    }

    eatFish(predator, prey) {
        // إنشاء تأثير الأكل
        this.createEatEffect(prey.x, prey.y, prey.size);

        // تشغيل صوت الأكل
        this.playEatingSound(predator.type, predator.size);

        // نمو المفترس - أبطأ بكثير وأكثر واقعية
        const sizeDifference = prey.size;
        let growthAmount;

        // حساب النمو بناءً على حجم الفريسة والمفترس (نسب أقل بكثير)
        if (predator.size > prey.size * 2) {
            // إذا كان المفترس أكبر بكثير، نمو قليل جداً
            growthAmount = sizeDifference * 0.01; // قلل من 0.05 إلى 0.01
        } else if (predator.size > prey.size) {
            // إذا كان المفترس أكبر قليلاً، نمو قليل
            growthAmount = sizeDifference * 0.025; // قلل من 0.1 إلى 0.025
        } else {
            // إذا كانا متقاربين في الحجم، نمو متوسط
            growthAmount = sizeDifference * 0.04; // قلل من 0.15 إلى 0.04
        }

        // تحديد الحد الأقصى للنمو في المرة الواحدة (أقل بكثير)
        const maxGrowthPerEat = predator.baseSize * 0.08; // قلل من 0.2 إلى 0.08
        growthAmount = Math.min(growthAmount, maxGrowthPerEat);

        predator.size += growthAmount;
        predator.baseSize = predator.size;

        // تحسين الصحة والطاقة بشكل متدرج (أبطأ)
        const healthGain = Math.min(10, prey.size * 0.2); // قلل من 20 و 0.5 إلى 10 و 0.2
        const energyGain = Math.min(15, prey.size * 0.3); // قلل من 30 و 0.8 إلى 15 و 0.3

        predator.maxHealth += healthGain * 0.2; // قلل من 0.5 إلى 0.2
        predator.health = Math.min(predator.maxHealth, predator.health + healthGain);
        predator.energy = Math.min(predator.maxEnergy, predator.energy + energyGain);
        predator.kills++;
        predator.foodEaten++;

        // تحديث لون المفترس حسب الحجم الجديد
        predator.color = this.getFishColor(predator.size, predator.type);

        // إذا كانت الفريسة هي السمكة المتابعة، ابحث عن سمكة كبيرة أخرى
        if (this.camera.followTarget === prey && this.autoFollowEnabled) {
            this.camera.followTarget = null;
            console.log('🎯 السمكة المتابعة تم أكلها، البحث عن سمكة كبيرة أخرى...');
        }

        // إزالة الفريسة
        const preyIndex = this.fish.indexOf(prey);
        if (preyIndex > -1) {
            this.fish.splice(preyIndex, 1);
        }

        // تحديث الإحصائيات
        this.gameState.battles++;

        // تحديث إحصائيات اللاعب
        this.updatePlayerStats(predator.playerName, 0, 1);

        console.log(`🦈 ${predator.playerName} أكل سمكة ${prey.playerName}! نمو: +${growthAmount.toFixed(1)}`);
    }

    killFish(index) {
        const fish = this.fish[index];

        // إذا كانت السمكة المتابعة هي التي ماتت، ابحث عن سمكة كبيرة أخرى
        if (this.camera.followTarget === fish && this.autoFollowEnabled) {
            this.camera.followTarget = null;
            console.log('🎯 السمكة المتابعة ماتت، البحث عن سمكة كبيرة أخرى...');
        }

        this.createDeathEffect(fish.x, fish.y, fish.size);
        this.fish.splice(index, 1);
        console.log(`💀 ماتت سمكة ${fish.playerName}`);
    }

    createSpawnEffect(x, y, fishType = 'normal') {
        const colors = {
            normal: '#00ff7f',
            shark: '#ff4444',
            whale: '#4444ff',
            dolphin: '#44ffff',
            eel: '#ffff44',
            salmon: '#ff8844',
            ray: '#ff44ff'
        };

        const particleCount = fishType === 'whale' ? 20 : fishType === 'shark' ? 15 : 10;
        const effectColor = colors[fishType] || colors.normal;

        for (let i = 0; i < particleCount; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 6,
                vy: (Math.random() - 0.5) * 6,
                size: 2 + Math.random() * 5,
                color: effectColor,
                life: 80,
                maxLife: 80,
                type: 'spawn'
            });
        }

        // تأثير إضافي للأسماك النادرة
        if (['whale', 'shark', 'eel'].includes(fishType)) {
            for (let i = 0; i < 5; i++) {
                this.particles.push({
                    x: x,
                    y: y,
                    vx: (Math.random() - 0.5) * 2,
                    vy: (Math.random() - 0.5) * 2,
                    size: 8 + Math.random() * 12,
                    color: effectColor,
                    life: 120,
                    maxLife: 120,
                    type: 'glow'
                });
            }
        }
    }

    createEatEffect(x, y, size) {
        for (let i = 0; i < 15; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 6,
                vy: (Math.random() - 0.5) * 6,
                size: 1 + Math.random() * 3,
                color: '#ff6b6b',
                life: 40,
                maxLife: 40,
                type: 'eat'
            });
        }
    }

    createDeathEffect(x, y, size) {
        for (let i = 0; i < 20; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 8,
                vy: (Math.random() - 0.5) * 8,
                size: 1 + Math.random() * 2,
                color: '#666666',
                life: 80,
                maxLife: 80,
                type: 'death'
            });
        }
    }

    // تأثيرات المعارك الجديدة
    createBattleEffect(x, y) {
        // شرارات المعركة
        for (let i = 0; i < 12; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 8,
                vy: (Math.random() - 0.5) * 8,
                size: 2 + Math.random() * 4,
                color: '#ff4444',
                life: 60,
                maxLife: 60,
                type: 'battle'
            });
        }

        // دوائر الطاقة
        for (let i = 0; i < 3; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 2,
                vy: (Math.random() - 0.5) * 2,
                size: 8 + Math.random() * 6,
                color: '#ffaa00',
                life: 40,
                maxLife: 40,
                type: 'energy'
            });
        }
    }

    createAttackEffect(x, y, damage) {
        // تأثير الهجوم حسب قوة الضرر
        const particleCount = Math.min(15, Math.max(5, Math.floor(damage / 2)));
        const effectColor = damage > 20 ? '#ff0000' : damage > 10 ? '#ff6600' : '#ffaa00';

        for (let i = 0; i < particleCount; i++) {
            this.particles.push({
                x: x,
                y: y,
                vx: (Math.random() - 0.5) * 10,
                vy: (Math.random() - 0.5) * 10,
                size: 1 + Math.random() * 3,
                color: effectColor,
                life: 30,
                maxLife: 30,
                type: 'attack'
            });
        }

        // رقم الضرر المتطاير
        this.particles.push({
            x: x,
            y: y,
            vx: (Math.random() - 0.5) * 2,
            vy: -2 - Math.random() * 2,
            size: 12,
            color: effectColor,
            life: 90,
            maxLife: 90,
            type: 'damage_text',
            text: Math.floor(damage).toString()
        });
    }

    updateAndDrawParticles() {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            const particle = this.particles[i];

            // تحديث الموقع
            particle.x += particle.vx;
            particle.y += particle.vy;

            // تطبيق الجاذبية والمقاومة
            particle.vy += this.physics.gravity * 0.1;
            particle.vx *= this.physics.friction;
            particle.vy *= this.physics.friction;

            // تقليل الحياة
            particle.life--;

            // رسم الجسيم
            this.ctx.save();
            this.ctx.globalAlpha = particle.life / particle.maxLife;

            if (particle.type === 'damage_text' && particle.text) {
                // رسم نص الضرر
                this.ctx.fillStyle = particle.color;
                this.ctx.font = `bold ${particle.size}px Arial`;
                this.ctx.textAlign = 'center';
                this.ctx.fillText(particle.text, particle.x, particle.y);

                // إضافة حدود للنص
                this.ctx.strokeStyle = '#000000';
                this.ctx.lineWidth = 1;
                this.ctx.strokeText(particle.text, particle.x, particle.y);
            } else {
                // رسم جسيم عادي
                this.ctx.fillStyle = particle.color;
                this.ctx.beginPath();
                this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
                this.ctx.fill();
            }

            this.ctx.restore();

            // إزالة الجسيمات الميتة
            if (particle.life <= 0) {
                this.particles.splice(i, 1);
            }
        }
    }

    updatePhysics() {
        // تحديث التيارات المائية
        const time = Date.now() * 0.001;
        this.physics.currentStrength = 0.3 + Math.sin(time * 0.5) * 0.2;

        // تطبيق التيارات على الأسماك
        this.fish.forEach(fish => {
            const currentX = Math.sin(time + fish.y * 0.01) * this.physics.currentStrength;
            const currentY = Math.cos(time + fish.x * 0.01) * this.physics.currentStrength * 0.5;

            fish.vx += currentX * 0.1;
            fish.vy += currentY * 0.1;
        });
    }

    updateFPS() {
        this.gameState.frameCount++;
        const now = Date.now();

        if (now - this.gameState.lastFpsUpdate >= 1000) {
            this.gameState.fps = this.gameState.frameCount;
            this.gameState.frameCount = 0;
            this.gameState.lastFpsUpdate = now;
        }
    }

    updateUI() {
        // تحديث الإحصائيات الأساسية
        document.getElementById('totalFish').textContent = this.fish.length;
        document.getElementById('battles').textContent = this.gameState.battles;
        document.getElementById('rosesReceived').textContent = this.gameState.rosesReceived;
        document.getElementById('fps').textContent = this.gameState.fps;

        // تحديث الإحصائيات الجديدة للقتال
        const activeBattlesElement = document.getElementById('activeBattles');
        const totalDeathsElement = document.getElementById('totalDeaths');

        if (activeBattlesElement) {
            activeBattlesElement.textContent = this.gameState.activeBattles;
        }
        if (totalDeathsElement) {
            totalDeathsElement.textContent = this.gameState.totalDeaths;
        }

        // تحديث عرض الدقة
        const resolutionElement = document.getElementById('resolution');
        if (resolutionElement) {
            resolutionElement.textContent = `${this.gameWidth}x${this.gameHeight}`;
        }

        // تحديث الإحصائيات المتقدمة
        const fishTypes = new Set(this.fish.map(f => f.type)).size;
        const biggestFish = this.fish.reduce((max, fish) => fish.size > max ? fish.size : max, 0);

        const fishTypesElement = document.getElementById('fishTypes');
        const biggestFishElement = document.getElementById('biggestFish');

        if (fishTypesElement) fishTypesElement.textContent = fishTypes;
        if (biggestFishElement) biggestFishElement.textContent = Math.floor(biggestFish);

        // تحديث وقت اللعب
        const gameTime = Math.floor((Date.now() - this.gameState.startTime) / 1000);
        const minutes = Math.floor(gameTime / 60);
        const seconds = gameTime % 60;
        document.getElementById('gameTime').textContent =
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

        // تحديث قائمة اللاعبين
        this.updatePlayersUI();

        // تحديث الإنجازات
        this.updateAchievementsUI();

        // تحديث كل ثانية
        setTimeout(() => this.updateUI(), 1000);
    }

    updatePlayersUI() {
        const playersList = document.getElementById('playersList');

        if (this.players.size === 0) {
            playersList.innerHTML = `
                <div class="player-item">
                    <span class="player-name">في انتظار اللاعبين...</span>
                    <span class="player-fish-count">0</span>
                </div>
            `;
            return;
        }

        // ترتيب اللاعبين حسب عدد الأسماك
        const sortedPlayers = Array.from(this.players.entries())
            .sort((a, b) => b[1].fishCount - a[1].fishCount);

        // عرض أفضل 20 لاعب فقط لتحسين الأداء
        const topPlayers = sortedPlayers.slice(0, 20);

        let playersHTML = topPlayers.map(([name, stats], index) => {
            const playerIndex = Array.from(this.players.keys()).indexOf(name);
            const playerColor = this.playerColors[playerIndex % this.maxPlayers];
            const rank = index + 1;
            const rankIcon = rank === 1 ? '👑' : rank === 2 ? '🥈' : rank === 3 ? '🥉' : `${rank}.`;

            return `
                <div class="player-item" style="border-left: 3px solid ${playerColor};">
                    <span class="player-rank">${rankIcon}</span>
                    <span class="player-name" title="${name}">${name.length > 12 ? name.substring(0, 12) + '...' : name}</span>
                    <span class="player-fish-count">${stats.fishCount}</span>
                </div>
            `;
        }).join('');

        // إضافة إحصائيات إجمالية إذا كان هناك أكثر من 20 لاعب
        if (sortedPlayers.length > 20) {
            playersHTML += `
                <div class="player-item" style="border-top: 1px solid rgba(255,255,255,0.2); margin-top: 5px; padding-top: 5px;">
                    <span class="player-name">... و ${sortedPlayers.length - 20} لاعب آخر</span>
                    <span class="player-fish-count">📊</span>
                </div>
            `;
        }

        playersList.innerHTML = playersHTML;
    }

    updateAchievementsUI() {
        // هذه الوظيفة ستتم إضافتها لاحقاً
    }

    updatePlayerStats(playerName, roseCount = 0, kills = 0) {
        if (!this.players.has(playerName)) {
            this.players.set(playerName, {
                fishCount: 0,
                totalRoses: 0,
                totalKills: 0,
                joinTime: Date.now()
            });
        }

        const stats = this.players.get(playerName);

        if (roseCount > 0) {
            stats.fishCount++;
            stats.totalRoses += roseCount;
        }

        if (kills > 0) {
            stats.totalKills += kills;
        }

        // حساب عدد الأسماك الحالية للاعب
        stats.fishCount = this.fish.filter(fish => fish.playerName === playerName).length;
    }

    // وظائف مساعدة
    getDistance(obj1, obj2) {
        const dx = obj1.x - obj2.x;
        const dy = obj1.y - obj2.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    darkenColor(color, amount) {
        const hex = color.replace('#', '');
        const r = Math.max(0, parseInt(hex.substr(0, 2), 16) - Math.floor(255 * amount));
        const g = Math.max(0, parseInt(hex.substr(2, 2), 16) - Math.floor(255 * amount));
        const b = Math.max(0, parseInt(hex.substr(4, 2), 16) - Math.floor(255 * amount));
        return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    }

    lightenColor(color, amount) {
        const hex = color.replace('#', '');
        const r = Math.min(255, parseInt(hex.substr(0, 2), 16) + Math.floor(255 * amount));
        const g = Math.min(255, parseInt(hex.substr(2, 2), 16) + Math.floor(255 * amount));
        const b = Math.min(255, parseInt(hex.substr(4, 2), 16) + Math.floor(255 * amount));
        return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
    }

    resizeCanvas() {
        // تحديث الدقة فقط إذا لم تكن محددة يدوي<|im_start|>
        if (!this.gameSettings.gameResolution || this.gameSettings.gameResolution === 'auto') {
            this.gameWidth = window.innerWidth;
            this.gameHeight = window.innerHeight;
            this.canvas.width = this.gameWidth;
            this.canvas.height = this.gameHeight;
            console.log(`🖥️ تم تحديث دقة النافذة تلقائي<|im_start|>: ${this.gameWidth}x${this.gameHeight}`);
        }
    }

    // وظائف مساعدة لرسم تفاصيل الأسماك
    drawSharkTail(fish) {
        const tailSize = fish.size * 0.8;
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;

        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size, 0);
        this.ctx.lineTo(-fish.size - tailSize, -tailSize * 0.6);
        this.ctx.lineTo(-fish.size - tailSize * 0.7, 0);
        this.ctx.lineTo(-fish.size - tailSize, tailSize * 0.3);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();
    }

    drawSharkEyes(fish) {
        const eyeSize = fish.size * 0.12;

        // عين القرش - أكثر شراسة
        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.4, -fish.size * 0.15, eyeSize, 0, Math.PI * 2);
        this.ctx.fill();

        // بريق العين
        this.ctx.fillStyle = '#ffffff';
        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.42, -fish.size * 0.17, eyeSize * 0.3, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawSharkTeeth(fish) {
        this.ctx.fillStyle = '#ffffff';
        this.ctx.strokeStyle = '#dddddd';
        this.ctx.lineWidth = 1;

        // الفك العلوي - أسنان كرتونية لطيفة
        for (let i = 0; i < 4; i++) {
            const x = fish.size * (0.5 + i * 0.08);
            const y = fish.size * 0.1;

            this.ctx.beginPath();
            this.ctx.moveTo(x, y);
            this.ctx.lineTo(x + fish.size * 0.02, y + fish.size * 0.06);
            this.ctx.lineTo(x - fish.size * 0.02, y + fish.size * 0.06);
            this.ctx.closePath();
            this.ctx.fill();
            this.ctx.stroke();
        }

        // الفك السفلي - أسنان أصغر
        for (let i = 0; i < 3; i++) {
            const x = fish.size * (0.52 + i * 0.08);
            const y = fish.size * 0.18;

            this.ctx.beginPath();
            this.ctx.moveTo(x, y);
            this.ctx.lineTo(x + fish.size * 0.015, y - fish.size * 0.04);
            this.ctx.lineTo(x - fish.size * 0.015, y - fish.size * 0.04);
            this.ctx.closePath();
            this.ctx.fill();
            this.ctx.stroke();
        }
    }

    drawWhaleTail(fish) {
        const tailSize = fish.size * 1.2;
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 3;

        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size, 0);
        this.ctx.lineTo(-fish.size - tailSize, -tailSize * 0.5);
        this.ctx.lineTo(-fish.size - tailSize * 0.8, 0);
        this.ctx.lineTo(-fish.size - tailSize, tailSize * 0.5);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();
    }

    drawWhaleEyes(fish) {
        const eyeSize = fish.size * 0.1;

        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.3, -fish.size * 0.2, eyeSize, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.fillStyle = '#ffffff';
        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.32, -fish.size * 0.22, eyeSize * 0.4, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawWhaleSpout(fish) {
        // نافورة الماء من الحوت
        this.ctx.strokeStyle = '#87CEEB';
        this.ctx.lineWidth = 2;

        for (let i = 0; i < 3; i++) {
            this.ctx.beginPath();
            this.ctx.moveTo(fish.size * 0.1, -fish.size * 0.7);
            this.ctx.lineTo(fish.size * 0.1 + (i - 1) * 5, -fish.size * 1.2);
            this.ctx.stroke();
        }
    }

    drawDolphinTail(fish) {
        const tailSize = fish.size * 0.7;
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;

        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size, 0);
        this.ctx.quadraticCurveTo(-fish.size - tailSize, -tailSize * 0.6, -fish.size - tailSize * 0.8, -tailSize * 0.2);
        this.ctx.quadraticCurveTo(-fish.size - tailSize * 0.6, 0, -fish.size - tailSize * 0.8, tailSize * 0.2);
        this.ctx.quadraticCurveTo(-fish.size - tailSize, tailSize * 0.6, -fish.size, 0);
        this.ctx.fill();
        this.ctx.stroke();
    }

    drawDolphinEyes(fish) {
        const eyeSize = fish.size * 0.08;

        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.5, -fish.size * 0.1, eyeSize, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.fillStyle = '#ffffff';
        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.52, -fish.size * 0.12, eyeSize * 0.4, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawEelEyes(fish) {
        const eyeSize = fish.size * 0.06;

        this.ctx.fillStyle = '#ffff00';
        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.7, -fish.size * 0.1, eyeSize, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.7, -fish.size * 0.1, eyeSize * 0.6, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawElectricSparks(fish) {
        this.ctx.strokeStyle = '#ffff00';
        this.ctx.lineWidth = 1;

        for (let i = 0; i < 5; i++) {
            const angle = (i / 5) * Math.PI * 2;
            const length = fish.size * 0.3;

            this.ctx.beginPath();
            this.ctx.moveTo(0, 0);
            this.ctx.lineTo(
                Math.cos(angle) * length + (Math.random() - 0.5) * 10,
                Math.sin(angle) * length + (Math.random() - 0.5) * 10
            );
            this.ctx.stroke();
        }
    }

    drawRayTail(fish) {
        this.ctx.strokeStyle = fish.color;
        this.ctx.lineWidth = fish.size * 0.1;
        this.ctx.lineCap = 'round';

        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 1.2, 0);
        this.ctx.lineTo(-fish.size * 2, fish.size * 0.1);
        this.ctx.stroke();
    }

    drawRayEyes(fish) {
        const eyeSize = fish.size * 0.08;

        // عيون على الجانب العلوي
        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.3, -fish.size * 0.1, eyeSize, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.1, -fish.size * 0.1, eyeSize, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawRaySpots(fish) {
        this.ctx.fillStyle = this.darkenColor(fish.color, 0.3);

        for (let i = 0; i < 8; i++) {
            const x = (Math.random() - 0.5) * fish.size * 1.5;
            const y = (Math.random() - 0.5) * fish.size * 0.4;
            const size = fish.size * 0.05;

            this.ctx.beginPath();
            this.ctx.arc(x, y, size, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }

    drawSalmonSpots(fish) {
        this.ctx.fillStyle = this.darkenColor(fish.color, 0.4);

        for (let i = 0; i < 6; i++) {
            const x = (Math.random() - 0.5) * fish.size * 1.2;
            const y = (Math.random() - 0.5) * fish.size * 0.8;
            const size = fish.size * 0.03;

            this.ctx.beginPath();
            this.ctx.arc(x, y, size, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }

    drawSalmonTail(fish) {
        const tailSize = fish.size * 0.6;
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;

        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size, 0);
        this.ctx.lineTo(-fish.size - tailSize, -tailSize * 0.4);
        this.ctx.lineTo(-fish.size - tailSize * 0.8, 0);
        this.ctx.lineTo(-fish.size - tailSize, tailSize * 0.4);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();
    }

    drawSalmonFins(fish) {
        const finSize = fish.size * 0.3;
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.2);
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 1;

        // زعنفة علوية
        this.ctx.beginPath();
        this.ctx.moveTo(0, -fish.size * 0.6);
        this.ctx.lineTo(finSize * 0.5, -fish.size - finSize);
        this.ctx.lineTo(-finSize * 0.5, -fish.size - finSize * 0.5);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();
    }

    drawSalmonEyes(fish) {
        const eyeSize = fish.size * 0.1;

        this.ctx.fillStyle = '#ffffff';
        this.ctx.strokeStyle = '#000000';
        this.ctx.lineWidth = 1;

        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.3, -fish.size * 0.2, eyeSize, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.35, -fish.size * 0.2, eyeSize * 0.6, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawFishPattern(fish) {
        // وظيفة فارغة للتوافق - لا تفعل شيء
    }

    // رسم قنديل البحر
    drawJellyfish(fish) {
        // جسم قنديل البحر - شكل قبة
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;

        // القبة الرئيسية - شكل بيضاوي بدلاً من دائرة
        this.ctx.beginPath();
        this.ctx.ellipse(0, 0, fish.size * 0.8, fish.size * 0.6, 0, 0, Math.PI);
        this.ctx.fill();
        this.ctx.stroke();

        // نمط شفاف داخلي - شكل بيضاوي
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.3);
        this.ctx.globalAlpha = 0.6;
        this.ctx.beginPath();
        this.ctx.ellipse(0, -fish.size * 0.2, fish.size * 0.5, fish.size * 0.3, 0, 0, Math.PI);
        this.ctx.fill();
        this.ctx.globalAlpha = 1;

        // المجسات (اللوامس)
        this.drawJellyfishTentacles(fish);

        // نقاط لامعة
        this.ctx.fillStyle = '#ffffff';
        this.ctx.globalAlpha = 0.8;
        for (let i = 0; i < 5; i++) {
            const angle = (i / 5) * Math.PI;
            const x = Math.cos(angle) * fish.size * 0.4;
            const y = Math.sin(angle) * fish.size * 0.3 - fish.size * 0.2;
            this.ctx.beginPath();
            this.ctx.arc(x, y, 2, 0, Math.PI * 2);
            this.ctx.fill();
        }
        this.ctx.globalAlpha = 1;

        // تأثير السم (إذا كان نشطاً)
        if (fish.special === 'poison_sting' && fish.isWild) {
            this.drawPoisonEffect(fish);
        }
    }

    // رسم مجسات قنديل البحر
    drawJellyfishTentacles(fish) {
        this.ctx.strokeStyle = fish.color;
        this.ctx.lineWidth = 3;
        this.ctx.lineCap = 'round';

        const tentacleCount = 8;
        for (let i = 0; i < tentacleCount; i++) {
            const angle = (i / tentacleCount) * Math.PI - Math.PI/2;
            const startX = Math.cos(angle) * fish.size * 0.7;
            const startY = Math.sin(angle) * fish.size * 0.3;

            // رسم مجس متموج
            this.ctx.beginPath();
            this.ctx.moveTo(startX, startY);

            const segments = 6;
            for (let j = 1; j <= segments; j++) {
                const segmentRatio = j / segments;
                const waveOffset = Math.sin(fish.age * 0.1 + i + j) * fish.size * 0.2;
                const x = startX + waveOffset;
                const y = startY + segmentRatio * fish.size * 1.5;
                this.ctx.lineTo(x, y);
            }
            this.ctx.stroke();
        }
    }

    // رسم تأثير السم
    drawPoisonEffect(fish) {
        this.ctx.strokeStyle = '#00ff00';
        this.ctx.lineWidth = 1;
        this.ctx.globalAlpha = 0.6;

        for (let i = 0; i < 3; i++) {
            this.ctx.beginPath();
            this.ctx.arc(0, 0, fish.size * (1 + i * 0.3), 0, Math.PI * 2);
            this.ctx.stroke();
        }
        this.ctx.globalAlpha = 1;
    }

    // رسم الأخطبوط
    drawOctopus(fish) {
        // رأس الأخطبوط
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;

        // الجسم الرئيسي - شكل بيضاوي
        this.ctx.beginPath();
        this.ctx.ellipse(0, 0, fish.size * 0.7, fish.size * 0.9, 0, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // نمط على الرأس - نقاط بيضاوية بدلاً من دوائر
        this.ctx.fillStyle = this.darkenColor(fish.color, 0.2);
        for (let i = 0; i < 6; i++) {
            const angle = (i / 6) * Math.PI * 2;
            const x = Math.cos(angle) * fish.size * 0.3;
            const y = Math.sin(angle) * fish.size * 0.3;
            this.ctx.beginPath();
            this.ctx.ellipse(x, y, fish.size * 0.08, fish.size * 0.06, 0, 0, Math.PI * 2);
            this.ctx.fill();
        }

        // الأذرع (8 أذرع)
        this.drawOctopusArms(fish);

        // العيون الكبيرة
        this.drawOctopusEyes(fish);

        // تأثير الإمساك (إذا كان نشطاً)
        if (fish.special === 'tentacle_grab' && fish.isHunting) {
            this.drawGrabEffect(fish);
        }
    }

    // رسم أذرع الأخطبوط
    drawOctopusArms(fish) {
        this.ctx.strokeStyle = fish.color;
        this.ctx.lineWidth = fish.size * 0.15;
        this.ctx.lineCap = 'round';

        const armCount = 8;
        for (let i = 0; i < armCount; i++) {
            const angle = (i / armCount) * Math.PI * 2;
            const startX = Math.cos(angle) * fish.size * 0.6;
            const startY = Math.sin(angle) * fish.size * 0.6;

            // رسم ذراع منحني
            this.ctx.beginPath();
            this.ctx.moveTo(startX, startY);

            const segments = 5;
            for (let j = 1; j <= segments; j++) {
                const segmentRatio = j / segments;
                const curve = Math.sin(fish.age * 0.08 + i + j * 0.5) * fish.size * 0.3;
                const extension = segmentRatio * fish.size * 1.2;

                const x = startX + Math.cos(angle + curve * 0.01) * extension;
                const y = startY + Math.sin(angle + curve * 0.01) * extension;

                this.ctx.lineTo(x, y);
            }
            this.ctx.stroke();

            // مصاصات على الذراع
            this.drawSuckers(fish, angle, startX, startY);
        }
    }

    // رسم المصاصات على أذرع الأخطبوط - نقاط بيضاوية بدلاً من دوائر
    drawSuckers(fish, armAngle, startX, startY) {
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.3);

        const suckerCount = 4;
        for (let i = 1; i <= suckerCount; i++) {
            const ratio = i / suckerCount;
            const extension = ratio * fish.size * 1.2;
            const x = startX + Math.cos(armAngle) * extension;
            const y = startY + Math.sin(armAngle) * extension;

            this.ctx.beginPath();
            this.ctx.ellipse(x, y, fish.size * 0.05, fish.size * 0.03, 0, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }

    // رسم عيون الأخطبوط
    drawOctopusEyes(fish) {
        const eyeSize = fish.size * 0.15;

        // العين اليسرى
        this.ctx.fillStyle = '#ffffff';
        this.ctx.beginPath();
        this.ctx.arc(-fish.size * 0.25, -fish.size * 0.3, eyeSize, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.arc(-fish.size * 0.22, -fish.size * 0.3, eyeSize * 0.7, 0, Math.PI * 2);
        this.ctx.fill();

        // العين اليمنى
        this.ctx.fillStyle = '#ffffff';
        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.25, -fish.size * 0.3, eyeSize, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.22, -fish.size * 0.3, eyeSize * 0.7, 0, Math.PI * 2);
        this.ctx.fill();

        // بريق العيون
        this.ctx.fillStyle = '#ffffff';
        this.ctx.beginPath();
        this.ctx.arc(-fish.size * 0.2, -fish.size * 0.32, eyeSize * 0.3, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.2, -fish.size * 0.32, eyeSize * 0.3, 0, Math.PI * 2);
        this.ctx.fill();
    }

    // رسم تأثير الإمساك - خطوط بدلاً من دوائر
    drawGrabEffect(fish) {
        this.ctx.strokeStyle = '#ff0000';
        this.ctx.lineWidth = 2;
        this.ctx.globalAlpha = 0.7;

        // خطوط تمدد للإمساك بدلاً من دوائر
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            this.ctx.beginPath();
            this.ctx.moveTo(Math.cos(angle) * fish.size, Math.sin(angle) * fish.size);
            this.ctx.lineTo(Math.cos(angle) * fish.size * 2, Math.sin(angle) * fish.size * 2);
            this.ctx.stroke();
        }
        this.ctx.globalAlpha = 1;
    }

    // رسم حصان البحر
    drawSeahorse(fish) {
        // جسم حصان البحر المنحني
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;

        // الجسم الرئيسي - شكل منحني
        this.ctx.beginPath();
        this.ctx.moveTo(0, -fish.size * 0.8);
        this.ctx.quadraticCurveTo(-fish.size * 0.3, -fish.size * 0.4, -fish.size * 0.2, 0);
        this.ctx.quadraticCurveTo(-fish.size * 0.1, fish.size * 0.4, 0, fish.size * 0.8);
        this.ctx.quadraticCurveTo(fish.size * 0.2, fish.size * 0.4, fish.size * 0.3, 0);
        this.ctx.quadraticCurveTo(fish.size * 0.2, -fish.size * 0.4, 0, -fish.size * 0.8);
        this.ctx.fill();
        this.ctx.stroke();

        // الرأس - شكل بيضاوي بدلاً من دائرة
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.4, -fish.size * 0.6, fish.size * 0.25, fish.size * 0.2, 0, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // العين - شكل بيضاوي
        this.ctx.fillStyle = '#ffffff';
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.45, -fish.size * 0.65, fish.size * 0.08, fish.size * 0.06, 0, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.43, -fish.size * 0.65, fish.size * 0.05, fish.size * 0.04, 0, 0, Math.PI * 2);
        this.ctx.fill();

        // الزعانف
        this.drawSeahorseFinns(fish);

        // التاج الملكي
        if (fish.special === 'royal_grace') {
            this.drawRoyalCrown(fish);
        }
    }

    drawSeahorseFinns(fish) {
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.3);

        // الزعنفة الظهرية
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.1, -fish.size * 0.2);
        this.ctx.quadraticCurveTo(fish.size * 0.4, -fish.size * 0.3, fish.size * 0.3, 0);
        this.ctx.quadraticCurveTo(fish.size * 0.4, fish.size * 0.1, fish.size * 0.1, fish.size * 0.2);
        this.ctx.fill();

        // الزعنفة الذيلية - شكل بيضاوي
        this.ctx.beginPath();
        this.ctx.ellipse(0, fish.size * 0.8, fish.size * 0.15, fish.size * 0.1, 0, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawRoyalCrown(fish) {
        this.ctx.fillStyle = '#f1c40f';
        this.ctx.strokeStyle = '#d68910';
        this.ctx.lineWidth = 1;

        // التاج الصغير
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 0.55, -fish.size * 0.8);
        this.ctx.lineTo(-fish.size * 0.45, -fish.size * 0.9);
        this.ctx.lineTo(-fish.size * 0.35, -fish.size * 0.85);
        this.ctx.lineTo(-fish.size * 0.25, -fish.size * 0.8);
        this.ctx.lineTo(-fish.size * 0.55, -fish.size * 0.8);
        this.ctx.fill();
        this.ctx.stroke();
    }

    // رسم السلحفاة
    drawTurtle(fish) {
        // الصدفة
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 3;

        // الصدفة الرئيسية - شكل بيضاوي
        this.ctx.beginPath();
        this.ctx.ellipse(0, 0, fish.size * 0.8, fish.size * 0.6, 0, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // نمط الصدفة
        this.drawTurtleShellPattern(fish);

        // الرأس
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.2);
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.9, 0, fish.size * 0.25, fish.size * 0.2, 0, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // العيون
        this.ctx.fillStyle = '#ffffff';
        this.ctx.beginPath();
        this.ctx.arc(-fish.size * 0.95, -fish.size * 0.1, fish.size * 0.06, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.arc(-fish.size * 0.93, -fish.size * 0.1, fish.size * 0.04, 0, Math.PI * 2);
        this.ctx.fill();

        // الزعانف
        this.drawTurtleFlippers(fish);
    }

    drawTurtleShellPattern(fish) {
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.4);
        this.ctx.lineWidth = 1;

        // خطوط الصدفة
        for (let i = -2; i <= 2; i++) {
            this.ctx.beginPath();
            this.ctx.moveTo(fish.size * 0.6 * i / 3, -fish.size * 0.4);
            this.ctx.lineTo(fish.size * 0.6 * i / 3, fish.size * 0.4);
            this.ctx.stroke();
        }

        for (let i = -1; i <= 1; i++) {
            this.ctx.beginPath();
            this.ctx.ellipse(0, fish.size * 0.3 * i, fish.size * 0.6, fish.size * 0.1, 0, 0, Math.PI * 2);
            this.ctx.stroke();
        }
    }

    drawTurtleFlippers(fish) {
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.2);

        // الزعانف الأمامية
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.6, -fish.size * 0.5, fish.size * 0.2, fish.size * 0.35, Math.PI / 4, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.6, fish.size * 0.5, fish.size * 0.2, fish.size * 0.35, -Math.PI / 4, 0, Math.PI * 2);
        this.ctx.fill();

        // الزعانف الخلفية
        this.ctx.beginPath();
        this.ctx.ellipse(fish.size * 0.6, -fish.size * 0.4, fish.size * 0.15, fish.size * 0.3, Math.PI / 6, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.beginPath();
        this.ctx.ellipse(fish.size * 0.6, fish.size * 0.4, fish.size * 0.15, fish.size * 0.3, -Math.PI / 6, 0, Math.PI * 2);
        this.ctx.fill();
    }

    // رسم سرطان البحر
    drawCrab(fish) {
        // جسم السرطان - شكل بيضاوي مسطح
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;

        // الجسم الرئيسي
        this.ctx.beginPath();
        this.ctx.ellipse(0, 0, fish.size * 0.8, fish.size * 0.5, 0, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // الصدفة العلوية
        this.ctx.fillStyle = this.darkenColor(fish.color, 0.2);
        this.ctx.beginPath();
        this.ctx.ellipse(0, -fish.size * 0.1, fish.size * 0.6, fish.size * 0.3, 0, 0, Math.PI * 2);
        this.ctx.fill();

        // المخالب
        this.drawCrabClaws(fish);

        // الأرجل
        this.drawCrabLegs(fish);

        // العيون المرتفعة
        this.drawCrabEyes(fish);
    }

    drawCrabClaws(fish) {
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.4);
        this.ctx.lineWidth = 2;

        // المخلب الأيسر
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.9, -fish.size * 0.2, fish.size * 0.25, fish.size * 0.15, -Math.PI/6, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // المخلب الأيمن
        this.ctx.beginPath();
        this.ctx.ellipse(fish.size * 0.9, -fish.size * 0.2, fish.size * 0.25, fish.size * 0.15, Math.PI/6, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // أصابع المخالب
        this.ctx.lineWidth = 3;
        this.ctx.lineCap = 'round';

        // أصابع المخلب الأيسر
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 1.1, -fish.size * 0.3);
        this.ctx.lineTo(-fish.size * 1.2, -fish.size * 0.4);
        this.ctx.moveTo(-fish.size * 1.1, -fish.size * 0.1);
        this.ctx.lineTo(-fish.size * 1.2, -fish.size * 0.05);
        this.ctx.stroke();

        // أصابع المخلب الأيمن
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 1.1, -fish.size * 0.3);
        this.ctx.lineTo(fish.size * 1.2, -fish.size * 0.4);
        this.ctx.moveTo(fish.size * 1.1, -fish.size * 0.1);
        this.ctx.lineTo(fish.size * 1.2, -fish.size * 0.05);
        this.ctx.stroke();
    }

    drawCrabLegs(fish) {
        this.ctx.strokeStyle = fish.color;
        this.ctx.lineWidth = 4;
        this.ctx.lineCap = 'round';

        // الأرجل الجانبية (4 على كل جانب)
        for (let i = 0; i < 4; i++) {
            const legAngle = (i / 4) * Math.PI * 0.8 - Math.PI * 0.4;
            const legLength = fish.size * 0.6;

            // الرجل اليسرى
            const leftStartX = -fish.size * 0.5;
            const leftStartY = fish.size * 0.2;
            const leftEndX = leftStartX + Math.cos(legAngle - Math.PI/2) * legLength;
            const leftEndY = leftStartY + Math.sin(legAngle - Math.PI/2) * legLength;

            this.ctx.beginPath();
            this.ctx.moveTo(leftStartX, leftStartY);
            this.ctx.lineTo(leftEndX, leftEndY);
            this.ctx.stroke();

            // الرجل اليمنى
            const rightStartX = fish.size * 0.5;
            const rightStartY = fish.size * 0.2;
            const rightEndX = rightStartX + Math.cos(legAngle + Math.PI/2) * legLength;
            const rightEndY = rightStartY + Math.sin(legAngle + Math.PI/2) * legLength;

            this.ctx.beginPath();
            this.ctx.moveTo(rightStartX, rightStartY);
            this.ctx.lineTo(rightEndX, rightEndY);
            this.ctx.stroke();
        }
    }

    drawCrabEyes(fish) {
        const eyeHeight = fish.size * 0.15;

        // عود العين الأيسر
        this.ctx.strokeStyle = fish.color;
        this.ctx.lineWidth = 3;
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 0.3, -fish.size * 0.3);
        this.ctx.lineTo(-fish.size * 0.3, -fish.size * 0.6);
        this.ctx.stroke();

        // عود العين الأيمن
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.3, -fish.size * 0.3);
        this.ctx.lineTo(fish.size * 0.3, -fish.size * 0.6);
        this.ctx.stroke();

        // العين اليسرى - شكل بيضاوي
        this.ctx.fillStyle = '#ffffff';
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.3, -fish.size * 0.6, eyeHeight, eyeHeight * 0.8, 0, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.28, -fish.size * 0.6, eyeHeight * 0.6, eyeHeight * 0.5, 0, 0, Math.PI * 2);
        this.ctx.fill();

        // العين اليمنى - شكل بيضاوي
        this.ctx.fillStyle = '#ffffff';
        this.ctx.beginPath();
        this.ctx.ellipse(fish.size * 0.3, -fish.size * 0.6, eyeHeight, eyeHeight * 0.8, 0, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.ellipse(fish.size * 0.28, -fish.size * 0.6, eyeHeight * 0.6, eyeHeight * 0.5, 0, 0, Math.PI * 2);
        this.ctx.fill();
    }

    // رسم الكركند
    drawLobster(fish) {
        // جسم الكركند - شكل مستطيل مدور
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;

        // الجسم الرئيسي
        this.ctx.beginPath();
        this.ctx.ellipse(0, 0, fish.size * 0.9, fish.size * 0.4, 0, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // الذيل المروحي
        this.drawLobsterTail(fish);

        // المقص العملاق
        this.drawLobsterClaws(fish);

        // قرون الاستشعار
        this.drawLobsterAntennae(fish);

        // العيون
        this.drawLobsterEyes(fish);

        // خطوط الجسم
        this.drawLobsterSegments(fish);
    }

    drawLobsterTail(fish) {
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.2);

        // الذيل المروحي
        const tailSegments = 5;
        for (let i = 0; i < tailSegments; i++) {
            const angle = (i / tailSegments) * Math.PI * 0.6 - Math.PI * 0.3;
            this.ctx.beginPath();
            this.ctx.ellipse(
                fish.size * 0.8,
                Math.sin(angle) * fish.size * 0.3,
                fish.size * 0.2,
                fish.size * 0.1,
                angle,
                0,
                Math.PI * 2
            );
            this.ctx.fill();
        }
    }

    drawLobsterClaws(fish) {
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.4);
        this.ctx.lineWidth = 2;

        // المقص الأيسر (أكبر)
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 1.1, -fish.size * 0.1, fish.size * 0.4, fish.size * 0.2, -Math.PI/4, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // المقص الأيمن (أصغر)
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 1.1, fish.size * 0.1, fish.size * 0.3, fish.size * 0.15, Math.PI/4, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // أصابع المقص
        this.ctx.lineWidth = 4;
        this.ctx.lineCap = 'round';

        // أصابع المقص الأيسر
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 1.4, -fish.size * 0.2);
        this.ctx.lineTo(-fish.size * 1.5, -fish.size * 0.3);
        this.ctx.moveTo(-fish.size * 1.4, 0);
        this.ctx.lineTo(-fish.size * 1.5, 0.1);
        this.ctx.stroke();

        // أصابع المقص الأيمن
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 1.3, fish.size * 0.05);
        this.ctx.lineTo(-fish.size * 1.4, fish.size * 0.15);
        this.ctx.moveTo(-fish.size * 1.3, fish.size * 0.15);
        this.ctx.lineTo(-fish.size * 1.4, fish.size * 0.25);
        this.ctx.stroke();
    }

    drawLobsterAntennae(fish) {
        this.ctx.strokeStyle = fish.color;
        this.ctx.lineWidth = 2;
        this.ctx.lineCap = 'round';

        // قرون الاستشعار الطويلة
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 0.8, -fish.size * 0.3);
        this.ctx.lineTo(-fish.size * 1.5, -fish.size * 0.8);
        this.ctx.moveTo(-fish.size * 0.8, -fish.size * 0.2);
        this.ctx.lineTo(-fish.size * 1.4, -fish.size * 0.7);
        this.ctx.stroke();

        // قرون الاستشعار القصيرة
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 0.7, -fish.size * 0.25);
        this.ctx.lineTo(-fish.size * 1.0, -fish.size * 0.5);
        this.ctx.moveTo(-fish.size * 0.7, -fish.size * 0.15);
        this.ctx.lineTo(-fish.size * 1.0, -fish.size * 0.4);
        this.ctx.stroke();
    }

    drawLobsterEyes(fish) {
        const eyeSize = fish.size * 0.08;

        // العين اليسرى - شكل بيضاوي
        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.6, -fish.size * 0.2, eyeSize, eyeSize * 0.7, 0, 0, Math.PI * 2);
        this.ctx.fill();

        // العين اليمنى - شكل بيضاوي
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.6, fish.size * 0.2, eyeSize, eyeSize * 0.7, 0, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawLobsterSegments(fish) {
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 1;

        // خطوط تقسيم الجسم
        for (let i = 1; i < 4; i++) {
            const x = -fish.size * 0.3 + (i / 4) * fish.size * 0.6;
            this.ctx.beginPath();
            this.ctx.moveTo(x, -fish.size * 0.3);
            this.ctx.lineTo(x, fish.size * 0.3);
            this.ctx.stroke();
        }
    }

    // رسم نجمة البحر
    drawStarfish(fish) {
        // جسم نجمة البحر - 5 أذرع
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;

        // المركز - شكل بيضاوي
        this.ctx.beginPath();
        this.ctx.ellipse(0, 0, fish.size * 0.3, fish.size * 0.25, 0, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // الأذرع الخمسة
        for (let i = 0; i < 5; i++) {
            const angle = (i / 5) * Math.PI * 2 - Math.PI / 2;
            this.drawStarfishArm(fish, angle);
        }

        // النمط المركزي
        this.drawStarfishPattern(fish);
    }

    drawStarfishArm(fish, angle) {
        const armLength = fish.size * 0.8;
        const armWidth = fish.size * 0.25;

        this.ctx.save();
        this.ctx.rotate(angle);

        // الذراع
        this.ctx.fillStyle = fish.color;
        this.ctx.beginPath();
        this.ctx.ellipse(armLength / 2, 0, armLength / 2, armWidth / 2, 0, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // نقاط على الذراع - شكل بيضاوي
        this.ctx.fillStyle = this.darkenColor(fish.color, 0.2);
        for (let i = 1; i <= 3; i++) {
            const x = (i / 4) * armLength;
            this.ctx.beginPath();
            this.ctx.ellipse(x, 0, fish.size * 0.05, fish.size * 0.03, 0, 0, Math.PI * 2);
            this.ctx.fill();
        }

        this.ctx.restore();
    }

    drawStarfishPattern(fish) {
        // نمط مركزي - شكل بيضاوي
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.3);
        this.ctx.beginPath();
        this.ctx.ellipse(0, 0, fish.size * 0.15, fish.size * 0.12, 0, 0, Math.PI * 2);
        this.ctx.fill();

        // نقاط حول المركز - شكل بيضاوي
        this.ctx.fillStyle = this.darkenColor(fish.color, 0.4);
        for (let i = 0; i < 10; i++) {
            const angle = (i / 10) * Math.PI * 2;
            const x = Math.cos(angle) * fish.size * 0.2;
            const y = Math.sin(angle) * fish.size * 0.2;
            this.ctx.beginPath();
            this.ctx.ellipse(x, y, fish.size * 0.03, fish.size * 0.02, 0, 0, Math.PI * 2);
            this.ctx.fill();
        }
    }

    // رسم سمكة الصياد
    drawAnglerfish(fish) {
        // جسم سمكة الصياد - شكل كمثري مخيف
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;

        // الجسم الرئيسي
        this.ctx.beginPath();
        this.ctx.ellipse(0, 0, fish.size * 0.8, fish.size * 0.6, 0, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // الفم المرعب
        this.drawAnglerfishMouth(fish);

        // المصباح المضيء
        this.drawAnglerfishLure(fish);

        // العيون المخيفة
        this.drawAnglerfishEyes(fish);

        // الزعانف
        this.drawAnglerfishFins(fish);

        // الأسنان الحادة
        this.drawAnglerfishTeeth(fish);
    }

    drawAnglerfishMouth(fish) {
        // الفم الكبير المفتوح
        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.6, 0, fish.size * 0.3, fish.size * 0.2, 0, 0, Math.PI * 2);
        this.ctx.fill();

        // اللسان
        this.ctx.fillStyle = '#8B0000';
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.6, fish.size * 0.1, fish.size * 0.2, fish.size * 0.1, 0, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawAnglerfishLure(fish) {
        // عود المصباح
        this.ctx.strokeStyle = fish.color;
        this.ctx.lineWidth = 3;
        this.ctx.beginPath();
        this.ctx.moveTo(0, -fish.size * 0.5);
        this.ctx.lineTo(0, -fish.size * 1.2);
        this.ctx.stroke();

        // المصباح المضيء - شكل بيضاوي
        this.ctx.fillStyle = '#FFFF00';
        this.ctx.shadowColor = '#FFFF00';
        this.ctx.shadowBlur = fish.size * 0.3;
        this.ctx.beginPath();
        this.ctx.ellipse(0, -fish.size * 1.2, fish.size * 0.15, fish.size * 0.12, 0, 0, Math.PI * 2);
        this.ctx.fill();

        // إزالة الظل
        this.ctx.shadowBlur = 0;

        // توهج حول المصباح - خطوط بدلاً من دوائر
        this.ctx.strokeStyle = '#FFFF00';
        this.ctx.lineWidth = 1;
        this.ctx.globalAlpha = 0.5;
        for (let i = 0; i < 8; i++) {
            const angle = (i / 8) * Math.PI * 2;
            this.ctx.beginPath();
            this.ctx.moveTo(
                Math.cos(angle) * fish.size * 0.2,
                -fish.size * 1.2 + Math.sin(angle) * fish.size * 0.2
            );
            this.ctx.lineTo(
                Math.cos(angle) * fish.size * 0.4,
                -fish.size * 1.2 + Math.sin(angle) * fish.size * 0.4
            );
            this.ctx.stroke();
        }
        this.ctx.globalAlpha = 1;
    }

    drawAnglerfishEyes(fish) {
        const eyeSize = fish.size * 0.12;

        // العين اليسرى
        this.ctx.fillStyle = '#FFFF00';
        this.ctx.beginPath();
        this.ctx.arc(-fish.size * 0.2, -fish.size * 0.3, eyeSize, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.arc(-fish.size * 0.15, -fish.size * 0.3, eyeSize * 0.6, 0, Math.PI * 2);
        this.ctx.fill();

        // العين اليمنى
        this.ctx.fillStyle = '#FFFF00';
        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.2, -fish.size * 0.3, eyeSize, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.arc(fish.size * 0.15, -fish.size * 0.3, eyeSize * 0.6, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawAnglerfishFins(fish) {
        this.ctx.fillStyle = this.darkenColor(fish.color, 0.2);

        // الزعنفة الظهرية
        this.ctx.beginPath();
        this.ctx.ellipse(fish.size * 0.3, -fish.size * 0.4, fish.size * 0.3, fish.size * 0.2, -Math.PI/6, 0, Math.PI * 2);
        this.ctx.fill();

        // الزعنفة السفلية
        this.ctx.beginPath();
        this.ctx.ellipse(fish.size * 0.3, fish.size * 0.4, fish.size * 0.3, fish.size * 0.2, Math.PI/6, 0, Math.PI * 2);
        this.ctx.fill();

        // الزعنفة الذيلية
        this.ctx.beginPath();
        this.ctx.ellipse(fish.size * 0.7, 0, fish.size * 0.2, fish.size * 0.4, 0, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawAnglerfishTeeth(fish) {
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.strokeStyle = '#CCCCCC';
        this.ctx.lineWidth = 1;

        // الأسنان العلوية
        for (let i = 0; i < 6; i++) {
            const x = -fish.size * 0.8 + (i / 6) * fish.size * 0.4;
            const y = -fish.size * 0.1;
            this.ctx.beginPath();
            this.ctx.moveTo(x, y);
            this.ctx.lineTo(x + fish.size * 0.02, y - fish.size * 0.08);
            this.ctx.lineTo(x + fish.size * 0.04, y);
            this.ctx.closePath();
            this.ctx.fill();
            this.ctx.stroke();
        }

        // الأسنان السفلية
        for (let i = 0; i < 6; i++) {
            const x = -fish.size * 0.8 + (i / 6) * fish.size * 0.4;
            const y = fish.size * 0.1;
            this.ctx.beginPath();
            this.ctx.moveTo(x, y);
            this.ctx.lineTo(x + fish.size * 0.02, y + fish.size * 0.08);
            this.ctx.lineTo(x + fish.size * 0.04, y);
            this.ctx.closePath();
            this.ctx.fill();
            this.ctx.stroke();
        }
    }

    // رسم سمكة السيف
    drawSwordfish(fish) {
        // جسم سمكة السيف - انسيابي وسريع
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;

        // الجسم الرئيسي
        this.ctx.beginPath();
        this.ctx.ellipse(0, 0, fish.size * 0.9, fish.size * 0.4, 0, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // السيف الطويل
        this.drawSwordfishSword(fish);

        // الزعنفة الظهرية الطويلة
        this.drawSwordfishDorsalFin(fish);

        // العيون الكبيرة
        this.drawSwordfishEyes(fish);

        // الزعانف الجانبية
        this.drawSwordfishSideFins(fish);

        // الذيل القوي
        this.drawSwordfishTail(fish);
    }

    drawSwordfishSword(fish) {
        // السيف الطويل والحاد
        this.ctx.fillStyle = this.lightenColor(fish.color, 0.4);
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.5);
        this.ctx.lineWidth = 2;

        // السيف الرئيسي
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 0.9, 0);
        this.ctx.lineTo(-fish.size * 2.2, -fish.size * 0.05);
        this.ctx.lineTo(-fish.size * 2.3, 0);
        this.ctx.lineTo(-fish.size * 2.2, fish.size * 0.05);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();

        // خط حاد في المنتصف
        this.ctx.strokeStyle = '#FFFFFF';
        this.ctx.lineWidth = 1;
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 0.9, 0);
        this.ctx.lineTo(-fish.size * 2.3, 0);
        this.ctx.stroke();
    }

    drawSwordfishDorsalFin(fish) {
        // الزعنفة الظهرية الطويلة والمدببة
        this.ctx.fillStyle = this.darkenColor(fish.color, 0.2);
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.4);
        this.ctx.lineWidth = 1;

        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 0.3, -fish.size * 0.4);
        this.ctx.lineTo(-fish.size * 0.1, -fish.size * 0.8);
        this.ctx.lineTo(fish.size * 0.3, -fish.size * 0.6);
        this.ctx.lineTo(fish.size * 0.5, -fish.size * 0.4);
        this.ctx.lineTo(-fish.size * 0.3, -fish.size * 0.4);
        this.ctx.fill();
        this.ctx.stroke();
    }

    drawSwordfishEyes(fish) {
        const eyeSize = fish.size * 0.15;

        // العين الكبيرة
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.beginPath();
        this.ctx.arc(-fish.size * 0.3, -fish.size * 0.1, eyeSize, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.arc(-fish.size * 0.25, -fish.size * 0.1, eyeSize * 0.7, 0, Math.PI * 2);
        this.ctx.fill();

        // بريق العين
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.beginPath();
        this.ctx.arc(-fish.size * 0.22, -fish.size * 0.12, eyeSize * 0.3, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawSwordfishSideFins(fish) {
        this.ctx.fillStyle = this.darkenColor(fish.color, 0.2);

        // الزعنفة الجانبية العلوية
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.2, -fish.size * 0.3, fish.size * 0.3, fish.size * 0.15, -Math.PI/4, 0, Math.PI * 2);
        this.ctx.fill();

        // الزعنفة الجانبية السفلية
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.2, fish.size * 0.3, fish.size * 0.3, fish.size * 0.15, Math.PI/4, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawSwordfishTail(fish) {
        // الذيل القوي والمتشعب
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;

        // الجزء العلوي من الذيل
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.9, 0);
        this.ctx.lineTo(fish.size * 1.4, -fish.size * 0.4);
        this.ctx.lineTo(fish.size * 1.2, -fish.size * 0.2);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();

        // الجزء السفلي من الذيل
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.9, 0);
        this.ctx.lineTo(fish.size * 1.4, fish.size * 0.4);
        this.ctx.lineTo(fish.size * 1.2, fish.size * 0.2);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();
    }

    // رسم القرش المطرقة
    drawHammerhead(fish) {
        // جسم القرش المطرقة
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;

        // الجسم الرئيسي
        this.ctx.beginPath();
        this.ctx.ellipse(0, 0, fish.size * 0.8, fish.size * 0.4, 0, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // الرأس المطرقة المميز
        this.drawHammerheadHead(fish);

        // الزعانف القوية
        this.drawHammerheadFins(fish);

        // العيون على جانبي المطرقة
        this.drawHammerheadEyes(fish);

        // الذيل القوي
        this.drawHammerheadTail(fish);

        // الخياشيم
        this.drawHammerheadGills(fish);
    }

    drawHammerheadHead(fish) {
        // الرأس المطرقة المميز
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.4);
        this.ctx.lineWidth = 2;

        // المطرقة الأفقية
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.9, 0, fish.size * 0.6, fish.size * 0.15, 0, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // الجزء المركزي من المطرقة
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.9, 0, fish.size * 0.2, fish.size * 0.25, 0, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.stroke();

        // الفم المخيف
        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.9, fish.size * 0.1, fish.size * 0.15, fish.size * 0.08, 0, 0, Math.PI * 2);
        this.ctx.fill();

        // الأسنان
        this.drawHammerheadTeeth(fish);
    }

    drawHammerheadEyes(fish) {
        const eyeSize = fish.size * 0.1;

        // العين اليسرى (على طرف المطرقة)
        this.ctx.fillStyle = '#000000';
        this.ctx.beginPath();
        this.ctx.arc(-fish.size * 1.4, -fish.size * 0.1, eyeSize, 0, Math.PI * 2);
        this.ctx.fill();

        // العين اليمنى (على الطرف الآخر)
        this.ctx.beginPath();
        this.ctx.arc(-fish.size * 1.4, fish.size * 0.1, eyeSize, 0, Math.PI * 2);
        this.ctx.fill();

        // بريق العيون
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.beginPath();
        this.ctx.arc(-fish.size * 1.38, -fish.size * 0.12, eyeSize * 0.3, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.beginPath();
        this.ctx.arc(-fish.size * 1.38, fish.size * 0.08, eyeSize * 0.3, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawHammerheadFins(fish) {
        this.ctx.fillStyle = this.darkenColor(fish.color, 0.2);

        // الزعنفة الظهرية الكبيرة
        this.ctx.beginPath();
        this.ctx.moveTo(-fish.size * 0.2, -fish.size * 0.4);
        this.ctx.lineTo(0, -fish.size * 0.8);
        this.ctx.lineTo(fish.size * 0.3, -fish.size * 0.4);
        this.ctx.closePath();
        this.ctx.fill();

        // الزعانف الجانبية
        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.3, -fish.size * 0.3, fish.size * 0.4, fish.size * 0.2, -Math.PI/3, 0, Math.PI * 2);
        this.ctx.fill();

        this.ctx.beginPath();
        this.ctx.ellipse(-fish.size * 0.3, fish.size * 0.3, fish.size * 0.4, fish.size * 0.2, Math.PI/3, 0, Math.PI * 2);
        this.ctx.fill();
    }

    drawHammerheadTail(fish) {
        // الذيل القوي والمتشعب
        this.ctx.fillStyle = fish.color;
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.3);
        this.ctx.lineWidth = 2;

        // الجزء العلوي من الذيل (أكبر)
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.8, 0);
        this.ctx.lineTo(fish.size * 1.5, -fish.size * 0.5);
        this.ctx.lineTo(fish.size * 1.2, -fish.size * 0.1);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();

        // الجزء السفلي من الذيل (أصغر)
        this.ctx.beginPath();
        this.ctx.moveTo(fish.size * 0.8, 0);
        this.ctx.lineTo(fish.size * 1.3, fish.size * 0.3);
        this.ctx.lineTo(fish.size * 1.1, fish.size * 0.1);
        this.ctx.closePath();
        this.ctx.fill();
        this.ctx.stroke();
    }

    drawHammerheadTeeth(fish) {
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.strokeStyle = '#CCCCCC';
        this.ctx.lineWidth = 0.5;

        // أسنان حادة
        for (let i = 0; i < 8; i++) {
            const x = -fish.size * 1.05 + (i / 8) * fish.size * 0.3;
            const y = fish.size * 0.1;
            this.ctx.beginPath();
            this.ctx.moveTo(x, y);
            this.ctx.lineTo(x + fish.size * 0.015, y - fish.size * 0.06);
            this.ctx.lineTo(x + fish.size * 0.03, y);
            this.ctx.closePath();
            this.ctx.fill();
            this.ctx.stroke();
        }
    }

    drawHammerheadGills(fish) {
        this.ctx.strokeStyle = this.darkenColor(fish.color, 0.4);
        this.ctx.lineWidth = 2;
        this.ctx.lineCap = 'round';

        // خطوط الخياشيم
        for (let i = 0; i < 5; i++) {
            const x = -fish.size * 0.6 + i * fish.size * 0.1;
            this.ctx.beginPath();
            this.ctx.moveTo(x, -fish.size * 0.2);
            this.ctx.lineTo(x + fish.size * 0.05, fish.size * 0.2);
            this.ctx.stroke();
        }
    }

    // معالجات أحداث TikTok Live
    handleGiftReceived(data) {
        const giftName = data.giftName || 'Rose';
        const giftCount = data.repeatCount || 1;
        const playerName = data.nickname || data.uniqueId;

        console.log(`🎁 تم استقبال هدية: ${giftName} (${giftCount}) من ${playerName}`);

        // تحديث إحصائيات الهدايا
        this.gameState.rosesReceived += giftCount;

        // تنفيذ كل هدية على حدة - عدالة كاملة للداعمين!
        console.log(`💰 تنفيذ ${giftCount} هدية منفصلة للداعم ${playerName}`);

        for (let i = 0; i < giftCount; i++) {
            // تأخير بسيط بين كل هدية لتأثير بصري أفضل
            setTimeout(() => {
                // الحصول على تعيين لهدية واحدة
                const assignment = this.getGiftAssignment(giftName, 1);

                // إنشاء سمكة لكل هدية منفصلة
                const fish = this.createFishWithAssignment(playerName, 1, assignment);

                // تشغيل صوت لكل هدية
                if (this.gameSettings.soundEnabled) {
                    this.playSound(assignment.soundEffect || 'spawn', 0.4);
                }

                // إضافة تأثير بصري لكل هدية
                this.createGiftEffect(giftName, 1);

                console.log(`🎯 هدية ${i + 1}/${giftCount}: ${giftName} → ${assignment.creatureType} للداعم ${playerName}`);
            }, i * 200); // 200ms بين كل هدية
        }

        // رسالة إجمالية
        console.log(`✅ تم تنفيذ جميع الـ ${giftCount} هدايا للداعم ${playerName} - لا نصب ولا احتيال!`);
    }

    // الحصول على تعيين الهدية
    getGiftAssignment(giftName, giftCount) {
        // محاولة الحصول على التعيين من صفحة الإعدادات
        if (window.getAssignmentForGift) {
            return window.getAssignmentForGift(giftName, giftCount);
        }

        // التعيينات الافتراضية
        const defaultAssignments = {
            'Rose': { creatureType: 'random', sizeMultiplier: 1, specialEffect: 'none' },
            'Diamond': { creatureType: 'shark', sizeMultiplier: 1.5, specialEffect: 'wild_mode' },
            'Crown': { creatureType: 'whale', sizeMultiplier: 2, specialEffect: 'size_boost' },
            'Lightning': { creatureType: 'eel', sizeMultiplier: 1.2, specialEffect: 'electric' },
            'Butterfly': { creatureType: 'jellyfish', sizeMultiplier: 1, specialEffect: 'poison' },
            'Star': { creatureType: 'salmon', sizeMultiplier: 1, specialEffect: 'speed_boost' },
            'Heart': { creatureType: 'dolphin', sizeMultiplier: 1.3, specialEffect: 'speed_boost' },
            'Fire': { creatureType: 'anglerfish', sizeMultiplier: 1.4, specialEffect: 'lure_trap' },
            'Ice': { creatureType: 'turtle', sizeMultiplier: 1.8, specialEffect: 'shell_defense' },
            'Thunder': { creatureType: 'hammerhead', sizeMultiplier: 1.6, specialEffect: 'hammer_smash' },
            'Join': { creatureType: 'normal', sizeMultiplier: 1, specialEffect: 'none' },
            'Like': { creatureType: 'seahorse', sizeMultiplier: 0.8, specialEffect: 'speed_boost' },
            'Follow': { creatureType: 'dolphin', sizeMultiplier: 1.3, specialEffect: 'speed_boost' },
            'Share': { creatureType: 'turtle', sizeMultiplier: 1.5, specialEffect: 'shell_defense' }
        };

        return defaultAssignments[giftName] || { creatureType: 'random', sizeMultiplier: 1, specialEffect: 'none' };
    }

    // إنشاء سمكة مع التعيين
    createFishWithAssignment(playerName, giftCount, assignment) {
        // تحديد نوع الكائن
        let fishType = assignment.creatureType;
        if (fishType === 'random') {
            fishType = this.determineFishType(giftCount);
        }

        // حساب الحجم مع المضاعف
        const baseSize = this.calculateFishSize(giftCount);
        let finalSize = baseSize * assignment.sizeMultiplier;

        // التأكد من الحد الأدنى للحجم لوضوح أفضل مع التكبير الجديد
        finalSize = Math.max(20, finalSize);

        // إنشاء السمكة
        const fish = this.createSpecificFishWithDetails(playerName, fishType, finalSize, giftCount);

        // تطبيق التأثير الخاص
        if (assignment.specialEffect !== 'none' && fish) {
            this.applySpecialEffect(fish, assignment.specialEffect);
        }

        // متابعة الأسماك الكبيرة تلقائياً للعرض الأفضل (حسب الإعدادات)
        if (fish && this.autoFollowEnabled && fish.size > this.autoFollowThreshold && !this.camera.followTarget) {
            setTimeout(() => {
                if (!this.camera.followTarget) {
                    this.followFish(fish);
                    console.log(`🎯 متابعة تلقائية للسمكة الكبيرة: ${fish.playerName} (حجم: ${fish.size.toFixed(1)})`);
                }
            }, 1000);
        }

        return fish;
    }

    // تطبيق التأثيرات الخاصة
    applySpecialEffect(fish, effect) {
        switch (effect) {
            case 'speed_boost':
                fish.speedBoost = 2;
                fish.speedBoostTime = 300;
                fish.glowIntensity = 0.8;
                break;
            case 'size_boost':
                fish.size *= 1.5;
                fish.baseSize *= 1.5;
                break;
            case 'wild_mode':
                fish.isWild = true;
                fish.aggression = 1.0;
                fish.speedBoost = 1.5;
                break;
            case 'electric':
                fish.special = 'electric_shock';
                fish.glowIntensity = 1.0;
                break;
            case 'poison':
                fish.special = 'poison_sting';
                fish.isWild = true;
                break;
            case 'invisible':
                fish.opacity = 0.3;
                fish.special = 'stealth';
                break;
        }

        console.log(`✨ تم تطبيق تأثير ${effect} على ${fish.type}`);
    }

    // إنشاء سمكة محددة مع تفاصيل مخصصة
    createSpecificFishWithDetails(playerName, fishType, size, giftCount) {
        const playerIndex = Array.from(this.players.keys()).indexOf(playerName);
        const playerColor = this.playerColors[playerIndex % this.maxPlayers];

        const fish = {
            id: Date.now() + Math.random(),
            playerName: playerName,
            playerColor: playerColor,
            x: Math.random() * (this.gameWidth - 100) + 50,
            y: Math.random() * (this.gameHeight - 200) + 100,
            vx: (Math.random() - 0.5) * 2,
            vy: (Math.random() - 0.5) * 2,
            size: size,
            baseSize: size,
            color: this.getFishColor(size, fishType),
            health: 100,
            maxHealth: 100,
            energy: 100,
            maxEnergy: 100,
            age: 0,
            roseCount: giftCount,
            kills: 0,

            // نوع السمكة وخصائصها
            type: fishType,
            typeName: this.fishTypes[fishType].name,
            baseSpeed: this.fishTypes[fishType].speed,
            aggression: this.fishTypes[fishType].aggression,
            special: this.fishTypes[fishType].special,
            specialCooldown: 0,

            // خصائص الحركة المحسنة
            targetX: Math.random() * this.gameWidth,
            targetY: Math.random() * this.gameHeight,
            speed: (1 + Math.random() * 2) * this.fishTypes[fishType].speed,
            maxSpeed: (3 + size * 0.1) * this.fishTypes[fishType].speed,

            // خصائص بصرية محسنة
            tailAngle: 0,
            finAngle: 0,
            eyeSize: size * 0.15,
            bodyPattern: Math.floor(Math.random() * 3),

            // حالات خاصة محسنة
            isHunting: false,
            isWild: false,
            speedBoost: 1,
            speedBoostTime: 0,
            isStunned: false,
            stunTime: 0,

            // ذكاء اصطناعي محسن
            behavior: 'wander',
            behaviorTimer: 0,
            nearbyFish: [],
            lastAttackTime: 0,

            // تأثيرات محسنة
            glowIntensity: 0,
            particles: [],
            trail: [],
            opacity: 1,

            // إحصائيات متقدمة
            distanceTraveled: 0,
            timeAlive: 0,
            foodEaten: 0,

            // وقت الإنشاء للحذف التلقائي
            createdAt: Date.now()
        };

        // تطبيق خصائص خاصة حسب النوع
        this.applyFishTypeSpecials(fish);

        this.fish.push(fish);
        this.gameState.totalFish++;

        // إضافة تأثير ظهور محسن
        this.createSpawnEffect(fish.x, fish.y, fishType);

        // تشغيل صوت مرعب للأسماك القوية
        this.playScarySpawnSound(fish.type, fish.size);

        // تحديث إحصائيات اللاعب
        this.updatePlayerStats(playerName, giftCount);

        console.log(`🐟 تم إنشاء ${fish.typeName} جديدة للاعب ${playerName} (حجم: ${size.toFixed(1)})`);

        // فحص المتابعة التلقائية للسمكة الجديدة إذا كانت كبيرة
        if (this.autoFollowEnabled && fish.size >= this.autoFollowThreshold) {
            // إذا لم تكن هناك سمكة متابعة حالياً، ابدأ متابعة هذه السمكة
            if (!this.camera.followTarget || !this.fish.includes(this.camera.followTarget)) {
                this.followFish(fish);
                console.log(`🎯 متابعة تلقائية فورية: ${fish.playerName} (${fish.typeName}) - حجم: ${fish.size.toFixed(1)}`);
            }
        }

        return fish;
    }

    // إنشاء تأثير بصري للهدية
    createGiftEffect(giftName, giftCount) {
        // إنشاء جسيمات ملونة حسب نوع الهدية
        const colors = this.getGiftColors(giftName);
        const centerX = this.gameWidth / 2;
        const centerY = this.gameHeight / 2;

        for (let i = 0; i < giftCount * 3; i++) {
            const angle = (i / (giftCount * 3)) * Math.PI * 2;
            const distance = 50 + Math.random() * 100;

            this.particles.push({
                x: centerX + Math.cos(angle) * distance,
                y: centerY + Math.sin(angle) * distance,
                vx: Math.cos(angle) * (2 + Math.random() * 3),
                vy: Math.sin(angle) * (2 + Math.random() * 3),
                size: 3 + Math.random() * 5,
                color: colors[Math.floor(Math.random() * colors.length)],
                life: 60 + Math.random() * 40,
                maxLife: 60 + Math.random() * 40,
                type: 'gift'
            });
        }

        console.log(`✨ تم إنشاء تأثير بصري للهدية: ${giftName}`);
    }

    // الحصول على ألوان الهدية
    getGiftColors(giftName) {
        const giftColors = {
            'Rose': ['#ff6b9d', '#ff8fab', '#ffa8cc'],
            'Diamond': ['#74b9ff', '#0984e3', '#e8f4fd'],
            'Crown': ['#f1c40f', '#f39c12', '#d68910'],
            'Lightning': ['#f1c40f', '#f39c12', '#e67e22'],
            'Butterfly': ['#ff6b9d', '#74b9ff', '#00b894'],
            'Star': ['#f1c40f', '#f39c12', '#ffffff'],
            'Heart': ['#e74c3c', '#ff6b9d', '#fd79a8'],
            'Fire': ['#e17055', '#d63031', '#fd79a8'],
            'Ice': ['#74b9ff', '#00cec9', '#ffffff'],
            'Thunder': ['#6c5ce7', '#a29bfe', '#fd79a8']
        };

        return giftColors[giftName] || ['#ffffff', '#f1c40f', '#74b9ff'];
    }

    // إنشاء تأثير بصري للانضمام
    createJoinEffect(playerName) {
        const centerX = this.gameWidth / 2;
        const centerY = this.gameHeight / 4;

        // جسيمات ترحيبية
        for (let i = 0; i < 15; i++) {
            const angle = (i / 15) * Math.PI * 2;
            const distance = 30 + Math.random() * 50;

            this.particles.push({
                x: centerX + Math.cos(angle) * distance,
                y: centerY + Math.sin(angle) * distance,
                vx: Math.cos(angle) * (1 + Math.random() * 2),
                vy: Math.sin(angle) * (1 + Math.random() * 2),
                size: 2 + Math.random() * 4,
                color: '#00b894',
                life: 80 + Math.random() * 40,
                maxLife: 80 + Math.random() * 40,
                type: 'join'
            });
        }

        console.log(`✨ تم إنشاء تأثير بصري للانضمام: ${playerName}`);
    }

    // إنشاء تأثير بصري للايك
    createLikeEffect(likeCount) {
        const centerX = this.gameWidth / 2;
        const centerY = this.gameHeight / 2;

        // جسيمات قلوب
        for (let i = 0; i < likeCount * 2; i++) {
            const angle = Math.random() * Math.PI * 2;
            const distance = 20 + Math.random() * 80;

            this.particles.push({
                x: centerX + Math.cos(angle) * distance,
                y: centerY + Math.sin(angle) * distance,
                vx: Math.cos(angle) * (0.5 + Math.random() * 1.5),
                vy: Math.sin(angle) * (0.5 + Math.random() * 1.5) - 1,
                size: 3 + Math.random() * 3,
                color: '#e74c3c',
                life: 60 + Math.random() * 30,
                maxLife: 60 + Math.random() * 30,
                type: 'like'
            });
        }

        console.log(`✨ تم إنشاء تأثير بصري للايك: ${likeCount} لايك`);
    }

    // إنشاء تأثير بصري للمتابعة
    createFollowEffect(playerName) {
        const centerX = this.gameWidth / 2;
        const centerY = this.gameHeight / 3;

        // جسيمات ذهبية للمتابعة
        for (let i = 0; i < 20; i++) {
            const angle = (i / 20) * Math.PI * 2;
            const distance = 40 + Math.random() * 60;

            this.particles.push({
                x: centerX + Math.cos(angle) * distance,
                y: centerY + Math.sin(angle) * distance,
                vx: Math.cos(angle) * (1.5 + Math.random() * 2),
                vy: Math.sin(angle) * (1.5 + Math.random() * 2),
                size: 3 + Math.random() * 5,
                color: '#f1c40f',
                life: 100 + Math.random() * 50,
                maxLife: 100 + Math.random() * 50,
                type: 'follow'
            });
        }

        console.log(`✨ تم إنشاء تأثير بصري للمتابعة: ${playerName}`);
    }

    handleCommentReceived(data) {
        const comment = data.comment.toLowerCase().trim();
        const playerName = data.nickname || data.uniqueId;

        // العثور على أسماك اللاعب
        const playerFish = this.fish.filter(fish => fish.playerName === playerName);

        if (playerFish.length === 0) return;

        switch (comment) {
            case 'هجوم':
            case 'attack':
                playerFish.forEach(fish => {
                    fish.isHunting = true;
                    fish.speedBoost = 2;
                    fish.speedBoostTime = 300; // 5 ثوان
                });
                console.log(`⚔️ ${playerName} فعل وضع الهجوم!`);
                break;

            case 'سرعة':
            case 'speed':
                playerFish.forEach(fish => {
                    fish.speedBoost = 2.5;
                    fish.speedBoostTime = 180; // 3 ثوان
                });
                console.log(`💨 ${playerName} فعل التسريع!`);
                break;

            case 'متوحش':
            case 'wild':
                playerFish.forEach(fish => {
                    fish.isWild = true;
                    fish.size *= 1.2;
                    fish.maxHealth *= 1.5;
                    fish.health = fish.maxHealth;
                    fish.color = '#ff0000';
                });
                console.log(`🔥 ${playerName} حول أسماكه إلى متوحشة!`);
                break;
        }
    }

    handleLikeReceived(data) {
        const playerName = data.nickname || data.uniqueId || 'معجب';
        const likeCount = data.likeCount || 1;

        console.log(`❤️ ${playerName} أعجب بالبث (${likeCount} لايك)`);

        // التحقق من وجود تعيين للإعجابات
        const testAssignment = this.getGiftAssignment('Like', 1);
        if (!testAssignment || testAssignment.creatureType === 'random') {
            console.log(`⚠️ لا يوجد تعيين محدد للإعجابات - تم تجاهل ${likeCount} إعجاب من ${playerName}`);

            // إضافة تأثير بصري فقط بدون إنشاء أسماك
            this.createLikeEffect(likeCount);
            return;
        }

        // تنفيذ كل إعجاب على حدة - عدالة كاملة!
        console.log(`💖 تنفيذ ${likeCount} إعجاب منفصل للداعم ${playerName}`);

        for (let i = 0; i < likeCount; i++) {
            setTimeout(() => {
                // الحصول على تعيين لإعجاب واحد
                const assignment = this.getGiftAssignment('Like', 1);

                // إنشاء سمكة لكل إعجاب منفصل
                this.createFishWithAssignment(playerName, 1, assignment);

                // تشغيل صوت لكل إعجاب
                if (this.gameSettings.soundEnabled) {
                    this.playSound(assignment.soundEffect || 'spawn', 0.3);
                }

                // إضافة تأثير بصري لكل إعجاب
                this.createLikeEffect(1);

                console.log(`💖 إعجاب ${i + 1}/${likeCount}: Like → ${assignment.creatureType} للداعم ${playerName}`);
            }, i * 150); // 150ms بين كل إعجاب
        }

        console.log(`✅ تم تنفيذ جميع الـ ${likeCount} إعجاب للداعم ${playerName} - عدالة كاملة!`);
    }

    handleFollowReceived(data) {
        const playerName = data.nickname || data.uniqueId || 'متابع_جديد';

        console.log(`🔔 ${playerName} تابع القناة`);

        // الحصول على تعيين حدث المتابعة
        const assignment = this.getGiftAssignment('Follow', 1);

        // إنشاء سمكة للمتابع الجديد
        const fish = this.createFishWithAssignment(playerName, 1, assignment);

        // تشغيل صوت المتابعة
        if (this.gameSettings.soundEnabled) {
            this.playSound('follow', 0.6);
        }

        // إضافة تأثير بصري للمتابعة
        this.createFollowEffect(playerName);

        console.log(`🎯 تم تطبيق تعيين المتابعة: Follow → ${assignment.creatureType}`);
    }

    handleMemberJoined(data) {
        const playerName = data.nickname || data.uniqueId || 'مشاهد_جديد';

        console.log(`👋 انضم ${playerName} إلى البث`);

        // الحصول على تعيين حدث الانضمام
        const assignment = this.getGiftAssignment('Join', 1);

        // إنشاء سمكة للمنضم الجديد
        const fish = this.createFishWithAssignment(playerName, 1, assignment);

        // تشغيل صوت الترحيب
        if (this.gameSettings.soundEnabled) {
            this.playSound('spawn', 0.4);
        }

        // إضافة تأثير بصري للانضمام
        this.createJoinEffect(playerName);

        console.log(`🎯 تم تطبيق تعيين الانضمام: Join → ${assignment.creatureType}`);
    }

    // وظائف وضع التجربة
    simulateRose(count) {
        const testNames = [
            'أحمد_تست', 'فاطمة_تست', 'محمد_تست', 'عائشة_تست', 'علي_تست',
            'TestUser1', 'TestUser2', 'TestUser3', 'TestUser4', 'TestUser5',
            'Player_A', 'Player_B', 'Player_C', 'Gamer_X', 'Gamer_Y'
        ];
        const randomName = testNames[Math.floor(Math.random() * testNames.length)] + '_' + Date.now();

        // محاكاة استقبال الهدية
        const giftData = {
            giftName: 'Rose',
            giftId: 5655,
            repeatCount: count,
            nickname: randomName,
            uniqueId: randomName
        };

        this.handleGiftReceived(giftData);

        console.log(`🧪 محاكاة: ${randomName} أرسل ${count} وردة`);
    }

    simulateComment(comment) {
        const testNames = [
            'أحمد_كومنت', 'فاطمة_كومنت', 'محمد_كومنت',
            'TestUser1', 'TestUser2', 'TestUser3',
            'Commenter_A', 'Commenter_B'
        ];
        const randomName = testNames[Math.floor(Math.random() * testNames.length)] + '_' + Date.now();

        // محاكاة استقبال التعليق
        const commentData = {
            comment: comment,
            nickname: randomName,
            uniqueId: randomName
        };

        this.handleCommentReceived(commentData);

        console.log(`🧪 محاكاة تعليق: ${randomName} كتب "${comment}"`);
    }

    simulateLikes(count) {
        const likeNames = [
            'محب_1', 'محب_2', 'محب_3', 'محب_4', 'محب_5',
            'Liker_A', 'Liker_B', 'Liker_C', 'Fan_X', 'Fan_Y'
        ];

        for (let i = 0; i < count; i++) {
            setTimeout(() => {
                const randomName = likeNames[Math.floor(Math.random() * likeNames.length)] + '_' + (Date.now() + i);
                this.handleLikeReceived({
                    nickname: randomName,
                    uniqueId: randomName
                });
            }, i * 50);
        }

        console.log(`🧪 محاكاة ${count} إعجاب`);
    }

    // محاكاة انضمام
    simulateJoin() {
        const testNames = [
            'مشاهد_جديد', 'زائر_كريم', 'متفرج_نشط',
            'NewViewer', 'JoinedUser', 'WatcherX',
            'Visitor_A', 'Guest_B'
        ];
        const randomName = testNames[Math.floor(Math.random() * testNames.length)] + '_' + Date.now();

        // محاكاة حدث الانضمام
        const joinData = {
            nickname: randomName,
            uniqueId: randomName
        };

        this.handleMemberJoined(joinData);

        console.log(`🧪 محاكاة انضمام: ${randomName} انضم للبث`);
    }

    // محاكاة متابعة
    simulateFollow() {
        const testNames = [
            'متابع_جديد', 'مشترك_كريم', 'فان_نشط',
            'NewFollower', 'Subscriber', 'FanX',
            'Follower_A', 'Fan_B'
        ];
        const randomName = testNames[Math.floor(Math.random() * testNames.length)] + '_' + Date.now();

        // محاكاة حدث المتابعة
        const followData = {
            nickname: randomName,
            uniqueId: randomName
        };

        this.handleFollowReceived(followData);

        console.log(`🧪 محاكاة متابعة: ${randomName} تابع القناة`);
    }

    // محاكاة مشاركة
    simulateShare() {
        const testNames = [
            'مشارك_كريم', 'ناشر_نشط', 'مروج_البث',
            'Sharer', 'Promoter', 'SharerX',
            'Sharer_A', 'Promoter_B'
        ];
        const randomName = testNames[Math.floor(Math.random() * testNames.length)] + '_' + Date.now();

        // محاكاة حدث المشاركة (إضافة وظيفة جديدة)
        const shareData = {
            nickname: randomName,
            uniqueId: randomName
        };

        // الحصول على تعيين حدث المشاركة
        const assignment = this.getGiftAssignment('Share', 1);

        // إنشاء سمكة للمشاركة
        const fish = this.createFishWithAssignment(randomName, 1, assignment);

        // تشغيل صوت المشاركة
        if (this.gameSettings.soundEnabled) {
            this.playSound('spawn', 0.7);
        }

        console.log(`🧪 محاكاة مشاركة: ${randomName} شارك البث`);
        console.log(`🎯 تم تطبيق تعيين المشاركة: Share → ${assignment.creatureType}`);
    }

    addRandomPlayers(count) {
        const arabicNames = ['أحمد', 'فاطمة', 'محمد', 'عائشة', 'علي', 'زينب', 'يوسف', 'مريم', 'عمر', 'خديجة', 'حسن', 'نور', 'كريم', 'سارة', 'طارق'];
        const englishNames = ['Ahmed', 'Fatima', 'Mohamed', 'Aisha', 'Ali', 'Zeinab', 'Youssef', 'Mariam', 'Omar', 'Khadija', 'Hassan', 'Nour', 'Karim', 'Sara', 'Tarek'];
        const gameNames = ['Player', 'Gamer', 'User', 'Fish_Master', 'Ocean_King', 'Sea_Lord', 'Aqua_Pro'];
        const allNames = [...arabicNames, ...englishNames, ...gameNames];

        console.log(`🧪 بدء إضافة ${count} لاعبين عشوائيين...`);

        for (let i = 0; i < count; i++) {
            setTimeout(() => {
                const baseName = allNames[Math.floor(Math.random() * allNames.length)];
                const uniqueId = Math.floor(Math.random() * 10000);
                const randomName = `${baseName}_${uniqueId}`;
                const randomRoses = 1 + Math.floor(Math.random() * 25);

                // محاكاة استقبال هدية وردة
                const giftData = {
                    giftName: 'Rose',
                    giftId: 5655,
                    repeatCount: randomRoses,
                    nickname: randomName,
                    uniqueId: randomName
                };

                this.handleGiftReceived(giftData);

                if (i % 10 === 0) {
                    console.log(`🧪 تم إضافة ${i + 1}/${count} لاعبين`);
                }
            }, i * 150); // تسريع الإضافة أكثر
        }

        // رسالة إتمام
        setTimeout(() => {
            console.log(`✅ تم إضافة ${count} لاعبين عشوائيين بنجاح!`);
        }, count * 150 + 1000);
    }

    createSpecificFish(fishType) {
        const testName = `${this.fishTypes[fishType].name}_${Math.floor(Math.random() * 100)}`;
        let roseCount;

        // تحديد عدد الورود المناسب لنوع السمكة
        switch (fishType) {
            case 'whale':
                roseCount = 50 + Math.floor(Math.random() * 50);
                break;
            case 'shark':
                roseCount = 20 + Math.floor(Math.random() * 30);
                break;
            case 'eel':
                roseCount = 15 + Math.floor(Math.random() * 25);
                break;
            case 'dolphin':
                roseCount = 10 + Math.floor(Math.random() * 20);
                break;
            case 'salmon':
                roseCount = 5 + Math.floor(Math.random() * 15);
                break;
            case 'ray':
                roseCount = 3 + Math.floor(Math.random() * 12);
                break;
            default:
                roseCount = 1 + Math.floor(Math.random() * 5);
        }

        // إنشاء السمكة مع فرض النوع المحدد
        const fish = this.createFish(testName, roseCount);
        fish.type = fishType;
        fish.typeName = this.fishTypes[fishType].name;
        fish.color = this.getFishColor(fish.size, fishType);
        this.applyFishTypeSpecials(fish);

        console.log(`🧪 تم إنشاء ${this.fishTypes[fishType].name} خاص`);
    }

    startFishBattle() {
        // إنشاء معركة بين الأسماك الكبيرة
        const largeFish = this.fish.filter(f => f.size > 40);

        if (largeFish.length < 2) {
            // إنشاء أسماك كبيرة للمعركة
            this.createSpecificFish('shark');
            this.createSpecificFish('whale');
            setTimeout(() => this.startFishBattle(), 1000);
            return;
        }

        // جعل الأسماك الكبيرة تتجه نحو بعضها
        largeFish.forEach(fish => {
            fish.isHunting = true;
            fish.aggression = 1.0;
            fish.speedBoost = 2;
            fish.speedBoostTime = 600; // 10 ثوان

            // العثور على أقرب سمكة كبيرة
            const otherFish = largeFish.filter(f => f !== fish);
            if (otherFish.length > 0) {
                const target = otherFish[Math.floor(Math.random() * otherFish.length)];
                fish.targetX = target.x;
                fish.targetY = target.y;
                fish.behavior = 'hunt';
            }
        });

        console.log('⚔️ بدأت معركة الأسماك الكبيرة!');
    }

    createTreasureHunt() {
        // تفعيل صيد الكنوز - الأسماك تتجه نحو الكنوز
        this.fish.forEach(fish => {
            const nearestTreasure = this.environment.treasures
                .filter(t => !t.discovered)
                .sort((a, b) => {
                    const distA = this.getDistance(fish, a);
                    const distB = this.getDistance(fish, b);
                    return distA - distB;
                })[0];

            if (nearestTreasure) {
                fish.targetX = nearestTreasure.x;
                fish.targetY = nearestTreasure.y;
                fish.behavior = 'treasure_hunt';
                fish.speedBoost = 1.5;
                fish.speedBoostTime = 300;
            }
        });

        console.log('💎 بدأ صيد الكنوز!');
    }

    activateSpecialEvents() {
        // تفعيل أحداث خاصة متنوعة
        const events = [
            () => {
                // عاصفة مائية - تحريك جميع الأسماك
                this.fish.forEach(fish => {
                    fish.vx += (Math.random() - 0.5) * 10;
                    fish.vy += (Math.random() - 0.5) * 10;
                });
                console.log('🌊 عاصفة مائية!');
            },
            () => {
                // طعام وفير - نمو بطيء لجميع الأسماك
                this.fish.forEach(fish => {
                    fish.size *= 1.05; // قلل من 1.2 إلى 1.05 (نمو 5% بدلاً من 20%)
                    fish.health = fish.maxHealth;
                    fish.energy = fish.maxEnergy;
                });
                console.log('🍽️ طعام وفير!');
            },
            () => {
                // تسريع جماعي
                this.fish.forEach(fish => {
                    fish.speedBoost = 3;
                    fish.speedBoostTime = 300;
                });
                console.log('💨 تسريع جماعي!');
            },
            () => {
                // توهج الأسماك
                this.fish.forEach(fish => {
                    fish.glowIntensity = 1;
                    fish.isWild = true;
                });
                console.log('✨ توهج الأسماك!');
            }
        ];

        const randomEvent = events[Math.floor(Math.random() * events.length)];
        randomEvent();
    }

    resetDemo() {
        // إعادة تشغيل التجربة
        this.clearAllFish();

        // إعادة تعيين البيئة
        this.environment.treasures.forEach(t => t.discovered = false);

        // إضافة أسماك تجريبية جديدة
        setTimeout(() => {
            this.addDemoFish();
            this.addRandomPlayers(10);
        }, 1000);

        console.log('🔄 تم إعادة تشغيل التجربة');
    }

    addDemoFish() {
        // إضافة أسماك تجريبية متنوعة
        const demoPlayers = [
            { name: 'اللاعب التجريبي 1', roses: 2 },
            { name: 'اللاعب التجريبي 2', roses: 5 },
            { name: 'اللاعب التجريبي 3', roses: 1 },
            { name: 'اللاعب التجريبي 4', roses: 8 },
            { name: 'اللاعب التجريبي 5', roses: 15 },
            { name: 'اللاعب التجريبي 6', roses: 3 },
            { name: 'اللاعب التجريبي 7', roses: 12 },
            { name: 'اللاعب التجريبي 8', roses: 25 }
        ];

        demoPlayers.forEach((player, index) => {
            setTimeout(() => {
                this.createFish(player.name, player.roses);
            }, index * 800);
        });

        console.log('🧪 تم إضافة أسماك تجريبية متنوعة');
    }

    clearAllFish() {
        // مسح جميع الأسماك والجزيئات
        this.fish = [];
        this.particles = [];
        this.players.clear();

        // إعادة تعيين الإحصائيات
        this.gameState.totalFish = 0;
        this.gameState.battles = 0;
        this.gameState.rosesReceived = 0;
        this.gameState.activeBattles = 0;
        this.gameState.totalDeaths = 0;

        // إعادة تعيين البيئة
        this.environment.treasures.forEach(treasure => {
            treasure.discovered = false;
        });

        // تحديث الواجهة فوراً
        this.updateUI();

        console.log('🗑️ تم مسح جميع الأسماك والإحصائيات');
    }

    toggleBattles() {
        // تفعيل معركة فورية بين الأسماك الموجودة
        if (this.fish.length < 2) {
            console.log('⚔️ يحتاج إلى سمكتين على الأقل للمعركة');
            return;
        }

        // جعل جميع الأسماك في وضع قتال
        this.fish.forEach(fish => {
            fish.isHunting = true;
            fish.aggression = 1.0;
            fish.speedBoost = 1.5;
            fish.speedBoostTime = 300;
            fish.behavior = 'hunt';

            // العثور على هدف عشوائي
            const otherFish = this.fish.filter(f => f !== fish);
            if (otherFish.length > 0) {
                const target = otherFish[Math.floor(Math.random() * otherFish.length)];
                fish.targetX = target.x;
                fish.targetY = target.y;
            }
        });

        console.log('⚔️ تم تفعيل معركة شاملة بين جميع الأسماك!');
    }

    // اختبار جميع الهدايا
    testAllGifts() {
        const gifts = ['Rose', 'Diamond', 'Crown', 'Lightning', 'Butterfly', 'Star', 'Heart', 'Fire', 'Ice', 'Thunder'];
        const testPlayer = 'مختبر_الهدايا';

        console.log('🎁 بدء اختبار جميع الهدايا...');

        gifts.forEach((gift, index) => {
            setTimeout(() => {
                const giftData = {
                    giftName: gift,
                    giftId: 5000 + index,
                    repeatCount: Math.floor(Math.random() * 10) + 1,
                    nickname: testPlayer,
                    uniqueId: testPlayer + '_' + index
                };

                this.handleGiftReceived(giftData);
                console.log(`🎁 تم اختبار هدية: ${gift} (${giftData.repeatCount} مرات)`);
            }, index * 1000);
        });

        setTimeout(() => {
            console.log('✅ تم اختبار جميع الهدايا بنجاح!');
        }, gifts.length * 1000 + 500);
    }

    // اختبار نظام التعيينات
    testGiftAssignments() {
        console.log('⚙️ بدء اختبار نظام التعيينات...');

        // محاكاة تعيينات مختلفة
        const testAssignments = [
            { gift: 'Rose', expectedType: 'random', count: 1 },
            { gift: 'Diamond', expectedType: 'shark', count: 5 },
            { gift: 'Crown', expectedType: 'whale', count: 10 },
            { gift: 'Lightning', expectedType: 'eel', count: 3 },
            { gift: 'Butterfly', expectedType: 'jellyfish', count: 2 },
            { gift: 'Star', expectedType: 'salmon', count: 1 }
        ];

        testAssignments.forEach((test, index) => {
            setTimeout(() => {
                const testPlayer = `مختبر_التعيين_${index}`;

                // محاكاة استقبال الهدية
                const giftData = {
                    giftName: test.gift,
                    giftId: 6000 + index,
                    repeatCount: test.count,
                    nickname: testPlayer,
                    uniqueId: testPlayer
                };

                this.handleGiftReceived(giftData);

                // التحقق من النتيجة
                setTimeout(() => {
                    const playerFish = this.fish.filter(f => f.playerName === testPlayer);
                    if (playerFish.length > 0) {
                        const fishType = playerFish[0].type;
                        const success = test.expectedType === 'random' || fishType === test.expectedType;
                        console.log(`${success ? '✅' : '❌'} ${test.gift} → ${fishType} (متوقع: ${test.expectedType})`);
                    }
                }, 500);
            }, index * 1500);
        });

        setTimeout(() => {
            console.log('✅ تم اختبار نظام التعيينات بنجاح!');
        }, testAssignments.length * 1500 + 1000);
    }

    // اختبار شامل للمخلوقات الجديدة
    testAllCreatures() {
        const creatures = ['seahorse', 'turtle', 'crab', 'lobster', 'starfish', 'anglerfish', 'swordfish', 'hammerhead'];

        console.log('🐟 بدء اختبار جميع المخلوقات الجديدة...');

        creatures.forEach((creature, index) => {
            setTimeout(() => {
                this.createSpecificFish(creature);
                console.log(`🐟 تم إنشاء: ${creature}`);
            }, index * 800);
        });

        setTimeout(() => {
            console.log('✅ تم اختبار جميع المخلوقات الجديدة!');
        }, creatures.length * 800 + 500);
    }

    // اختبار الأصوات المرعبة
    testScarySound() {
        const scaryTypes = ['shark', 'whale', 'eel', 'jellyfish', 'octopus'];

        console.log('🔊 بدء اختبار الأصوات المرعبة...');

        scaryTypes.forEach((type, index) => {
            setTimeout(() => {
                this.createSpecificFish(type);
                console.log(`🔊 تم تشغيل صوت: ${type}`);
            }, index * 2000);
        });
    }

    // اختبار أصوات المحيط
    testAmbientSounds() {
        console.log('🌊 بدء اختبار أصوات المحيط...');

        const ambientSounds = [
            { name: 'ocean_waves', label: 'أمواج البحر' },
            { name: 'underwater_bubbles', label: 'فقاعات تحت الماء' },
            { name: 'deep_sea', label: 'أعماق البحر' },
            { name: 'coral_reef', label: 'الشعاب المرجانية' },
            { name: 'sea_current', label: 'التيارات المائية' },
            { name: 'whale_song', label: 'أغنية الحوت' },
            { name: 'dolphin_clicks', label: 'أصوات الدلافين' }
        ];

        // اختبار كل صوت 3 مرات للتأكد من عمله
        ambientSounds.forEach((sound, index) => {
            for (let i = 0; i < 3; i++) {
                setTimeout(() => {
                    this.playAmbientSound(sound.name, 0.5);
                    console.log(`🌊 تشغيل صوت (${i + 1}/3): ${sound.label}`);
                }, (index * 3 + i) * 1000);
            }
        });

        setTimeout(() => {
            console.log('✅ تم اختبار جميع أصوات المحيط 3 مرات لكل صوت!');
        }, ambientSounds.length * 3 * 1000 + 1000);
    }

    // اختبار الأصوات المخصصة
    testCustomSounds() {
        console.log('🎵 بدء اختبار الأصوات المخصصة...');

        if (!this.customSoundBuffers || this.customSoundBuffers.size === 0) {
            console.log('⚠️ لا توجد أصوات مخصصة محملة. ارفع أصوات مخصصة من صفحة الإعدادات أولاً.');
            return;
        }

        const customSounds = Array.from(this.customSoundBuffers.keys());
        console.log(`🎵 تم العثور على ${customSounds.length} صوت مخصص:`, customSounds);

        // اختبار كل صوت مخصص 5 مرات للتأكد من عمله
        customSounds.forEach((soundName, index) => {
            for (let i = 0; i < 5; i++) {
                setTimeout(() => {
                    // تحديد نوع الصوت
                    const isAmbient = ['ocean_waves', 'underwater_bubbles', 'deep_sea', 'coral_reef', 'sea_current', 'whale_song', 'dolphin_clicks'].includes(soundName);

                    if (isAmbient) {
                        this.playAmbientSound(soundName, 0.7);
                        console.log(`🌊 تشغيل صوت محيط مخصص (${i + 1}/5): ${soundName}`);
                    } else {
                        this.playSound(soundName, 0.7);
                        console.log(`🎵 تشغيل صوت مخصص (${i + 1}/5): ${soundName}`);
                    }
                }, (index * 5 + i) * 800);
            }
        });

        setTimeout(() => {
            console.log('✅ تم اختبار جميع الأصوات المخصصة 5 مرات لكل صوت!');
            console.log('📊 إحصائيات الاختبار:');
            console.log(`- عدد الأصوات المخصصة: ${customSounds.length}`);
            console.log(`- إجمالي مرات التشغيل: ${customSounds.length * 5}`);
            console.log('🎯 إذا لم تسمع الأصوات، تحقق من:');
            console.log('  1. مستوى الصوت في المتصفح');
            console.log('  2. إعدادات الصوت في اللعبة');
            console.log('  3. صحة الملفات الصوتية المرفوعة');
        }, customSounds.length * 5 * 800 + 2000);
    }

    // اختبار التأثيرات الخاصة
    testSpecialEffects() {
        console.log('✨ بدء اختبار التأثيرات الخاصة...');

        // إنشاء أسماك مع تأثيرات مختلفة
        const effects = [
            { type: 'eel', effect: 'electric' },
            { type: 'jellyfish', effect: 'poison' },
            { type: 'octopus', effect: 'tentacle_grab' },
            { type: 'anglerfish', effect: 'lure_trap' },
            { type: 'swordfish', effect: 'sword_strike' }
        ];

        effects.forEach((effect, index) => {
            setTimeout(() => {
                const fish = this.createSpecificFish(effect.type);
                if (fish) {
                    // تفعيل التأثير الخاص
                    fish.isWild = true;
                    fish.specialCooldown = 0;
                    console.log(`✨ تم تفعيل تأثير ${effect.effect} على ${effect.type}`);
                }
            }, index * 1000);
        });
    }
}

// بدء اللعبة عند تحميل الصفحة
let game;
window.addEventListener('load', () => {
    game = new FishEatFishGame();
    window.game = game; // تعيين المتغير العام للوصول من وضع التجربة
    console.log('🎮 تم تحميل اللعبة وتعيين المتغير العام window.game');
});