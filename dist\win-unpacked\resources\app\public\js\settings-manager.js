/**
 * نظام إدارة الإعدادات المركزي
 * يتعامل مع تحميل وحفظ الإعدادات في ملفات محلية
 */

// كائن عام لتخزين جميع الإعدادات في الذاكرة
const SettingsStore = {
  // الإعدادات المحملة
  settings: {},

  // حالة التحميل
  isLoaded: false,

  // قائمة الدوال التي ستنفذ عند اكتمال تحميل الإعدادات
  onLoadCallbacks: [],

  // إظهار مؤشر التحميل
  showLoadingIndicator() {
    // التحقق من وجود مؤشر التحميل
    let loadingOverlay = document.getElementById('loading-overlay');

    // إنشاء مؤشر التحميل إذا لم يكن موجوداً
    if (!loadingOverlay) {
      loadingOverlay = document.createElement('div');
      loadingOverlay.id = 'loading-overlay';
      loadingOverlay.className = 'loading-overlay';

      const spinner = document.createElement('div');
      spinner.className = 'loading-spinner';

      const message = document.createElement('div');
      message.className = 'loading-message';
      message.textContent = 'جاري تحميل الإعدادات...';

      loadingOverlay.appendChild(spinner);
      loadingOverlay.appendChild(message);

      document.body.appendChild(loadingOverlay);
    }

    // إظهار مؤشر التحميل
    setTimeout(() => {
      loadingOverlay.classList.add('active');
    }, 0);
  },

  // إخفاء مؤشر التحميل
  hideLoadingIndicator() {
    const loadingOverlay = document.getElementById('loading-overlay');
    if (loadingOverlay) {
      loadingOverlay.classList.remove('active');
    }
  },

  // تحميل الإعدادات من التخزين المحلي
  loadFromLocalStorage() {
    try {
      const savedSettings = localStorage.getItem('app_settings');
      if (savedSettings) {
        this.settings = JSON.parse(savedSettings);
        return true;
      }
    } catch (error) {
      console.error('خطأ في تحميل الإعدادات من التخزين المحلي:', error);
    }
    return false;
  },

  // حفظ الإعدادات في التخزين المحلي
  saveToLocalStorage() {
    try {
      localStorage.setItem('app_settings', JSON.stringify(this.settings));
      return true;
    } catch (error) {
      console.error('خطأ في حفظ الإعدادات في التخزين المحلي:', error);
      return false;
    }
  },

  // تحميل الإعدادات من الخادم
  loadFromServer() {
    return new Promise((resolve) => {
      // إذا لم يكن هناك اتصال بالخادم، استخدم الإعدادات المحلية
      if (!window.socket || !window.socket.connected) {
        console.warn('لا يوجد اتصال بالخادم، استخدام الإعدادات المحلية');
        resolve(false);
        return;
      }

      // تعيين مهلة زمنية للاتصال
      const timeout = setTimeout(() => {
        console.warn('انتهت مهلة الاتصال بالخادم، استخدام الإعدادات المحلية');
        resolve(false);
      }, 2000);

      // طلب الإعدادات من الخادم
      window.socket.emit('getAllSettings', (response) => {
        clearTimeout(timeout);

        if (response && response.success) {
          // دمج الإعدادات الجديدة مع الإعدادات الحالية
          this.settings = { ...this.settings, ...response.data };
          this.saveToLocalStorage();
          resolve(true);
        } else {
          console.warn('فشل في الحصول على الإعدادات من الخادم:', response?.error || 'خطأ غير معروف');
          resolve(false);
        }
      });
    });
  },

  // تهيئة نظام الإعدادات
  async initialize() {
    // تحميل الإعدادات من التخزين المحلي أولاً
    this.loadFromLocalStorage();

    // تعيين الإعدادات الافتراضية إذا لم تكن موجودة
    this.ensureDefaultSettings();

    // محاولة تحميل الإعدادات من الخادم
    try {
      await this.loadFromServer();
    } catch (error) {
      console.error('خطأ في تحميل الإعدادات من الخادم:', error);
    }

    // تعيين حالة التحميل إلى مكتمل
    this.isLoaded = true;

    // تنفيذ الدوال المسجلة
    this.onLoadCallbacks.forEach(callback => {
      try {
        callback(this.settings);
      } catch (error) {
        console.error('خطأ في تنفيذ دالة callback:', error);
      }
    });

    // تفريغ قائمة الدوال
    this.onLoadCallbacks = [];

    return this.settings;
  },

  // التأكد من وجود الإعدادات الافتراضية
  ensureDefaultSettings() {
    // إعدادات عامة
    if (!this.settings.general) {
      this.settings.general = {
        theme: 'light',
        language: 'ar'
      };
    }

    // إعدادات الملفات الشخصية
    if (!this.settings.profiles) {
      this.settings.profiles = {
        profiles: [
          {
            id: 'default',
            name: 'الملف الافتراضي',
            created: new Date().toISOString(),
            lastModified: new Date().toISOString()
          }
        ],
        activeProfile: 'default'
      };
    }

    // إعدادات ربط الهدايا
    if (!this.settings.giftMappings) {
      this.settings.giftMappings = {
        mappings: []
      };
    }

    // إعدادات شاشة العرض
    if (!this.settings.overlay) {
      this.settings.overlay = {
        alertPosition: 'top-right',
        width: 30,
        opacity: 80,
        maxItems: 5,
        backgroundColor: '#000000',
        textColor: '#ffffff',
        accentColor: '#ff3b5c',
        fontFamily: "'Tajawal', sans-serif",
        fontSize: 16,
        enableAnimations: true,
        sound: {
          enabled: false,
          volume: 50
        }
      };
    }

    // إعدادات قراءة التعليقات الصوتية
    if (!this.settings.tts) {
      this.settings.tts = {
        enabled: true,
        voice: 'ar-SA-HamedNeural',
        rate: 1,
        pitch: 1,
        volume: 1
      };
    }
  },

  // الحصول على الإعدادات
  get(key) {
    if (!key) return this.settings;

    const keys = key.split('.');
    let value = this.settings;

    for (const k of keys) {
      if (value === undefined || value === null) return undefined;
      value = value[k];
    }

    return value;
  },

  // تعيين الإعدادات
  set(key, value) {
    return new Promise((resolve) => {
      if (!key) {
        this.settings = value;
        this.saveToLocalStorage();
        resolve(true);
        return;
      }

      const keys = key.split('.');
      const lastKey = keys.pop();
      let target = this.settings;

      for (const k of keys) {
        if (target[k] === undefined || target[k] === null) {
          target[k] = {};
        }
        target = target[k];
      }

      target[lastKey] = value;
      this.saveToLocalStorage();

      // إرسال الإعدادات إلى الخادم إذا كان متصلاً
      this.saveToServer(key);

      resolve(true);
    });
  },

  // حفظ الإعدادات في الخادم
  saveToServer(key) {
    // إذا لم يكن هناك اتصال بالخادم، لا تفعل شيئاً
    if (!window.socket || !window.socket.connected) {
      return false;
    }

    // تحديد نوع الإعدادات المراد حفظها
    let settingsType = 'general';
    if (key.startsWith('profiles')) {
      settingsType = 'profiles';
    } else if (key.startsWith('giftMappings')) {
      settingsType = 'giftMappings';
    } else if (key.startsWith('overlay')) {
      settingsType = 'overlay';
    } else if (key.startsWith('tts')) {
      settingsType = 'tts';
    }

    // إرسال الإعدادات إلى الخادم
    window.socket.emit('saveSettings', { type: settingsType, data: this.settings[settingsType] });

    return true;
  },

  // تسجيل دالة لتنفيذها عند اكتمال تحميل الإعدادات
  onLoad(callback) {
    if (this.isLoaded) {
      // إذا كانت الإعدادات محملة بالفعل، نفذ الدالة فوراً
      callback(this.settings);
    } else {
      // وإلا، أضف الدالة إلى قائمة الانتظار
      this.onLoadCallbacks.push(callback);
    }
  }
};

// تصدير الكائن
window.SettingsManager = SettingsStore;

// تهيئة نظام الإعدادات عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', () => {
  // إذا كان هناك اتصال Socket.IO، انتظر حتى يتم الاتصال
  if (window.socket) {
    window.socket.on('connect', () => {
      SettingsStore.initialize();
    });

    // إذا كان الاتصال موجوداً بالفعل، ابدأ التهيئة
    if (window.socket.connected) {
      SettingsStore.initialize();
    }
  } else {
    // إذا لم يكن هناك اتصال Socket.IO، استخدم الإعدادات المحلية فقط
    SettingsStore.initialize();
  }
});
