/**
 * sidebar-enhanced.css
 * تنسيقات محسنة للشريط الجانبي لجعله أكثر احترافية وجمالاً
 */

/* تنسيق الشريط الجانبي الأساسي */
.sidebar {
  width: 280px;
  background: linear-gradient(180deg, #ffffff 0%, #f8f9fa 100%);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.1);
  padding: 0;
  position: fixed;
  height: 100%;
  right: 0;
  top: 0;
  overflow-y: auto;
  z-index: 1000;
  border-left: 1px solid rgba(0, 0, 0, 0.05);
  border-top-left-radius: 15px;
  border-bottom-left-radius: 15px;
  transition: all 0.3s ease;
}

/* تنسيق الشعار */
.logo {
  text-align: center;
  padding: 25px 15px;
  margin-bottom: 10px;
  background: linear-gradient(135deg, #ff3b5c 0%, #ff6b87 100%);
  box-shadow: 0 4px 10px rgba(255, 59, 92, 0.2);
  position: relative;
  border-top-left-radius: 15px;
  overflow: hidden;
}

.logo::before {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(255,255,255,0.3) 0%, rgba(255,255,255,0) 70%);
  opacity: 0;
  transform: rotate(30deg);
  transition: opacity 0.5s ease, transform 1s ease;
}

.logo:hover::before {
  opacity: 1;
  transform: rotate(0deg);
}

.logo h3 {
  color: white;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.logo:hover h3 {
  transform: scale(1.05);
}

.logo::after {
  content: '';
  position: absolute;
  bottom: -10px;
  right: 50%;
  transform: translateX(50%);
  width: 20px;
  height: 20px;
  background: #ff3b5c;
  transform: rotate(45deg) translateX(50%);
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.1);
  z-index: -1;
}

.logo img {
  max-width: 80%;
  height: auto;
  transition: transform 0.3s ease;
}

.logo:hover img {
  transform: scale(1.05);
}

/* تنسيق قائمة التنقل */
.nav-menu {
  display: flex;
  flex-direction: column;
  gap: 10px;
  padding: 20px 15px;
}

.nav-menu a {
  display: flex;
  align-items: center;
  padding: 14px 18px;
  color: #444;
  text-decoration: none;
  border-radius: 12px;
  transition: all 0.3s ease;
  font-weight: 500;
  position: relative;
  overflow: hidden;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  border: 1px solid rgba(0, 0, 0, 0.03);
  white-space: nowrap;
  font-size: 15px;
}

.nav-menu a::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 3px;
  height: 100%;
  background: #ff3b5c;
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.nav-menu a::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at center, rgba(255, 59, 92, 0.1) 0%, rgba(255, 59, 92, 0) 70%);
  opacity: 0;
  transition: opacity 0.3s ease;
  z-index: -1;
}

.nav-menu a:hover {
  background-color: #f0f4f8;
  color: #ff3b5c;
  transform: translateY(-2px);
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.05);
}

.nav-menu a:hover::before {
  transform: scaleY(1);
}

.nav-menu a:hover::after {
  opacity: 1;
}

.nav-menu a.active {
  background: linear-gradient(45deg, #ff3b5c 0%, #ff6b87 100%);
  color: white;
  box-shadow: 0 5px 15px rgba(255, 59, 92, 0.25);
  border: none;
}

.nav-menu a.active::before {
  transform: scaleY(0);
}

.nav-icon {
  margin-left: 15px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 30px;
  height: 30px;
  background-color: rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.nav-menu a:hover .nav-icon {
  background-color: rgba(255, 59, 92, 0.1);
  transform: rotate(5deg);
}

.nav-menu a.active .nav-icon {
  background-color: rgba(255, 255, 255, 0.2);
}

/* توحيد هامش المحتوى الرئيسي */
.main-content {
  margin-right: 280px;
  padding: 20px;
}

/* تحسينات للوضع المظلم */
[data-theme="dark"] .sidebar {
  background: linear-gradient(180deg, #1a1a1a 0%, #2a2a2a 100%);
  border-left-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

[data-theme="dark"] .nav-menu a {
  color: #e0e0e0;
  border-color: rgba(255, 255, 255, 0.05);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .nav-menu a:hover {
  background-color: #333;
  box-shadow: 0 5px 10px rgba(0, 0, 0, 0.15);
}

/* تحسينات خاصة لقسم اتصل بنا في الوضع المظلم */
[data-theme="dark"] .nav-menu a[href="/contact.html"] {
  background-color: rgba(255, 59, 92, 0.1);
  border-color: rgba(255, 59, 92, 0.2);
}

[data-theme="dark"] .nav-menu a[href="/contact.html"]:hover {
  background-color: rgba(255, 59, 92, 0.2);
}

[data-theme="dark"] .nav-menu a[href="/contact.html"].active {
  background: linear-gradient(45deg, #ff3b5c 0%, #ff6b87 100%);
  color: white;
}

/* تأثير نبض للشريط الجانبي عند التحميل */
@keyframes sidebar-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 59, 92, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(255, 59, 92, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 59, 92, 0);
  }
}

.sidebar-loaded {
  animation: sidebar-pulse 1s ease-out;
}
