<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>⚙️ إعدادات صراع الأعلام</title>
  <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
  <style>
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    body {
      font-family: '<PERSON><PERSON><PERSON>', sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;
      min-height: 100vh;
      padding: 20px;
    }

    .container {
      max-width: 800px;
      margin: 0 auto;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 20px;
      padding: 30px;
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
    }

    h1 {
      text-align: center;
      margin-bottom: 30px;
      font-size: 2.5rem;
      color: #ffd700;
      text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    }

    .settings-section {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 15px;
      padding: 25px;
      margin-bottom: 20px;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .section-title {
      font-size: 1.3rem;
      font-weight: 600;
      margin-bottom: 20px;
      color: #00ff7f;
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .setting-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding: 15px;
      background: rgba(255, 255, 255, 0.05);
      border-radius: 10px;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .setting-label {
      font-weight: 500;
      flex: 1;
    }

    .setting-control {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    /* مفاتيح التبديل */
    .toggle-switch {
      position: relative;
      width: 60px;
      height: 30px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 15px;
      cursor: pointer;
      transition: all 0.3s ease;
    }

    .toggle-switch.active {
      background: #00ff7f;
    }

    .toggle-switch::after {
      content: '';
      position: absolute;
      top: 3px;
      left: 3px;
      width: 24px;
      height: 24px;
      background: white;
      border-radius: 50%;
      transition: all 0.3s ease;
    }

    .toggle-switch.active::after {
      transform: translateX(30px);
    }

    /* شرائح التمرير */
    .slider {
      width: 150px;
      height: 6px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 3px;
      outline: none;
      cursor: pointer;
    }

    .slider::-webkit-slider-thumb {
      appearance: none;
      width: 20px;
      height: 20px;
      background: #00ff7f;
      border-radius: 50%;
      cursor: pointer;
    }

    .slider::-moz-range-thumb {
      width: 20px;
      height: 20px;
      background: #00ff7f;
      border-radius: 50%;
      cursor: pointer;
      border: none;
    }

    /* قوائم الاختيار */
    .select-box {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 8px;
      padding: 8px 12px;
      color: white;
      font-family: 'Tajawal', sans-serif;
      min-width: 120px;
    }

    .select-box option {
      background: #333;
      color: white;
    }

    /* أزرار */
    .btn {
      background: linear-gradient(45deg, #ff6b87, #ff3b5c);
      border: none;
      border-radius: 10px;
      padding: 12px 24px;
      color: white;
      font-family: 'Tajawal', sans-serif;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.3s ease;
      margin: 5px;
    }

    .btn:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(255, 59, 92, 0.4);
    }

    .btn-secondary {
      background: linear-gradient(45deg, #667eea, #764ba2);
    }

    .btn-success {
      background: linear-gradient(45deg, #00ff7f, #00cc66);
    }

    .btn-warning {
      background: linear-gradient(45deg, #ffd700, #ffed4e);
      color: #333;
    }

    .actions {
      text-align: center;
      margin-top: 30px;
      padding-top: 20px;
      border-top: 1px solid rgba(255, 255, 255, 0.2);
    }

    .value-display {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 5px;
      padding: 5px 10px;
      min-width: 40px;
      text-align: center;
      font-weight: bold;
      color: #ffd700;
    }

    /* تصميم متجاوب */
    @media (max-width: 768px) {
      .container {
        padding: 20px;
        margin: 10px;
      }

      .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
      }

      .setting-control {
        width: 100%;
        justify-content: space-between;
      }

      h1 {
        font-size: 2rem;
      }
    }

    /* رسائل التنبيه */
    .alert {
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      background: linear-gradient(45deg, #00ff7f, #00cc66);
      color: white;
      padding: 15px 25px;
      border-radius: 10px;
      font-weight: 500;
      z-index: 1000;
      animation: slideDown 0.3s ease;
    }

    @keyframes slideDown {
      from {
        transform: translateX(-50%) translateY(-100%);
        opacity: 0;
      }
      to {
        transform: translateX(-50%) translateY(0);
        opacity: 1;
      }
    }

    /* إعدادات الأعلام المخصصة */
    .country-item {
      background: rgba(255, 255, 255, 0.05);
      border-radius: 10px;
      padding: 20px;
      margin-bottom: 15px;
      border: 1px solid rgba(255, 255, 255, 0.1);
    }

    .country-header {
      display: flex;
      align-items: center;
      gap: 15px;
      margin-bottom: 15px;
      font-weight: 600;
      color: #ffd700;
    }

    .country-number {
      font-weight: bold;
      color: #4CAF50;
      font-size: 18px;
      min-width: 30px;
      background: rgba(76, 175, 80, 0.1);
      padding: 4px 8px;
      border-radius: 6px;
      border: 1px solid rgba(76, 175, 80, 0.3);
    }

    .join-command {
      background: rgba(76, 175, 80, 0.2);
      color: #4CAF50;
      padding: 4px 12px;
      border-radius: 15px;
      font-size: 12px;
      font-weight: bold;
      border: 1px solid rgba(76, 175, 80, 0.3);
      margin-left: auto;
      font-family: 'Courier New', monospace;
    }

    .country-flag-preview {
      width: 40px;
      height: 30px;
      border-radius: 5px;
      object-fit: cover;
      border: 2px solid rgba(255, 255, 255, 0.3);
    }

    .country-inputs {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 15px;
      margin-bottom: 15px;
    }

    .input-group {
      display: flex;
      flex-direction: column;
      gap: 5px;
    }

    .input-group label {
      font-size: 0.9rem;
      color: #ccc;
    }

    .text-input {
      background: rgba(255, 255, 255, 0.1);
      border: 1px solid rgba(255, 255, 255, 0.3);
      border-radius: 8px;
      padding: 10px;
      color: white;
      font-family: 'Tajawal', sans-serif;
    }

    .text-input:focus {
      outline: none;
      border-color: #00ff7f;
      box-shadow: 0 0 10px rgba(0, 255, 127, 0.3);
    }

    .file-input {
      display: none;
    }

    .file-input-label {
      background: linear-gradient(45deg, #4080ff, #6060ff);
      border: none;
      border-radius: 8px;
      padding: 10px 15px;
      color: white;
      font-family: 'Tajawal', sans-serif;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
      display: block;
    }

    .file-input-label:hover {
      transform: translateY(-2px);
      box-shadow: 0 5px 15px rgba(64, 128, 255, 0.4);
    }

    .country-actions {
      display: flex;
      gap: 10px;
      justify-content: flex-end;
    }

    .btn-small {
      padding: 8px 16px;
      font-size: 0.9rem;
    }

    /* معاينة الأرض */
    .floor-preview {
      width: 100px;
      height: 60px;
      border-radius: 8px;
      border: 2px solid rgba(255, 255, 255, 0.3);
      display: flex;
      align-items: center;
      justify-content: center;
      background: #2d5016;
      color: white;
      font-size: 0.8rem;
      text-align: center;
      background-size: cover;
      background-position: center;
      background-repeat: no-repeat;
    }

    .floor-preview.has-image {
      background-color: transparent;
    }

    .floor-preview.has-image span {
      display: none;
    }

    @media (max-width: 768px) {
      .country-inputs {
        grid-template-columns: 1fr;
      }

      .country-header {
        flex-direction: column;
        align-items: flex-start;
      }
    }

    /* أنماط النافذة المنبثقة */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.7);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }

    .modal-content {
      background: #2a2a2a;
      border-radius: 10px;
      padding: 0;
      max-width: 500px;
      width: 90%;
      max-height: 80vh;
      overflow-y: auto;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    }

    .modal-header {
      background: #333;
      padding: 15px 20px;
      border-radius: 10px 10px 0 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #444;
    }

    .modal-header h3 {
      margin: 0;
      color: #fff;
      font-size: 18px;
    }

    .modal-close {
      background: none;
      border: none;
      color: #fff;
      font-size: 24px;
      cursor: pointer;
      padding: 0;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: background 0.3s;
    }

    .modal-close:hover {
      background: rgba(255, 255, 255, 0.1);
    }

    .modal-body {
      padding: 20px;
    }

    .form-group {
      margin-bottom: 15px;
    }

    .form-group label {
      display: block;
      color: #fff;
      margin-bottom: 5px;
      font-weight: bold;
    }

    .form-group input,
    .form-group select {
      width: 100%;
      padding: 10px;
      border: 1px solid #555;
      border-radius: 5px;
      background: #1a1a1a;
      color: #fff;
      font-size: 14px;
    }

    .form-group input:focus,
    .form-group select:focus {
      outline: none;
      border-color: #4CAF50;
      box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
    }

    .modal-footer {
      padding: 15px 20px;
      border-top: 1px solid #444;
      display: flex;
      gap: 10px;
      justify-content: flex-end;
    }

    .modal-footer .btn {
      padding: 8px 16px;
      font-size: 14px;
    }

    /* نصائح الإعدادات */
    .setting-hint {
      font-size: 12px;
      color: #888;
      margin-top: 5px;
      font-style: italic;
    }
  </style>
</head>
<body>
  <div class="container">
    <h1>⚙️ إعدادات صراع الأعلام</h1>

    <!-- إعدادات الرسوميات -->
    <div class="settings-section">
      <div class="section-title">
        🎨 إعدادات الرسوميات
      </div>

      <div class="setting-item">
        <div class="setting-label">تفعيل التأثيرات البصرية</div>
        <div class="setting-control">
          <div class="toggle-switch active" data-setting="particleEffects"></div>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">الدوران التلقائي للمكعبات</div>
        <div class="setting-control">
          <div class="toggle-switch active" data-setting="autoRotate"></div>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">سرعة الدوران</div>
        <div class="setting-control">
          <input type="range" class="slider" min="0.001" max="0.05" step="0.001" value="0.01" data-setting="rotationSpeed">
          <div class="value-display" id="rotationSpeedValue">0.01</div>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">الحد الأقصى لحجم المكعب</div>
        <div class="setting-control">
          <input type="range" class="slider" min="2" max="10" step="0.5" value="5" data-setting="maxCubeSize">
          <div class="value-display" id="maxCubeSizeValue">5</div>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">معدل النمو</div>
        <div class="setting-control">
          <input type="range" class="slider" min="0.001" max="0.01" step="0.001" value="0.002" data-setting="growthRate">
          <div class="value-display" id="growthRateValue">0.002</div>
        </div>
      </div>
    </div>

    <!-- إعدادات الصوت -->
    <div class="settings-section">
      <div class="section-title">
        🔊 إعدادات الصوت
      </div>

      <div class="setting-item">
        <div class="setting-label">تفعيل الأصوات</div>
        <div class="setting-control">
          <div class="toggle-switch active" data-setting="soundEnabled"></div>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">مستوى الصوت</div>
        <div class="setting-control">
          <input type="range" class="slider" min="0" max="100" step="5" value="50" data-setting="soundVolume">
          <div class="value-display" id="soundVolumeValue">50</div>
        </div>
      </div>
    </div>

    <!-- إعدادات اللعبة -->
    <div class="settings-section">
      <div class="section-title">
        🎮 إعدادات اللعبة
      </div>

      <div class="setting-item">
        <div class="setting-label">وضع التجربة</div>
        <div class="setting-control">
          <div class="toggle-switch" data-setting="demoMode"></div>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">سرعة التجربة (ثانية)</div>
        <div class="setting-control">
          <input type="range" class="slider" min="1" max="10" step="1" value="2" data-setting="demoSpeed">
          <div class="value-display" id="demoSpeedValue">2</div>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">عدد الدول المعروضة</div>
        <div class="setting-control">
          <select class="select-box" data-setting="countryCount">
            <option value="4">4 دول</option>
            <option value="6">6 دول</option>
            <option value="8" selected>8 دول</option>
            <option value="10">10 دول</option>
            <option value="12">12 دولة</option>
          </select>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">🏆 إظهار ترتيب الدول</div>
        <div class="setting-control">
          <div class="toggle-switch active" data-setting="showLeaderboard"></div>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">ترتيب المكعبات</div>
        <div class="setting-control">
          <select class="select-box" data-setting="cubeArrangement">
            <option value="circle" selected>دائري</option>
            <option value="grid">شبكي (صفوف وأعمدة)</option>
            <option value="line">خطي</option>
            <option value="double_circle">دائرة مزدوجة</option>
          </select>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">المسافة بين المكعبات</div>
        <div class="setting-control">
          <input type="range" class="slider" min="3" max="20" step="1" value="8" data-setting="cubeSpacing">
          <div class="value-display" id="cubeSpacingValue">8</div>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">عدد الصفوف (للترتيب الشبكي)</div>
        <div class="setting-control">
          <input type="range" class="slider" min="1" max="6" step="1" value="3" data-setting="gridRows">
          <div class="value-display" id="gridRowsValue">3</div>
          <div class="setting-hint">للـ12 دولة: 3 صفوف × 4 أعمدة أو 4 صفوف × 3 أعمدة</div>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">عدد الأعمدة (للترتيب الشبكي)</div>
        <div class="setting-control">
          <input type="range" class="slider" min="2" max="8" step="1" value="4" data-setting="gridCols">
          <div class="value-display" id="gridColsValue">4</div>
          <div class="setting-hint">للـ12 دولة: 4 أعمدة × 3 صفوف أو 6 أعمدة × 2 صف</div>
        </div>
      </div>
    </div>

    <!-- إعدادات الكاميرا -->
    <div class="settings-section">
      <div class="section-title">
        🎥 إعدادات الكاميرا
      </div>

      <div class="setting-item">
        <div class="setting-label">تمكين تحكم الكاميرا</div>
        <div class="setting-control">
          <div class="toggle-switch" data-setting="cameraControls"></div>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">موضع الكاميرا X</div>
        <div class="setting-control">
          <input type="range" class="slider" min="-30" max="30" step="1" value="0" data-setting="cameraX">
          <div class="value-display" id="cameraXValue">0</div>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">موضع الكاميرا Y (الارتفاع)</div>
        <div class="setting-control">
          <input type="range" class="slider" min="1" max="30" step="1" value="8" data-setting="cameraY">
          <div class="value-display" id="cameraYValue">8</div>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">موضع الكاميرا Z (العمق)</div>
        <div class="setting-control">
          <input type="range" class="slider" min="1" max="50" step="1" value="15" data-setting="cameraZ">
          <div class="value-display" id="cameraZValue">15</div>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">اتجاه النظر X</div>
        <div class="setting-control">
          <input type="range" class="slider" min="-20" max="20" step="1" value="0" data-setting="cameraLookAtX">
          <div class="value-display" id="cameraLookAtXValue">0</div>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">اتجاه النظر Y (الارتفاع)</div>
        <div class="setting-control">
          <input type="range" class="slider" min="-10" max="10" step="1" value="0" data-setting="cameraLookAtY">
          <div class="value-display" id="cameraLookAtYValue">0</div>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">اتجاه النظر Z (العمق)</div>
        <div class="setting-control">
          <input type="range" class="slider" min="-20" max="20" step="1" value="0" data-setting="cameraLookAtZ">
          <div class="value-display" id="cameraLookAtZValue">0</div>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">إعادة تعيين الكاميرا</div>
        <div class="setting-control">
          <button class="btn btn-secondary" onclick="resetCameraSettings()">
            🎥 إعادة تعيين الكاميرا
          </button>
        </div>
      </div>
    </div>

    <!-- إعدادات الأعلام المخصصة -->
    <div class="settings-section">
      <div class="section-title">
        🏁 الأعلام والأسماء المخصصة
      </div>

      <div id="customCountriesContainer">
        <!-- سيتم ملء هذا القسم بـ JavaScript -->
      </div>

      <div class="setting-item">
        <div class="setting-label">إضافة دولة جديدة</div>
        <div class="setting-control">
          <button class="btn btn-success" onclick="addNewCountry()">➕ إضافة دولة</button>
        </div>
      </div>
    </div>

    <!-- إعدادات الأرض المخصصة -->
    <div class="settings-section">
      <div class="section-title">
        🌍 تخصيص الأرض
      </div>

      <div class="setting-item">
        <div class="setting-label">صورة الأرض الحالية:</div>
        <div class="setting-control">
          <div id="currentFloorPreview" class="floor-preview">
            <span>لون افتراضي</span>
          </div>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">رفع صورة أرض جديدة:</div>
        <div class="setting-control">
          <input type="file" class="file-input" id="floorImageInput"
                 accept="image/*" onchange="updateFloorImage(this)">
          <label for="floorImageInput" class="file-input-label">
            🖼️ اختيار صورة الأرض
          </label>
        </div>
      </div>

      <div class="setting-item">
        <div class="setting-label">إعادة تعيين الأرض:</div>
        <div class="setting-control">
          <button class="btn btn-warning" onclick="resetFloorImage()">
            🔄 إعادة تعيين للون الافتراضي
          </button>
        </div>
      </div>
    </div>

    <!-- أزرار الإجراءات -->
    <div class="actions">
      <button class="btn btn-success" onclick="saveSettings()">💾 حفظ الإعدادات</button>
      <button class="btn btn-secondary" onclick="resetSettings()">🔄 إعادة تعيين</button>
      <button class="btn btn-warning" onclick="testSettings()">🧪 اختبار الإعدادات</button>
      <button class="btn" onclick="window.close()">❌ إغلاق</button>
    </div>
  </div>

  <!-- Firebase and Auth Scripts -->
  <script type="module" src="/js/firebase-config.js"></script>
  <script>
    // تأخير تحميل auth-guard حتى يتم تحميل Firebase
    setTimeout(() => {
      const script = document.createElement('script');
      script.type = 'module';
      script.src = '/js/auth-guard.js';
      document.head.appendChild(script);
    }, 500);
  </script>
  <script src="/js/flag-settings.js"></script>

  <script>
    // متغيرات حالة الاشتراك
    let currentUser = null;
    let userSubscription = null;
    let hasActiveSubscription = false;
    let authCheckDone = false;
    let hasRedirected = false;

    // التحقق من المصادقة
    window.addEventListener('authStateChanged', (event) => {
      if (authCheckDone || hasRedirected) return;

      const user = event.detail.user;
      console.log('🔐 Flag Settings - Auth state:', user?.email);

      if (!user) {
        console.log('❌ No user, redirecting to auth');
        hasRedirected = true;
        window.location.href = '/auth.html';
      } else if (!user.emailVerified) {
        console.log('❌ Email not verified, redirecting to verification');
        hasRedirected = true;
        window.location.href = '/email-verification.html';
      } else {
        console.log('✅ User authenticated and verified');
        authCheckDone = true;
        currentUser = user;

        // تحميل بيانات الاشتراك
        loadUserSubscription();
      }
    });

    // تحميل بيانات اشتراك المستخدم
    async function loadUserSubscription() {
      try {
        if (window.firebaseHelpers && currentUser) {
          userSubscription = await window.firebaseHelpers.getUserSubscription(currentUser.uid);
          hasActiveSubscription = userSubscription &&
            userSubscription.status === 'active' &&
            new Date() < (userSubscription.endDate?.toDate ? userSubscription.endDate.toDate() : new Date(userSubscription.endDate));

          console.log('User subscription status:', hasActiveSubscription ? 'Active' : 'None');

          if (!hasActiveSubscription) {
            showSubscriptionRequired();
          } else {
            hideSubscriptionMessage();
            enableSettingsPage();
          }
        }
      } catch (error) {
        console.error('Error loading user subscription:', error);
        hasActiveSubscription = false;
        showSubscriptionRequired();
      }
    }

    // عرض رسالة الاشتراك المطلوب
    function showSubscriptionRequired() {
      // إخفاء محتوى الصفحة
      const container = document.querySelector('.container');
      if (container) {
        container.style.display = 'none';
      }

      // إنشاء رسالة الاشتراك
      const subscriptionMessage = document.createElement('div');
      subscriptionMessage.id = 'subscription-message';
      subscriptionMessage.innerHTML = `
        <div style="
          position: fixed;
          top: 0;
          left: 0;
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 10000;
          color: white;
          text-align: center;
          font-family: 'Tajawal', sans-serif;
        ">
          <div style="
            max-width: 600px;
            padding: 40px;
            background: rgba(255,255,255,0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
          ">
            <div style="font-size: 4rem; margin-bottom: 20px;">🔒</div>
            <h1 style="font-size: 2.5rem; margin-bottom: 20px;">إعدادات صراع الأعلام مقفلة</h1>
            <p style="font-size: 1.3rem; margin-bottom: 30px; opacity: 0.9;">
              إعدادات اللعبة متاحة فقط للمشتركين في الباقة الشاملة
            </p>

            <div style="
              background: rgba(255,255,255,0.1);
              border-radius: 15px;
              padding: 25px;
              margin: 30px 0;
            ">
              <h3 style="font-size: 1.5rem; margin-bottom: 20px;">⚙️ ما ستحصل عليه:</h3>
              <div style="text-align: right; margin: 20px 0;">
                <div style="margin: 10px 0;">🎨 إعدادات رسوميات متقدمة</div>
                <div style="margin: 10px 0;">🔊 تحكم كامل في الصوت</div>
                <div style="margin: 10px 0;">🎮 خيارات اللعبة المتقدمة</div>
                <div style="margin: 10px 0;">🎥 تحكم في الكاميرا</div>
                <div style="margin: 10px 0;">🏁 أعلام وأسماء مخصصة</div>
                <div style="margin: 10px 0;">🌍 تخصيص الأرض والخلفية</div>
              </div>
            </div>

            <div style="margin: 30px 0;">
              <div style="
                display: inline-block;
                background: rgba(255,255,255,0.2);
                padding: 15px 25px;
                border-radius: 50px;
                margin-bottom: 10px;
              ">
                <span style="font-size: 2rem; font-weight: bold; color: #00ff7f;">$10</span>
                <span style="font-size: 1rem; opacity: 0.8;"> / شهرياً</span>
              </div>
              <p style="font-size: 1.1rem; opacity: 0.9;">باقة واحدة شاملة - جميع الميزات</p>
            </div>

            <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
              <button onclick="window.location.href='/subscriptions.html'" style="
                background: linear-gradient(45deg, #00ff7f, #00cc66);
                color: white;
                border: none;
                padding: 15px 30px;
                border-radius: 50px;
                font-size: 1.1rem;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                font-family: 'Tajawal', sans-serif;
                box-shadow: 0 10px 20px rgba(0,255,127,0.3);
              " onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 15px 30px rgba(0,255,127,0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 20px rgba(0,255,127,0.3)'">
                ⚡ اشترك الآن
              </button>
              <button onclick="window.location.href='/games.html'" style="
                background: rgba(255,255,255,0.2);
                color: white;
                border: 2px solid rgba(255,255,255,0.3);
                padding: 15px 30px;
                border-radius: 50px;
                font-size: 1.1rem;
                font-weight: bold;
                cursor: pointer;
                transition: all 0.3s ease;
                font-family: 'Tajawal', sans-serif;
                backdrop-filter: blur(10px);
              " onmouseover="this.style.background='rgba(255,255,255,0.3)'; this.style.transform='translateY(-3px)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(0)'">
                🔙 العودة للألعاب
              </button>
            </div>
          </div>
        </div>
      `;

      document.body.appendChild(subscriptionMessage);
    }

    // إخفاء رسالة الاشتراك
    function hideSubscriptionMessage() {
      const subscriptionMessage = document.getElementById('subscription-message');
      if (subscriptionMessage) {
        subscriptionMessage.remove();
      }
    }

    // تفعيل صفحة الإعدادات للمشتركين
    function enableSettingsPage() {
      const container = document.querySelector('.container');
      if (container) {
        container.style.display = 'block';
      }

      console.log('⚙️ Flag Settings enabled for subscriber');
    }

    // تهيئة الصفحة
    document.addEventListener('DOMContentLoaded', () => {
      console.log('⚙️ Flag Settings page loaded');

      // إخفاء المحتوى مؤقتاً حتى يتم التحقق من الاشتراك
      const container = document.querySelector('.container');
      if (container) {
        container.style.display = 'none';
      }
    });
  </script>
</body>
</html>
