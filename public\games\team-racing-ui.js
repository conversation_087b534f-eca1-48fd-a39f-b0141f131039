// Enhanced Team Racing Game - UI Management
class TeamRacingUI {
    constructor(gameInstance) {
        this.game = gameInstance;
        this.init();
    }

    init() {
        this.setupSettingsUI();
        this.setupEventListeners();
        this.setupImageUploadHandlers();
        this.loadSettings();
    }

    setupImageUploadHandlers() {
        // Setup drag and drop for team images
        const gameContainer = document.getElementById('gameContainer');
        if (gameContainer) {
            gameContainer.addEventListener('dragover', (e) => {
                e.preventDefault();
            });

            gameContainer.addEventListener('drop', (e) => {
                e.preventDefault();
                // Handle file drop functionality here if needed
            });
        }
    }
    
    setupEventListeners() {
        // Wait for game instance to be ready
        const checkGameReady = setInterval(() => {
            if (window.gameInstance) {
                this.game = window.gameInstance;
                this.setupSettingsUI();
                clearInterval(checkGameReady);
            }
        }, 100);
    }
    
    setupSettingsUI() {
        this.setupTeamsConfig();
        this.setupGiftsConfig();
        this.setupGameSettings();
        this.setupVisualSettings();
        this.setupAdvancedTeamSettings();
    }

    setupAdvancedTeamSettings() {
        // Default team type
        const defaultTeamTypeSelect = document.getElementById('defaultTeamType');
        if (defaultTeamTypeSelect) {
            defaultTeamTypeSelect.addEventListener('change', (e) => {
                if (this.game) {
                    this.game.gameSettings.defaultTeamType = e.target.value;
                    this.game.saveSettings();
                    this.showNotification(`تم تغيير نوع الفرق الافتراضي إلى: ${this.getTeamTypeLabel(e.target.value)}`, '#3498db');
                }
            });
        }

        // Global team size
        const globalTeamSizeSlider = document.getElementById('globalTeamSize');
        const globalTeamSizeValue = document.getElementById('globalTeamSizeValue');
        if (globalTeamSizeSlider && globalTeamSizeValue) {
            globalTeamSizeSlider.addEventListener('input', (e) => {
                if (this.game) {
                    this.game.gameSettings.globalTeamSize = parseFloat(e.target.value);
                    globalTeamSizeValue.textContent = this.game.gameSettings.globalTeamSize.toFixed(1) + 'x';
                    this.game.saveSettings();
                    // Update team meshes if they exist
                    if (this.game.updateTeamSizes) {
                        this.game.updateTeamSizes();
                    }
                }
            });
        }

        // Enable team animations
        const enableTeamAnimationsCheckbox = document.getElementById('enableTeamAnimations');
        if (enableTeamAnimationsCheckbox) {
            enableTeamAnimationsCheckbox.addEventListener('change', (e) => {
                if (this.game) {
                    this.game.gameSettings.enableTeamAnimations = e.target.checked;
                    this.game.saveSettings();
                    this.showNotification(`تأثيرات الحركة ${e.target.checked ? 'مفعلة' : 'معطلة'}`, '#3498db');
                }
            });
        }
    }

    getTeamTypeLabel(type) {
        const labels = {
            'ball': 'كرة ملونة',
            'image': 'صورة مخصصة',
            'gif': 'صورة متحركة',
            'mixed': 'مختلط'
        };
        return labels[type] || type;
    }

    updateGameSetting(settingName, value) {
        if (this.game && this.game.gameSettings) {
            this.game.gameSettings[settingName] = value;
            this.game.saveSettings();

            // Apply specific updates based on setting
            switch (settingName) {
                case 'globalTeamSize':
                    if (this.game.updateTeamSizes) {
                        this.game.updateTeamSizes();
                    }
                    break;
                case 'backgroundOpacity':
                    this.updateBackgroundOpacity(value);
                    break;
                case 'cameraMode':
                    if (this.game.updateCameraMode) {
                        this.game.updateCameraMode(value);
                    }
                    break;
            }
        }
    }

    updateBackgroundOpacity(opacity) {
        const gameContainer = document.getElementById('gameContainer');
        if (gameContainer) {
            gameContainer.style.background = `linear-gradient(135deg,
                rgba(15, 15, 35, ${opacity/100}) 0%,
                rgba(26, 26, 46, ${opacity/100}) 25%,
                rgba(22, 33, 62, ${opacity/100}) 50%,
                rgba(15, 52, 96, ${opacity/100}) 75%,
                rgba(10, 36, 99, ${opacity/100}) 100%)`;
        }
    }
    
    setupTeamsConfig() {
        const container = document.getElementById('teamsConfig');
        
        const renderTeams = () => {
            if (!this.game) return;
            
            container.innerHTML = this.game.teams.map((team, index) => `
                <div class="setting-item" style="flex-direction: column; align-items: stretch; margin-bottom: 20px; padding: 15px; border: 2px solid ${team.color}; border-radius: 12px; background: rgba(255,255,255,0.05);">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                        <input type="text" value="${team.name}" onchange="updateTeamName(${index}, this.value)" style="flex: 1; margin-right: 10px; padding: 8px; border-radius: 6px;">
                        <input type="color" value="${team.color}" onchange="updateTeamColor(${index}, this.value)" style="width: 50px; height: 40px; border-radius: 6px;">
                        <button class="btn-danger" onclick="removeTeam(${index})" style="margin-right: 10px; padding: 8px 12px;">🗑️</button>
                    </div>
                    
                    <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 12px;">
                        <span style="font-size: 12px; min-width: 80px;">هدية الانضمام:</span>
                        <input type="text" value="${team.joinGift}" onchange="updateTeamJoinGift(${index}, this.value)" style="width: 70px; text-align: center; padding: 6px;">
                        <span style="font-size: 11px; color: #bbb;">الداعمون: ${team.supporters.length}</span>
                    </div>

                    <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 12px;">
                        <span style="font-size: 12px; min-width: 80px;">نوع العرض:</span>
                        <select onchange="updateTeamDisplayType(${index}, this.value)" style="padding: 6px; border-radius: 4px;">
                            <option value="ball" ${(team.displayType || 'ball') === 'ball' ? 'selected' : ''}>⚽ كرة</option>
                            <option value="cube" ${team.displayType === 'cube' ? 'selected' : ''}>🔲 مكعب</option>
                            <option value="cylinder" ${team.displayType === 'cylinder' ? 'selected' : ''}>🔘 أسطوانة</option>
                            <option value="image" ${team.displayType === 'image' ? 'selected' : ''}>🖼️ صورة</option>
                            <option value="gif" ${team.displayType === 'gif' ? 'selected' : ''}>🎬 صورة متحركة</option>
                        </select>
                    </div>

                    <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 12px;">
                        <span style="font-size: 12px; min-width: 80px;">حجم الفريق:</span>
                        <input type="range" min="0.5" max="2.0" step="0.1" value="${team.size || 1.0}" onchange="updateTeamSize(${index}, this.value)" style="width: 100px;">
                        <span style="font-size: 11px; color: #bbb;">${((team.size || 1.0) * 100).toFixed(0)}%</span>
                    </div>

                    <div style="display: flex; align-items: center; gap: 15px; margin-bottom: 12px;">
                        <span style="font-size: 12px; min-width: 80px;">ارتفاع عن الأرض:</span>
                        <input type="range" min="0.1" max="2.0" step="0.1" value="${team.height || 0.3}" onchange="updateTeamHeight(${index}, this.value)" style="width: 100px;">
                        <span style="font-size: 11px; color: #bbb;">${((team.height || 0.3) * 100).toFixed(0)}cm</span>
                    </div>

                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 12px;">
                        <span style="font-size: 12px; min-width: 80px;">صورة/فيديو:</span>
                        <button onclick="uploadTeamImage(${index})" class="btn-primary" style="padding: 6px 12px; font-size: 11px;">
                            📁 رفع صورة/WebM
                        </button>
                        ${team.customImage ? `
                            <button onclick="removeTeamImage(${index})" class="btn-danger" style="padding: 4px 8px; font-size: 10px;">
                                🗑️ حذف
                            </button>
                            <span style="color: #27ae60; font-size: 11px;">✅ ${team.displayType === 'webm' ? 'فيديو' : 'صورة'}</span>
                        ` : '<span style="color: #95a5a6; font-size: 11px;">لا توجد ملف</span>'}
                    </div>
                    
                    ${team.customImage ? `
                        <div style="display: flex; align-items: center; gap: 10px;">
                            <button onclick="removeTeamImage(${index})" class="btn-warning" style="padding: 4px 8px; font-size: 10px;">
                                🗑️ حذف الصورة
                            </button>
                        </div>
                    ` : ''}
                </div>
            `).join('');
        };
        
        renderTeams();
        
        // Global functions for team management
        window.updateTeamName = (index, name) => {
            if (this.game) {
                this.game.teams[index].name = name;
                this.game.updateStatsPanel();
                this.game.saveSettings();
            }
        };
        
        window.updateTeamColor = (index, color) => {
            if (this.game) {
                this.game.teams[index].color = color;
                this.game.updateStatsPanel();
                this.game.saveSettings();
                renderTeams(); // Re-render to update border colors
            }
        };
        
        window.updateTeamJoinGift = (index, gift) => {
            if (this.game) {
                this.game.teams[index].joinGift = gift;
                this.game.saveSettings();
            }
        };

        window.updateTeamDisplayType = (index, type) => {
            if (this.game) {
                this.game.teams[index].displayType = type;
                this.game.saveSettings();
                this.game.createTeamMeshes(); // Recreate meshes with new type
                renderTeams(); // Update UI
                this.showNotification(`تم تغيير نوع عرض ${this.game.teams[index].name}`, '#3498db');
            }
        };

        window.updateTeamSize = (index, size) => {
            if (this.game) {
                this.game.teams[index].size = parseFloat(size);
                this.game.saveSettings();
                this.game.updateTeamSizes(); // Update sizes in 3D scene
                renderTeams(); // Update UI
            }
        };

        window.updateTeamHeight = (index, height) => {
            if (this.game) {
                this.game.teams[index].height = parseFloat(height);
                this.game.saveSettings();

                // Update height immediately in 3D scene
                if (this.game.teamMeshes && this.game.teamMeshes[index]) {
                    this.game.teamMeshes[index].position.y = parseFloat(height);
                }

                renderTeams(); // Update UI
                this.showNotification(`تم تغيير ارتفاع ${this.game.teams[index].name}`, '#3498db');
            }
        };
        

        
        window.uploadTeamImage = (index) => {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*,video/webm';
            input.onchange = (e) => {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = (event) => {
                        if (this.game) {
                            // Store the data URL and file info
                            this.game.teams[index].customImage = {
                                src: event.target.result,
                                type: file.type,
                                name: file.name
                            };

                            // Set display type based on file type
                            if (file.type === 'video/webm') {
                                this.game.teams[index].displayType = 'webm';
                                console.log('✅ WebM file detected for team:', this.game.teams[index].name);
                            } else {
                                this.game.teams[index].displayType = 'image';
                                console.log('✅ Image file detected for team:', this.game.teams[index].name);
                            }

                            // Update both WebGL and HTML overlay
                            this.game.updateTeamCustomImages();
                            this.game.saveSettings();
                            renderTeams();

                            const fileType = file.type === 'video/webm' ? 'فيديو متحرك' : 'صورة';
                            this.showNotification(`تم رفع ${fileType} لـ ${this.game.teams[index].name}`, '#27ae60');

                            console.log('📁 File uploaded:', {
                                team: this.game.teams[index].name,
                                type: file.type,
                                displayType: this.game.teams[index].displayType,
                                size: file.size
                            });
                        }
                    };
                    reader.readAsDataURL(file);
                }
            };
            input.click();
        };

        // دالة حذف صورة/فيديو الفريق
        window.removeTeamImage = (index) => {
            if (this.game && this.game.teams[index]) {
                const team = this.game.teams[index];
                const fileType = team.displayType === 'webm' ? 'الفيديو' : 'الصورة';

                // حذف الصورة/الفيديو
                delete team.customImage;
                delete team.displayType;

                // تحديث العرض
                this.game.updateTeamCustomImages();
                this.game.saveSettings();
                renderTeams();

                this.showNotification(`تم حذف ${fileType} من ${team.name}`, '#e74c3c');
                console.log(`🗑️ ${fileType} removed from team:`, team.name);
            }
        };
        
        // دالة حذف جميع الفرق
        window.clearAllTeams = () => {
            if (this.game && confirm('هل أنت متأكد من حذف جميع الفرق؟ سيتم حذف كل شيء نهائياً.')) {
                // حذف جميع الفرق تماماً
                this.game.teams = [];

                // تحديث العرض
                this.game.createTeamMeshes();
                this.game.updateHTMLOverlay();
                this.game.saveSettings();
                renderTeams();

                this.showNotification('تم حذف جميع الفرق نهائياً', '#e74c3c');
                console.log('🗑️ All teams cleared completely');
            }
        };
        
        window.removeTeam = (index) => {
            if (this.game && this.game.teams.length > 2) {
                const teamName = this.game.teams[index].name;
                this.game.teams.splice(index, 1);
                renderTeams();
                this.game.updateStatsPanel();
                this.game.saveSettings();
                this.showNotification(`تم حذف ${teamName}`, '#e74c3c');
            }
        };
        
        window.addNewTeam = () => {
            if (!this.game) return;
            
            const colors = ['#e74c3c', '#3498db', '#27ae60', '#f39c12', '#9b59b6', '#e67e22', '#1abc9c', '#34495e'];
            const gifts = ['🌟', '💫', '⚡', '🔥', '💎', '👑', '🎯', '🏆'];
            
            const newTeam = {
                name: `فريق ${this.game.teams.length + 1}`,
                color: colors[this.game.teams.length % colors.length],
                joinGift: gifts[this.game.teams.length % gifts.length],
                progress: 0,
                supporters: [],
                customImage: null
            };
            
            this.game.teams.push(newTeam);
            renderTeams();
            this.game.updateStatsPanel();
            this.game.saveSettings();
            this.showNotification(`تم إضافة ${newTeam.name}`, '#27ae60');
        };
    }
    
    setupGiftsConfig() {
        const joinContainer = document.getElementById('joinGiftsConfig');
        const progressContainer = document.getElementById('progressGiftsConfig');
        
        // Join gifts info
        joinContainer.innerHTML = `
            <div style="padding: 15px; background: rgba(52, 152, 219, 0.1); border-radius: 10px; font-size: 13px; border: 1px solid rgba(52, 152, 219, 0.3);">
                <strong>💡 ملاحظة:</strong> هدايا الانضمام يتم تعيينها في تبويب الفرق أعلاه
            </div>
        `;
        
        // Progress gifts config
        const renderProgressGifts = () => {
            if (!this.game) return;
            
            progressContainer.innerHTML = Object.entries(this.game.progressGifts).map(([gift, value]) => `
                <div class="setting-item" style="margin-bottom: 12px; padding: 10px; background: rgba(255,255,255,0.05); border-radius: 8px;">
                    <span style="font-size: 20px; margin-right: 15px;">${gift}</span>
                    <div class="setting-control" style="flex: 1; display: flex; align-items: center; gap: 10px;">
                        <input type="number" value="${value}" min="1" max="100" onchange="updateProgressGift('${gift}', this.value)" style="width: 70px; padding: 6px;">
                        <span style="font-size: 12px; color: #bbb;">وحدة تقدم</span>
                        <button class="btn-danger" onclick="removeProgressGift('${gift}')" style="padding: 6px 10px; font-size: 11px;">🗑️</button>
                    </div>
                </div>
            `).join('') + `
                <div class="setting-item" style="margin-top: 20px; padding: 15px; background: rgba(39, 174, 96, 0.1); border-radius: 10px; border: 1px solid rgba(39, 174, 96, 0.3);">
                    <div style="display: flex; align-items: center; gap: 10px; margin-bottom: 10px;">
                        <span style="font-size: 13px; font-weight: bold;">➕ إضافة هدية جديدة:</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <input type="text" id="newGiftEmoji" placeholder="🎁" style="width: 60px; text-align: center; padding: 8px;">
                        <input type="number" id="newGiftValue" placeholder="قيمة التقدم" min="1" max="100" style="width: 100px; padding: 8px;">
                        <button class="btn-success" onclick="addProgressGift()" style="padding: 8px 15px; font-size: 12px;">➕ إضافة</button>
                    </div>
                </div>
            `;
        };
        
        renderProgressGifts();
        
        window.updateProgressGift = (gift, value) => {
            if (this.game) {
                this.game.progressGifts[gift] = parseInt(value) || 1;
                this.game.saveSettings();
            }
        };
        
        window.removeProgressGift = (gift) => {
            if (this.game) {
                delete this.game.progressGifts[gift];
                renderProgressGifts();
                this.game.saveSettings();
                this.showNotification(`تم حذف هدية ${gift}`, '#e74c3c');
            }
        };
        
        window.addProgressGift = () => {
            const emoji = document.getElementById('newGiftEmoji').value.trim();
            const value = parseInt(document.getElementById('newGiftValue').value) || 5;
            
            if (emoji && this.game && !this.game.progressGifts[emoji]) {
                this.game.progressGifts[emoji] = value;
                renderProgressGifts();
                this.game.saveSettings();
                this.showNotification(`تم إضافة هدية ${emoji} بقيمة ${value}`, '#27ae60');
                
                // Clear inputs
                document.getElementById('newGiftEmoji').value = '';
                document.getElementById('newGiftValue').value = '';
            } else if (this.game && this.game.progressGifts[emoji]) {
                this.showNotification(`هدية ${emoji} موجودة بالفعل!`, '#f39c12');
            }
        };
    }
    
    setupGameSettings() {
        // Race distance
        const raceDistanceSlider = document.getElementById('raceDistance');
        const raceDistanceValue = document.getElementById('raceDistanceValue');
        
        raceDistanceSlider.addEventListener('input', (e) => {
            if (this.game) {
                this.game.gameSettings.raceDistance = parseInt(e.target.value);
                raceDistanceValue.textContent = this.game.gameSettings.raceDistance;
                this.game.saveSettings();
            }
        });
        
        // Animation speed
        const animationSpeedSlider = document.getElementById('animationSpeed');
        const animationSpeedValue = document.getElementById('animationSpeedValue');
        
        animationSpeedSlider.addEventListener('input', (e) => {
            if (this.game) {
                this.game.gameSettings.animationSpeed = parseInt(e.target.value);
                animationSpeedValue.textContent = this.game.gameSettings.animationSpeed;
                this.game.saveSettings();
            }
        });
        
        // Auto reset
        const autoResetCheckbox = document.getElementById('autoReset');
        autoResetCheckbox.addEventListener('change', (e) => {
            if (this.game) {
                this.game.gameSettings.autoReset = e.target.checked;
                this.game.saveSettings();
            }
        });
    }
    
    setupVisualSettings() {
        // Show effects
        const showEffectsCheckbox = document.getElementById('showEffects');
        showEffectsCheckbox.addEventListener('change', (e) => {
            if (this.game) {
                this.game.gameSettings.showEffects = e.target.checked;
                this.game.saveSettings();
            }
        });

        // Show supporter names
        const showSupporterNamesCheckbox = document.getElementById('showSupporterNames');
        showSupporterNamesCheckbox.addEventListener('change', (e) => {
            if (this.game) {
                this.game.gameSettings.showSupporterNames = e.target.checked;
                this.game.saveSettings();
            }
        });

        // Font size
        const fontSizeSlider = document.getElementById('fontSize');
        const fontSizeValue = document.getElementById('fontSizeValue');

        fontSizeSlider.addEventListener('input', (e) => {
            if (this.game) {
                this.game.gameSettings.fontSize = parseInt(e.target.value);
                fontSizeValue.textContent = this.game.gameSettings.fontSize;
                this.game.saveSettings();
            }
        });

        // Team size
        const teamSizeSlider = document.getElementById('teamSize');
        const teamSizeValue = document.getElementById('teamSizeValue');

        teamSizeSlider.addEventListener('input', (e) => {
            if (this.game) {
                this.game.gameSettings.teamSize = parseInt(e.target.value);
                teamSizeValue.textContent = this.game.gameSettings.teamSize;
                this.game.saveSettings();
            }
        });

        // Performance settings
        this.setupPerformanceSettings();

        // Update UI with current settings
        this.updateUIValues();
    }

    updateUIValues() {
        if (this.game) {
            // Update visual settings values
            const fontSizeSlider = document.getElementById('fontSize');
            const fontSizeValue = document.getElementById('fontSizeValue');
            if (fontSizeSlider && fontSizeValue) {
                fontSizeSlider.value = this.game.gameSettings.fontSize || 16;
                fontSizeValue.textContent = this.game.gameSettings.fontSize || 16;
            }

            const teamSizeSlider = document.getElementById('teamSize');
            const teamSizeValue = document.getElementById('teamSizeValue');
            if (teamSizeSlider && teamSizeValue) {
                teamSizeSlider.value = this.game.gameSettings.teamSize || 60;
                teamSizeValue.textContent = this.game.gameSettings.teamSize || 60;
            }

            // Update checkboxes
            const showEffectsCheckbox = document.getElementById('showEffects');
            if (showEffectsCheckbox) {
                showEffectsCheckbox.checked = this.game.gameSettings.showEffects !== false;
            }

            const showSupporterNamesCheckbox = document.getElementById('showSupporterNames');
            if (showSupporterNamesCheckbox) {
                showSupporterNamesCheckbox.checked = this.game.gameSettings.showSupporterNames !== false;
            }
        }
    }

    setupPerformanceSettings() {
        // Target FPS
        const targetFPSSelect = document.getElementById('targetFPS');
        targetFPSSelect.addEventListener('change', (e) => {
            if (this.game) {
                this.game.targetFPS = parseInt(e.target.value);
                this.game.frameInterval = 1000 / this.game.targetFPS;
                this.game.saveSettings();
                this.showNotification(`تم تغيير معدل الإطارات إلى ${this.game.targetFPS} FPS`, '#3498db');
            }
        });

        // VSync
        const enableVSyncCheckbox = document.getElementById('enableVSync');
        enableVSyncCheckbox.addEventListener('change', (e) => {
            if (this.game) {
                this.game.enableVSync = e.target.checked;
                this.game.saveSettings();
                this.showNotification(`VSync ${e.target.checked ? 'مفعل' : 'معطل'}`, '#3498db');
            }
        });

        // Show FPS
        const showFPSCheckbox = document.getElementById('showFPS');
        showFPSCheckbox.addEventListener('change', (e) => {
            if (this.game) {
                this.game.gameSettings.showFPS = e.target.checked;
                this.game.saveSettings();
            }
        });
    }
    
    showNotification(message, color = '#3498db') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${color};
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-family: 'Tajawal', sans-serif;
            font-size: 14px;
            font-weight: bold;
            z-index: 1001;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            animation: slideDown 0.3s ease-out;
        `;

        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideDown 0.3s ease-out reverse';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    loadSettings() {
        // Load settings from localStorage or use defaults
        const savedSettings = localStorage.getItem('teamRacingSettings');
        if (savedSettings && this.game) {
            try {
                const settings = JSON.parse(savedSettings);
                Object.assign(this.game.gameSettings, settings.gameSettings || {});
                if (settings.teams) {
                    this.game.teams = settings.teams;
                }
                if (settings.progressGifts) {
                    this.game.progressGifts = settings.progressGifts;
                }
                this.updateUIValues();
            } catch (error) {
                console.error('Error loading settings:', error);
            }
        }
    }
}

// Global UI functions
window.showTab = (tabName, clickedElement) => {
    // Hide all tab contents
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });

    // Remove active class from all tabs
    document.querySelectorAll('.tab').forEach(tab => {
        tab.classList.remove('active');
    });

    // Show selected tab content
    document.getElementById(tabName).classList.add('active');

    // Add active class to clicked tab
    if (clickedElement) {
        clickedElement.classList.add('active');
    }
};

// Enhanced global functions
window.updateDefaultTeamType = (type) => {
    if (window.gameInstance) {
        window.gameInstance.gameSettings.defaultTeamType = type;
        window.gameInstance.saveSettings();
        if (window.uiInstance) {
            window.uiInstance.showNotification(`تم تغيير نوع الفرق الافتراضي`, '#3498db');
        }
    }
};

window.resetAllTeams = () => {
    if (window.gameInstance && confirm('هل أنت متأكد من إعادة تعيين جميع الفرق؟')) {
        window.gameInstance.teams.forEach(team => {
            team.progress = 0;
            team.supporters = [];
        });
        window.gameInstance.supporters.clear();
        window.gameInstance.resetRace();
        if (window.uiInstance) {
            window.uiInstance.showNotification('تم إعادة تعيين جميع الفرق', '#27ae60');
        }
    }
};

window.exportSettings = () => {
    if (window.gameInstance) {
        const settings = {
            gameSettings: window.gameInstance.gameSettings,
            teams: window.gameInstance.teams.map(team => ({
                ...team,
                customImage: null // Don't export images
            })),
            progressGifts: window.gameInstance.progressGifts,
            exportDate: new Date().toISOString(),
            version: '2.0'
        };

        const dataStr = JSON.stringify(settings, null, 2);
        const dataBlob = new Blob([dataStr], {type: 'application/json'});

        const link = document.createElement('a');
        link.href = URL.createObjectURL(dataBlob);
        link.download = `team-racing-settings-${new Date().toISOString().split('T')[0]}.json`;
        link.click();

        if (window.uiInstance) {
            window.uiInstance.showNotification('تم تصدير الإعدادات بنجاح', '#27ae60');
        }
    }
};

window.importSettings = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';
    input.onchange = (e) => {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (event) => {
                try {
                    const settings = JSON.parse(event.target.result);

                    if (window.gameInstance) {
                        // Import settings
                        if (settings.gameSettings) {
                            Object.assign(window.gameInstance.gameSettings, settings.gameSettings);
                        }
                        if (settings.teams) {
                            window.gameInstance.teams = settings.teams;
                        }
                        if (settings.progressGifts) {
                            window.gameInstance.progressGifts = settings.progressGifts;
                        }

                        // Save and update UI
                        window.gameInstance.saveSettings();
                        if (window.uiInstance) {
                            window.uiInstance.setupSettingsUI();
                            window.uiInstance.updateUIValues();
                            window.uiInstance.showNotification('تم استيراد الإعدادات بنجاح', '#27ae60');
                        }

                        // Restart game with new settings
                        window.gameInstance.resetRace();
                    }
                } catch (error) {
                    console.error('Error importing settings:', error);
                    if (window.uiInstance) {
                        window.uiInstance.showNotification('خطأ في استيراد الإعدادات', '#e74c3c');
                    }
                }
            };
            reader.readAsText(file);
        }
    };
    input.click();
};

// New advanced functions
window.resetAllSettings = () => {
    if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟ سيتم فقدان جميع التخصيصات!')) {
        localStorage.removeItem('teamRacingSettings');
        location.reload();
    }
};

window.showSavedImages = () => {
    if (window.uiInstance) {
        window.uiInstance.showNotification('ميزة عرض الصور المحفوظة قيد التطوير', '#3498db');
    }
};

window.clearAllImages = () => {
    if (confirm('هل أنت متأكد من مسح جميع الصور؟')) {
        if (window.gameInstance) {
            window.gameInstance.teams.forEach(team => {
                team.customImage = null;
            });
            window.gameInstance.updateTeamCustomImages();
            window.gameInstance.saveSettings();
        }
        if (window.uiInstance) {
            window.uiInstance.showNotification('تم مسح جميع الصور', '#e74c3c');
        }
    }
};

window.showPerformanceInfo = () => {
    if (window.gameInstance && window.getPerformanceInfo) {
        const info = window.getPerformanceInfo();
        const infoText = `
FPS الحالي: ${info.currentFPS}
FPS المستهدف: ${info.targetFPS}
عدد الإطارات: ${info.frameCount}
المحرك: ${info.renderer}
        `;
        alert(infoText);
    }
};

window.showErrorLog = () => {
    if (window.uiInstance) {
        window.uiInstance.showNotification('سجل الأخطاء فارغ - لا توجد أخطاء', '#27ae60');
    }
};

// Initialize UI when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
    window.uiInstance = new TeamRacingUI();
});
