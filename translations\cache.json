{"ar:en:الاتصال بـ TikTok Live": "Connect to TikTok Live", "ar:en:ربط الهدايا بالإجراءات": "Link Gifts to Actions", "ar:en:قراءة التعليقات الصوتية": "Read Voiceover", "ar:en:الإعدادات": "الإعدادات", "ar:en:الملفات الشخصية": "Profiles", "ar:en:إعدادات اللغة": "Language Settings", "ar:en:اتصل بنا": "اتصل بنا", "ar:en:فتح Overlay": "Open Overlay", "ar:en:اسم المستخدم على TikTok:": "TikTok username:", "ar:en:أدخل اسم المستخدم (بدون @)": "Enter username (without @)", "ar:en:اتصال": "Rings", "ar:en:قطع الاتصال": "disconnect", "ar:en:غير متصل. الرجاء إدخال اسم مستخدم.": "Please enter a username", "ar:en:أحداث TikTok الحية": "TikTok Live Events", "ar:en:سيتم عرض آخر الأحداث من البث المباشر هنا:": "Latest events from the live stream will be displayed here:", "ar:en:حفظ": "Save", "ar:en:إلغاء": "Rechargeable", "ar:en:إضافة": "Profiles", "ar:en:تعديل": "Modification", "ar:en:حذف": "Delete", "ar:en:اختبار": "Test", "ar:en:تم الحفظ بنجاح": "Saved successfully", "ar:en:حدث خطأ": "Something wrong", "ar:en:هل أنت متأكد؟": "Are you sure?", "ar:en:????? ???????": "The United States", "ar:fr:الاتصال بـ TikTok Live": "Connectez-vous <PERSON>", "ar:fr:ربط الهدايا بالإجراءات": "Lier les cadeaux aux actions", "ar:fr:قراءة التعليقات الصوتية": "Lire la voix off", "ar:fr:الإعدادات": "Paramètres", "ar:fr:الملفات الشخصية": "Fichiers personnels ?", "ar:fr:إعدادات اللغة": "Configuration de la langue", "ar:fr:اتصل بنا": "Contactez-nous", "ar:fr:فتح Overlay": "Superposition ouverte", "ar:fr:اسم المستخدم على TikTok:": "Nom d'utilisateur TikTok :", "ar:fr:أدخل اسم المستخدم (بدون @)": "Saisissez le nom d'utilisateur (sans @)", "ar:fr:اتصال": "Connection", "ar:fr:قطع الاتصال": "Je coupe le cordon ombilical.", "ar:fr:غير متصل. الرجاء إدخال اسم مستخدم.": "Hors ligne. Veuillez saisir un nom d'utilisateur.", "ar:fr:أحداث TikTok الحية": "Événements en direct TikTok", "ar:fr:سيتم عرض آخر الأحداث من البث المباشر هنا:": "Les derniers événements du flux en direct seront affichés ici :", "ar:fr:حفظ": "Enregistrer", "ar:fr:إلغاء": "Annuler", "ar:fr:إضافة": "Ajouter", "ar:fr:تعديل": "Modifier", "ar:fr:حذف": " <PERSON><PERSON><PERSON><PERSON>", "ar:fr:اختبار": "<PERSON><PERSON><PERSON>.", "ar:fr:تم الحفظ بنجاح": "Enregistré avec succès", "ar:fr:حدث خطأ": "C'est une erreur.", "ar:fr:هل أنت متأكد؟": "Êtes-vous sûr?", "ar:de:الاتصال بـ TikTok Live": "Mit TikTok Live verbinden", "ar:de:ربط الهدايا بالإجراءات": "Geschenke mit Aktionen verknüpfen", "ar:de:قراءة التعليقات الصوتية": "Voiceover lesen", "ar:de:الإعدادات": "& Einstellungen", "ar:de:الملفات الشخصية": "Profile", "ar:de:إعدادات اللغة": "Spracheinstellungen", "ar:de:اتصل بنا": "Kontakten Sie uns", "ar:de:فتح Overlay": "<PERSON><PERSON>", "ar:de:اسم المستخدم على TikTok:": "TikTok-Benutzername:", "ar:de:أدخل اسم المستخدم (بدون @)": "Benutzernamen eingeben (ohne @)", "ar:de:اتصال": "Verbinden", "ar:de:قطع الاتصال": "Colorado, out.", "ar:de:غير متصل. الرجاء إدخال اسم مستخدم.": "Offline. Bitte geben Si<PERSON> einen Benutzernamen ein.", "ar:de:أحداث TikTok الحية": "TikTok-Live-Events", "ar:de:سيتم عرض آخر الأحداث من البث المباشر هنا:": "Die neuesten Ereignisse aus dem Live-Stream werden hier angezeigt:", "ar:de:حفظ": "SPEICHERN", "ar:de:إلغاء": "Abbruch...", "ar:de:إضافة": "Addendum", "ar:de:تعديل": "Aber ganz sicher. subbed by realexkav", "ar:de:حذف": "LÖSCHEN", "ar:de:اختبار": "- Test. Check.", "ar:de:تم الحفظ بنجاح": "Erfolgreich gespeichert", "ar:de:حدث خطأ": "Ein Fehler ist aufgetreten.", "ar:de:هل أنت متأكد؟": "Bist du da so sicher, <PERSON><PERSON>?", "ar:zh:الاتصال بـ TikTok Live": "连接到TikTok Live", "ar:zh:ربط الهدايا بالإجراءات": "将礼品链接到操作", "ar:zh:قراءة التعليقات الصوتية": "阅读配音", "ar:zh:الإعدادات": "标签设置", "ar:zh:الملفات الشخصية": "个人资料", "ar:zh:إعدادات اللغة": "语言设置", "ar:zh:اتصل بنا": "联系我们", "ar:zh:فتح Overlay": "打开叠加层", "ar:zh:اسم المستخدم على TikTok:": "TikTok用户名：", "ar:zh:أدخل اسم المستخدم (بدون @)": "输入用户名（不含@ ）", "ar:zh:اتصال": "\"连接\"", "ar:zh:قطع الاتصال": "断开", "ar:zh:غير متصل. الرجاء إدخال اسم مستخدم.": "离线。请输入用户名。", "ar:zh:أحداث TikTok الحية": "TikTok直播活动", "ar:zh:سيتم عرض آخر الأحداث من البث المباشر هنا:": "直播中的最新活动将在此处显示：", "ar:zh:حفظ": "已保存", "ar:zh:إلغاء": "ä¸­æ­¢", "ar:zh:إضافة": "加", "ar:zh:تعديل": "编辑", "ar:zh:حذف": "删除", "ar:zh:اختبار": "测试( E)", "ar:zh:تم الحفظ بنجاح": "保存成功", "ar:zh:حدث خطأ": "出错了", "ar:zh:هل أنت متأكد؟": "您确定吗 ？", "ar:it:الاتصال بـ TikTok Live": "Connettiti a TikTok Live", "ar:it:ربط الهدايا بالإجراءات": "Collega i regali alle azioni", "ar:it:قراءة التعليقات الصوتية": "<PERSON><PERSON><PERSON>", "ar:it:الإعدادات": "& Impostazioni", "ar:it:الملفات الشخصية": "Profili", "ar:it:إعدادات اللغة": "Impostazioni lingua", "ar:it:اتصل بنا": "Con<PERSON><PERSON><PERSON>", "ar:it:فتح Overlay": "<PERSON><PERSON> overlay", "ar:it:اسم المستخدم على TikTok:": "Nome utente TikTok:", "ar:it:أدخل اسم المستخدم (بدون @)": "Inserisci nome utente (senza @)", "ar:it:اتصال": "Connessione.", "ar:it:قطع الاتصال": "- Ricevuto. Passo e chiudo.", "ar:it:غير متصل. الرجاء إدخال اسم مستخدم.": "Offline. Inserisci un nome utente.", "ar:it:أحداث TikTok الحية": "Eventi live TikTok", "ar:it:سيتم عرض آخر الأحداث من البث المباشر هنا:": "Gli ultimi eventi della diretta streaming verranno visualizzati qui:", "ar:it:حفظ": "<PERSON><PERSON>", "ar:it:إلغاء": "<PERSON><PERSON><PERSON>", "ar:it:إضافة": "Rettifico.", "ar:it:تعديل": "- Stagione 1 Episodio 04 - \"Flower\" Traduzione: <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_, <PERSON><PERSON><PERSON><PERSON><PERSON>, il@ri@, <PERSON><PERSON>, newyorker90", "ar:it:حذف": "Cancella!", "ar:it:اختبار": "Oh, so io come. Un quiz.", "ar:it:تم الحفظ بنجاح": "Salvato con successo", "ar:it:حدث خطأ": "Si è verificato un errore", "ar:it:هل أنت متأكد؟": "- Ne e' certo?", "ar:ru:الاتصال بـ TikTok Live": "Подключиться к TikTok Live", "ar:ru:ربط الهدايا بالإجراءات": "Привязать подарки к действиям", "ar:ru:قراءة التعليقات الصوتية": "Чтение закадрового голоса", "ar:ru:الإعدادات": "spasibo", "ar:ru:الملفات الشخصية": "Профили", "ar:ru:إعدادات اللغة": "Настройки языка", "ar:ru:اتصل بنا": "связаться с нами", "ar:ru:فتح Overlay": "Открыть оверлей", "ar:ru:اسم المستخدم على TikTok:": "Имя пользователя TikTok:", "ar:ru:أدخل اسم المستخدم (بدون @)": "Введите имя пользователя (без @)", "ar:ru:اتصال": "Подключение тарифного плана", "ar:ru:قطع الاتصال": "Отключить", "ar:ru:غير متصل. الرجاء إدخال اسم مستخدم.": "Не в сети. Введите имя пользователя.", "ar:ru:أحداث TikTok الحية": "Прямые трансляции TikTok", "ar:ru:سيتم عرض آخر الأحداث من البث المباشر هنا:": "Здесь будут отображаться последние события из прямой трансляции:", "ar:ru:حفظ": "Сохранить", "ar:ru:إلغاء": "Отменить", "ar:ru:إضافة": "Добавить", "ar:ru:تعديل": "Изменение", "ar:ru:حذف": "Удалить", "ar:ru:اختبار": "тест", "ar:ru:تم الحفظ بنجاح": "Успешно сохранено", "ar:ru:حدث خطأ": "Что-то пошло не так", "ar:ru:هل أنت متأكد؟": "Вы уверены?", "ar:tr:الاتصال بـ TikTok Live": "TikTok Live'a bağlanın", "ar:tr:ربط الهدايا بالإجراءات": "Hediyeleri Eylemlere Bağla", "ar:tr:قراءة التعليقات الصوتية": "<PERSON><PERSON>ndi<PERSON><PERSON><PERSON>", "ar:tr:الإعدادات": "& Ayarlar", "ar:tr:الملفات الشخصية": "Profiller", "ar:tr:إعدادات اللغة": "<PERSON><PERSON>ı", "ar:tr:اتصل بنا": "Bize Ulaşın", "ar:tr:فتح Overlay": "<PERSON><PERSON>ımını Aç", "ar:tr:اسم المستخدم على TikTok:": "TikTok kullanıcı adı:", "ar:tr:أدخل اسم المستخدم (بدون @)": "Kullanıcı adını girin (@ olmadan)", "ar:tr:اتصال": "İletişim", "ar:tr:قطع الاتصال": "Bağlantıyı kes", "ar:tr:غير متصل. الرجاء إدخال اسم مستخدم.": "Çevrimdışı. Lütfen bir kullanıcı adı girin.", "ar:tr:أحداث TikTok الحية": "TikTok Canlı Etkinlikleri", "ar:tr:سيتم عرض آخر الأحداث من البث المباشر هنا:": "Canlı akıştaki en son etkinlikler burada görüntülenecektir:", "ar:tr:حفظ": "KAYDET", "ar:tr:إلغاء": "&amp; İptal", "ar:tr:إضافة": "Adds", "ar:tr:تعديل": "Düzenleme (Değişiklik)", "ar:tr:حذف": "& Silinme", "ar:tr:اختبار": "Test", "ar:tr:تم الحفظ بنجاح": "Başarıyla <PERSON>ildi", "ar:tr:حدث خطأ": "Bilinmeyen bir hata o<PERSON>.", "ar:tr:هل أنت متأكد؟": "Emin misiniz?", "ar:id:الاتصال بـ TikTok Live": "Hubungkan ke TikTok Live", "ar:id:ربط الهدايا بالإجراءات": "<PERSON><PERSON><PERSON> ke <PERSON>", "ar:id:قراءة التعليقات الصوتية": "Baca Voiceover", "ar:id:الإعدادات": "<PERSON><PERSON><PERSON><PERSON>", "ar:id:الملفات الشخصية": "Profil", "ar:id:إعدادات اللغة": "Pengaturan Bahasa", "ar:id:اتصل بنا": "<PERSON><PERSON><PERSON><PERSON> kami.", "ar:id:فتح Overlay": "<PERSON><PERSON>", "ar:id:اسم المستخدم على TikTok:": "<PERSON><PERSON> pengguna TikTok:", "ar:id:أدخل اسم المستخدم (بدون @)": "<PERSON><PERSON><PERSON><PERSON> nama pengguna (tanpa @)", "ar:id:اتصال": "Aku memanggilnya.", "ar:id:قطع الاتصال": "( SAMBUNGAN TERPUTUS )", "ar:id:غير متصل. الرجاء إدخال اسم مستخدم.": "Offline. <PERSON><PERSON><PERSON> masukkan nama pengguna.", "ar:id:أحداث TikTok الحية": "<PERSON><PERSON><PERSON>ik<PERSON>", "ar:id:سيتم عرض آخر الأحداث من البث المباشر هنا:": "<PERSON>cara terbaru dari live streaming akan ditampilkan di sini:", "ar:id:حفظ": "<PERSON><PERSON>", "ar:id:إلغاء": "Batalkan", "ar:id:إضافة": "Lagipula..", "ar:id:تعديل": "Penerjemah:", "ar:id:حذف": "Ha<PERSON>.", "ar:id:اختبار": "<PERSON><PERSON><PERSON>.", "ar:id:تم الحفظ بنجاح": "<PERSON><PERSON><PERSON><PERSON>", "ar:id:حدث خطأ": "<PERSON><PERSON><PERSON> kes<PERSON>han telah terjadi", "ar:id:هل أنت متأكد؟": "- Apa kamu yakin? - Ya.", "ar:en:tts.filter.title": "tts.filter.title", "ar:en:tts.filter.description": "tts.filter.description", "ar:en:tts.title": "tts.title", "ar:en:tts.filter.minLength": "tts.filter.minLength", "ar:en:tts.filter.maxLength": "tts.filter.maxLength", "ar:en:tts.filter.readRate": "tts.filter.readRate", "ar:en:tts.enable": "tts.enable", "ar:en:tts.filter.readRateHint": "tts.filter.readRateHint", "ar:en:tts.status.disabled": "tts.status.disabled", "ar:en:tts.filter.blockedWords": "tts.filter.blockedWords", "ar:en:tts.voice.title": "tts.voice.title", "ar:en:tts.voice.search": "tts.voice.search", "ar:en:tts.voice.description": "tts.voice.description", "ar:en:tts.voice.test.placeholder": "tts.voice.test.placeholder", "ar:en:tts.voice.select": "tts.voice.select", "ar:en:tts.filter.blockedWordsPlaceholder": "tts.filter.blockedWordsPlaceholder", "ar:en:tts.voice.speed": "tts.voice.speed", "ar:en:tts.voice.volume": "tts.voice.volume", "ar:en:tts.voice.test.label": "tts.voice.test.label", "ar:en:tts.voice.test.button": "tts.voice.test.button", "ar:fr:tts.filter.title": "tts.filter.title", "ar:fr:tts.title": "tts.title", "ar:fr:tts.filter.description": "tts.filter.description", "ar:fr:tts.filter.minLength": "tts.filter.minLength", "ar:fr:tts.enable": "tts.enable", "ar:fr:tts.filter.maxLength": "tts.filter.maxLength", "ar:fr:tts.status.disabled": "tts.status.disabled", "ar:fr:tts.filter.readRate": "tts.filter.readRate", "ar:fr:tts.voice.title": "tts.voice.title", "ar:fr:tts.filter.readRateHint": "tts.filter.readRateHint", "ar:fr:tts.voice.description": "tts.voice.description", "ar:fr:tts.voice.select": "tts.voice.select", "ar:fr:tts.filter.blockedWords": "tts.filter.blockedWords", "ar:fr:tts.voice.speed": "tts.voice.speed", "ar:fr:tts.voice.search": "tts.voice.search", "ar:fr:tts.voice.volume": "tts.voice.volume", "ar:fr:tts.voice.test.placeholder": "tts.voice.test.placeholder", "ar:fr:tts.voice.test.label": "tts.voice.test.label", "ar:fr:tts.filter.blockedWordsPlaceholder": "tts.filter.blockedWordsPlaceholder", "ar:fr:tts.voice.test.button": "tts.voice.test.button", "ar:it:tts.filter.title": "tts.filter.title", "ar:it:tts.title": "tts.title", "ar:it:tts.filter.description": "tts.filter.description", "ar:it:tts.enable": "tts.enable", "ar:it:tts.filter.minLength": "tts.filter.minLength", "ar:it:tts.status.disabled": "tts.status.disabled", "ar:it:tts.filter.maxLength": "tts.filter.maxLength", "ar:it:tts.voice.title": "tts.voice.title", "ar:it:tts.filter.readRate": "tts.filter.readRate", "ar:it:tts.voice.description": "tts.voice.description", "ar:it:tts.filter.readRateHint": "tts.filter.readRateHint", "ar:it:tts.voice.select": "tts.voice.select", "ar:it:tts.filter.blockedWords": "tts.filter.blockedWords", "ar:it:tts.voice.speed": "tts.voice.speed", "ar:it:tts.voice.search": "tts.voice.search", "ar:it:tts.voice.volume": "tts.voice.volume", "ar:it:tts.voice.test.placeholder": "tts.voice.test.placeholder", "ar:it:tts.voice.test.label": "tts.voice.test.label", "ar:it:tts.filter.blockedWordsPlaceholder": "tts.filter.blockedWordsPlaceholder", "ar:it:tts.voice.test.button": "tts.voice.test.button", "ar:fr:settings.general.maxItems": "settings.general.maxItems", "ar:fr:settings.appearance.textColor": "settings.appearance.textColor", "ar:fr:settings.appearance.animations": "settings.appearance.animations", "ar:fr:settings.title": "settings.title", "ar:fr:settings.appearance.title": "settings.appearance.title", "ar:fr:settings.appearance.accentColor": "settings.appearance.accentColor", "ar:fr:settings.appearance.animationsTooltip": "settings.appearance.animationsTooltip", "ar:fr:settings.general.title": "settings.general.title", "ar:fr:settings.appearance.fontFamily": "settings.appearance.fontFamily", "ar:fr:settings.appearance.backgroundColor": "settings.appearance.backgroundColor", "ar:fr:settings.general.overlayWidth": "settings.general.overlayWidth", "ar:fr:settings.appearance.fontSize": "settings.appearance.fontSize", "ar:fr:settings.appearance.withOpacity": "settings.appearance.withOpacity", "ar:fr:settings.general.overlayOpacity": "settings.general.overlayOpacity", "ar:fr:settings.appearance.backgroundOpacity": "settings.appearance.backgroundOpacity", "ar:fr:settings.appearance.backgroundOpacityHint": "settings.appearance.backgroundOpacityHint", "ar:en:settings.appearance.textColor": "settings.appearance.textColor", "ar:en:settings.general.maxItems": "settings.general.maxItems", "ar:en:settings.appearance.animations": "settings.appearance.animations", "ar:en:settings.title": "settings.title", "ar:en:settings.appearance.accentColor": "settings.appearance.accentColor", "ar:en:settings.appearance.animationsTooltip": "settings.appearance.animationsTooltip", "ar:en:settings.appearance.title": "settings.appearance.title", "ar:en:settings.general.title": "settings.general.title", "ar:en:settings.appearance.fontFamily": "settings.appearance.fontFamily", "ar:en:settings.general.overlayWidth": "settings.general.overlayWidth", "ar:en:settings.appearance.backgroundColor": "settings.appearance.backgroundColor", "ar:en:settings.appearance.fontSize": "settings.appearance.fontSize", "ar:en:settings.general.overlayOpacity": "settings.general.overlayOpacity", "ar:en:settings.appearance.withOpacity": "settings.appearance.withOpacity", "ar:en:settings.appearance.backgroundOpacity": "settings.appearance.backgroundOpacity", "ar:en:settings.appearance.backgroundOpacityHint": "settings.appearance.backgroundOpacityHint", "ar:en:settings.appearance.animationType": "settings.appearance.animationType", "ar:en:settings.appearance.enableSound": "settings.appearance.enableSound", "ar:en:settings.preview.title": "settings.preview.title", "ar:en:settings.appearance.animationTypes.zoomIn": "settings.appearance.animationTypes.zoomIn", "ar:en:settings.appearance.animationTypes.pulse": "settings.appearance.animationTypes.pulse", "ar:en:settings.appearance.animationTypes.random": "settings.appearance.animationTypes.random", "ar:en:settings.preview.overlayPreview": "settings.preview.overlayPreview", "ar:en:settings.appearance.enableSoundTooltip": "settings.appearance.enableSoundTooltip", "ar:en:settings.appearance.animationTypes.rotateIn": "settings.appearance.animationTypes.rotateIn", "ar:en:settings.appearance.animationTypes.flip": "settings.appearance.animationTypes.flip", "ar:en:settings.preview.sampleText": "settings.preview.sampleText", "ar:en:settings.appearance.animationTypes.bounce": "settings.appearance.animationTypes.bounce", "ar:en:settings.appearance.soundVolume": "settings.appearance.soundVolume", "ar:en:settings.appearance.animationTypes.glow": "settings.appearance.animationTypes.glow", "ar:en:settings.appearance.animationTypes.slideIn": "settings.appearance.animationTypes.slideIn", "ar:en:settings.appearance.animationTypes.shake": "settings.appearance.animationTypes.shake", "ar:en:settings.appearance.animationTypeHint": "settings.appearance.animationTypeHint", "ar:en:settings.appearance.loopAnimation": "settings.appearance.loopAnimation", "ar:en:button.reset": "button.reset", "ar:en:settings.appearance.loopAnimationTooltip": "settings.appearance.loopAnimationTooltip", "ar:en:settings.appearance.loopAnimationHint": "settings.appearance.loopAnimationHint", "ar:fr:settings.appearance.animationType": "settings.appearance.animationType", "ar:fr:settings.appearance.animationTypes.zoomIn": "settings.appearance.animationTypes.zoomIn", "ar:fr:settings.appearance.enableSound": "settings.appearance.enableSound", "ar:fr:settings.preview.title": "settings.preview.title", "ar:fr:settings.appearance.animationTypes.pulse": "settings.appearance.animationTypes.pulse", "ar:fr:settings.appearance.animationTypes.random": "settings.appearance.animationTypes.random", "ar:fr:settings.preview.overlayPreview": "settings.preview.overlayPreview", "ar:fr:settings.appearance.animationTypes.rotateIn": "settings.appearance.animationTypes.rotateIn", "ar:fr:settings.appearance.enableSoundTooltip": "settings.appearance.enableSoundTooltip", "ar:fr:settings.appearance.animationTypes.flip": "settings.appearance.animationTypes.flip", "ar:fr:settings.appearance.animationTypes.bounce": "settings.appearance.animationTypes.bounce", "ar:fr:settings.preview.sampleText": "settings.preview.sampleText", "ar:fr:settings.appearance.soundVolume": "settings.appearance.soundVolume", "ar:fr:settings.appearance.animationTypes.glow": "settings.appearance.animationTypes.glow", "ar:fr:settings.appearance.animationTypes.shake": "settings.appearance.animationTypes.shake", "ar:fr:settings.appearance.animationTypes.slideIn": "settings.appearance.animationTypes.slideIn", "ar:fr:button.reset": "button.reset", "ar:fr:settings.appearance.animationTypeHint": "settings.appearance.animationTypeHint", "ar:fr:settings.appearance.loopAnimation": "settings.appearance.loopAnimation", "ar:fr:settings.appearance.loopAnimationTooltip": "settings.appearance.loopAnimationTooltip", "ar:fr:settings.appearance.loopAnimationHint": "settings.appearance.loopAnimationHint", "ar:en:profiles.section.importExportDescription": "profiles.section.importExportDescription", "ar:en:profiles.title": "profiles.title", "ar:en:profiles.exportSettings": "profiles.exportSettings", "ar:en:profiles.section.profiles": "profiles.section.profiles", "ar:en:profiles.section.profilesDescription": "profiles.section.profilesDescription", "ar:en:profiles.buttons.addProfile": "profiles.buttons.addProfile", "ar:en:profiles.exportSettingsDescription": "profiles.exportSettingsDescription", "ar:en:profiles.section.management": "profiles.section.management", "ar:en:profiles.buttons.exportSettings": "profiles.buttons.exportSettings", "ar:en:profiles.currentProfile": "profiles.currentProfile", "ar:en:profiles.importSettings": "profiles.importSettings", "ar:en:profiles.buttons.saveSettings": "profiles.buttons.saveSettings", "ar:en:profiles.buttons.exportProfile": "profiles.buttons.exportProfile", "ar:en:profiles.importSettingsDescription": "profiles.importSettingsDescription", "ar:en:profiles.buttons.importProfile": "profiles.buttons.importProfile", "ar:en:profiles.buttons.importSettings": "profiles.buttons.importSettings", "ar:en:profiles.section.importExport": "profiles.section.importExport", "ar:en:profiles.modal.addProfile": "profiles.modal.addProfile", "ar:en:profiles.modal.profileName": "profiles.modal.profileName", "ar:en:profiles.modal.profileNamePlaceholder": "profiles.modal.profileNamePlaceholder", "ar:fr:profiles.section.importExportDescription": "profiles.section.importExportDescription", "ar:fr:profiles.title": "profiles.title", "ar:fr:profiles.section.profiles": "profiles.section.profiles", "ar:fr:profiles.exportSettings": "profiles.exportSettings", "ar:fr:profiles.section.profilesDescription": "profiles.section.profilesDescription", "ar:fr:profiles.buttons.addProfile": "profiles.buttons.addProfile", "ar:fr:profiles.exportSettingsDescription": "profiles.exportSettingsDescription", "ar:fr:profiles.buttons.exportSettings": "profiles.buttons.exportSettings", "ar:fr:profiles.section.management": "profiles.section.management", "ar:fr:profiles.importSettings": "profiles.importSettings", "ar:fr:profiles.currentProfile": "profiles.currentProfile", "ar:fr:profiles.buttons.saveSettings": "profiles.buttons.saveSettings", "ar:fr:profiles.importSettingsDescription": "profiles.importSettingsDescription", "ar:fr:profiles.buttons.exportProfile": "profiles.buttons.exportProfile", "ar:fr:profiles.buttons.importSettings": "profiles.buttons.importSettings", "ar:fr:profiles.buttons.importProfile": "profiles.buttons.importProfile", "ar:fr:profiles.modal.addProfile": "profiles.modal.addProfile", "ar:fr:profiles.section.importExport": "profiles.section.importExport", "ar:fr:profiles.modal.profileName": "profiles.modal.profileName", "ar:fr:profiles.modal.profileNamePlaceholder": "profiles.modal.profileNamePlaceholder", "ar:ja:الاتصال بـ TikTok Live": "TikTok Liveに接続", "ar:ja:ربط الهدايا بالإجراءات": "ギフトをアクションにリンク", "ar:ja:قراءة التعليقات الصوتية": "ボイスオーバーを読む", "ar:ja:الإعدادات": "タブの設定", "ar:ja:الملفات الشخصية": "プロフィール", "ar:ja:إعدادات اللغة": "言語の設定", "ar:ja:اتصل بنا": "お問い合わせ", "ar:ja:فتح Overlay": "オーバーレイを開く", "ar:ja:اسم المستخدم على TikTok:": "TikTokユーザー名：", "ar:ja:أدخل اسم المستخدم (بدون @)": "ユーザー名を入力してください（@なし）", "ar:ja:اتصال": "(接続 %1)", "ar:ja:قطع الاتصال": "ヘソの緒を切る", "ar:ja:غير متصل. الرجاء إدخال اسم مستخدم.": "オフラインです。ユーザー名を入力してください。", "ar:ja:أحداث TikTok الحية": "TikTokライブイベント", "ar:ja:سيتم عرض آخر الأحداث من البث المباشر هنا:": "ライブストリームの最新イベントはここに表示されます：", "ar:ja:حفظ": "保存...", "ar:ja:إلغاء": "ã­ã£ã³ã»ã«", "ar:ja:إضافة": "- 単刀直入に言おう", "ar:ja:تعديل": "今日", "ar:ja:حذف": "Del", "ar:ja:اختبار": "テスト...", "ar:ja:تم الحفظ بنجاح": "正常に保存されました", "ar:ja:حدث خطأ": "エラーが発生しました", "ar:ja:هل أنت متأكد؟": "それでいいのかい？", "ar:ja:tts.filter.title": "tts.filter.title", "ar:ja:tts.title": "tts.title", "ar:ja:tts.filter.description": "tts.filter.description", "ar:ja:tts.enable": "tts.enable", "ar:ja:tts.filter.minLength": "tts.filter.minLength", "ar:ja:tts.status.disabled": "tts.status.disabled", "ar:ja:tts.filter.maxLength": "tts.filter.maxLength", "ar:ja:tts.voice.title": "tts.voice.title", "ar:ja:tts.filter.readRate": "tts.filter.readRate", "ar:ja:tts.filter.readRateHint": "tts.filter.readRateHint", "ar:ja:tts.voice.description": "tts.voice.description", "ar:ja:tts.voice.select": "tts.voice.select", "ar:ja:tts.filter.blockedWords": "tts.filter.blockedWords", "ar:ja:tts.voice.speed": "tts.voice.speed", "ar:ja:tts.voice.search": "tts.voice.search", "ar:ja:tts.voice.test.placeholder": "tts.voice.test.placeholder", "ar:ja:tts.voice.volume": "tts.voice.volume", "ar:ja:tts.voice.test.label": "tts.voice.test.label", "ar:ja:tts.filter.blockedWordsPlaceholder": "tts.filter.blockedWordsPlaceholder", "ar:ja:tts.voice.test.button": "tts.voice.test.button", "ar:ja:settings.title": "settings.title", "ar:ja:settings.general.overlayOpacity": "settings.general.overlayOpacity", "ar:ja:settings.appearance.textColor": "settings.appearance.textColor", "ar:ja:settings.appearance.enableSound": "settings.appearance.enableSound", "ar:ja:settings.appearance.fontSize": "settings.appearance.fontSize", "ar:ja:settings.appearance.animationTypes.pulse": "settings.appearance.animationTypes.pulse", "ar:ja:settings.general.title": "settings.general.title", "ar:ja:settings.general.maxItems": "settings.general.maxItems", "ar:ja:settings.appearance.animations": "settings.appearance.animations", "ar:ja:settings.appearance.enableSoundTooltip": "settings.appearance.enableSoundTooltip", "ar:ja:settings.appearance.accentColor": "settings.appearance.accentColor", "ar:ja:settings.appearance.animationTypes.flip": "settings.appearance.animationTypes.flip", "ar:ja:settings.general.overlayWidth": "settings.general.overlayWidth", "ar:ja:settings.appearance.title": "settings.appearance.title", "ar:ja:settings.appearance.animationsTooltip": "settings.appearance.animationsTooltip", "ar:ja:settings.appearance.soundVolume": "settings.appearance.soundVolume", "ar:ja:settings.appearance.fontFamily": "settings.appearance.fontFamily", "ar:ja:settings.appearance.animationTypes.slideIn": "settings.appearance.animationTypes.slideIn", "ar:ja:settings.appearance.backgroundColor": "settings.appearance.backgroundColor", "ar:ja:settings.appearance.animationType": "settings.appearance.animationType", "ar:ja:settings.preview.title": "settings.preview.title", "ar:ja:settings.appearance.animationTypes.zoomIn": "settings.appearance.animationTypes.zoomIn", "ar:ja:settings.appearance.withOpacity": "settings.appearance.withOpacity", "ar:ja:settings.preview.overlayPreview": "settings.preview.overlayPreview", "ar:ja:settings.appearance.animationTypes.random": "settings.appearance.animationTypes.random", "ar:ja:settings.appearance.animationTypes.rotateIn": "settings.appearance.animationTypes.rotateIn", "ar:ja:settings.appearance.backgroundOpacity": "settings.appearance.backgroundOpacity", "ar:ja:settings.preview.sampleText": "settings.preview.sampleText", "ar:ja:settings.appearance.animationTypes.bounce": "settings.appearance.animationTypes.bounce", "ar:ja:settings.appearance.animationTypes.glow": "settings.appearance.animationTypes.glow", "ar:ja:settings.appearance.backgroundOpacityHint": "settings.appearance.backgroundOpacityHint", "ar:ja:settings.appearance.animationTypes.shake": "settings.appearance.animationTypes.shake", "ar:ja:button.reset": "button.reset", "ar:ja:settings.appearance.animationTypeHint": "settings.appearance.animationTypeHint", "ar:ja:profiles.section.importExportDescription": "profiles.section.importExportDescription", "ar:ja:profiles.title": "profiles.title", "ar:ja:settings.appearance.loopAnimation": "settings.appearance.loopAnimation", "ar:ja:profiles.exportSettings": "profiles.exportSettings", "ar:ja:profiles.section.profiles": "profiles.section.profiles", "ar:ja:settings.appearance.loopAnimationTooltip": "settings.appearance.loopAnimationTooltip", "ar:ja:profiles.exportSettingsDescription": "profiles.exportSettingsDescription", "ar:ja:profiles.section.profilesDescription": "profiles.section.profilesDescription", "ar:ja:settings.appearance.loopAnimationHint": "settings.appearance.loopAnimationHint", "ar:ja:profiles.buttons.exportSettings": "profiles.buttons.exportSettings", "ar:ja:profiles.buttons.addProfile": "profiles.buttons.addProfile", "ar:ja:profiles.importSettings": "profiles.importSettings", "ar:ja:profiles.section.management": "profiles.section.management", "ar:ja:profiles.importSettingsDescription": "profiles.importSettingsDescription", "ar:ja:profiles.currentProfile": "profiles.currentProfile", "ar:ja:profiles.buttons.importSettings": "profiles.buttons.importSettings", "ar:ja:profiles.buttons.saveSettings": "profiles.buttons.saveSettings", "ar:ja:profiles.modal.addProfile": "profiles.modal.addProfile", "ar:ja:profiles.buttons.exportProfile": "profiles.buttons.exportProfile", "ar:ja:profiles.modal.profileName": "profiles.modal.profileName", "ar:ja:profiles.buttons.importProfile": "profiles.buttons.importProfile", "ar:ja:profiles.modal.profileNamePlaceholder": "profiles.modal.profileNamePlaceholder", "ar:ja:profiles.section.importExport": "profiles.section.importExport", "ar:es:الاتصال بـ TikTok Live": "Conéctate a TikTok Live", "ar:es:ربط الهدايا بالإجراءات": "Vincular regalos a acciones", "ar:es:قراءة التعليقات الصوتية": "<PERSON><PERSON> voz en off", "ar:es:الإعدادات": "Preferències", "ar:es:الملفات الشخصية": "<PERSON><PERSON><PERSON>", "ar:es:إعدادات اللغة": "Preferencias del idioma", "ar:es:اتصل بنا": "Llámanos.", "ar:es:فتح Overlay": "Abrir superposición", "ar:es:اسم المستخدم على TikTok:": "Nombre de usuario de TikTok:", "ar:es:أدخل اسم المستخدم (بدون @)": "Introduzca el nombre de usuario (sin @)", "ar:es:اتصال": "Coneixement", "ar:es:قطع الاتصال": "Colorado, cambio y fuera.", "ar:es:غير متصل. الرجاء إدخال اسم مستخدم.": "Sin conexión. Introduce un nombre de usuario.", "ar:es:أحداث TikTok الحية": "Eventos en directo de TikTok", "ar:es:سيتم عرض آخر الأحداث من البث المباشر هنا:": "Los últimos eventos de la transmisión en vivo se mostrarán aquí:", "ar:es:حفظ": "Guardar.", "ar:es:إلغاء": "Cancelamos.", "ar:es:إضافة": "adición", "ar:es:تعديل": "<PERSON><PERSON>", "ar:es:حذف": "Borrar...", "ar:es:اختبار": "- Sonido. Listo.", "ar:es:تم الحفظ بنجاح": "Guardado correctamente", "ar:es:حدث خطأ": "Cometió un error Vamo<PERSON>..", "ar:es:هل أنت متأكد؟": "- ¿Está seguro? - Sí.", "ar:es:tts.filter.title": "tts.filter.title", "ar:es:tts.title": "tts.title", "ar:es:tts.filter.description": "tts.filter.description", "ar:es:tts.enable": "tts.enable", "ar:es:tts.filter.minLength": "tts.filter.minLength", "ar:es:tts.status.disabled": "tts.status.disabled", "ar:es:tts.filter.maxLength": "tts.filter.maxLength", "ar:es:tts.voice.title": "tts.voice.title", "ar:es:tts.filter.readRate": "tts.filter.readRate", "ar:es:tts.voice.description": "tts.voice.description", "ar:es:tts.filter.readRateHint": "tts.filter.readRateHint", "ar:es:tts.voice.select": "tts.voice.select", "ar:es:tts.filter.blockedWords": "tts.filter.blockedWords", "ar:es:tts.voice.speed": "tts.voice.speed", "ar:es:tts.voice.search": "tts.voice.search", "ar:es:tts.voice.test.placeholder": "tts.voice.test.placeholder", "ar:es:tts.voice.volume": "tts.voice.volume", "ar:es:tts.filter.blockedWordsPlaceholder": "tts.filter.blockedWordsPlaceholder", "ar:es:tts.voice.test.label": "tts.voice.test.label", "ar:es:tts.voice.test.button": "tts.voice.test.button", "ar:es:settings.appearance.textColor": "settings.appearance.textColor", "ar:es:settings.appearance.enableSound": "settings.appearance.enableSound", "ar:es:settings.appearance.animationTypes.pulse": "settings.appearance.animationTypes.pulse", "ar:es:settings.general.overlayOpacity": "settings.general.overlayOpacity", "ar:es:settings.title": "settings.title", "ar:es:settings.appearance.fontSize": "settings.appearance.fontSize", "ar:es:settings.appearance.accentColor": "settings.appearance.accentColor", "ar:es:settings.appearance.enableSoundTooltip": "settings.appearance.enableSoundTooltip", "ar:es:settings.appearance.animationTypes.flip": "settings.appearance.animationTypes.flip", "ar:es:settings.general.title": "settings.general.title", "ar:es:settings.general.maxItems": "settings.general.maxItems", "ar:es:settings.appearance.animations": "settings.appearance.animations", "ar:es:settings.appearance.fontFamily": "settings.appearance.fontFamily", "ar:es:settings.appearance.soundVolume": "settings.appearance.soundVolume", "ar:es:settings.general.overlayWidth": "settings.general.overlayWidth", "ar:es:settings.appearance.title": "settings.appearance.title", "ar:es:settings.appearance.animationTypes.slideIn": "settings.appearance.animationTypes.slideIn", "ar:es:settings.appearance.animationsTooltip": "settings.appearance.animationsTooltip", "ar:es:settings.preview.title": "settings.preview.title", "ar:es:settings.appearance.backgroundColor": "settings.appearance.backgroundColor", "ar:es:settings.appearance.animationTypes.zoomIn": "settings.appearance.animationTypes.zoomIn", "ar:es:settings.appearance.animationType": "settings.appearance.animationType", "ar:es:settings.preview.overlayPreview": "settings.preview.overlayPreview", "ar:es:settings.appearance.withOpacity": "settings.appearance.withOpacity", "ar:es:settings.appearance.animationTypes.rotateIn": "settings.appearance.animationTypes.rotateIn", "ar:es:settings.appearance.animationTypes.random": "settings.appearance.animationTypes.random", "ar:es:settings.appearance.backgroundOpacity": "settings.appearance.backgroundOpacity", "ar:es:settings.appearance.animationTypes.glow": "settings.appearance.animationTypes.glow", "ar:es:settings.preview.sampleText": "settings.preview.sampleText", "ar:es:settings.appearance.animationTypes.bounce": "settings.appearance.animationTypes.bounce", "ar:es:settings.appearance.backgroundOpacityHint": "settings.appearance.backgroundOpacityHint", "ar:es:settings.appearance.animationTypeHint": "settings.appearance.animationTypeHint", "ar:es:settings.appearance.animationTypes.shake": "settings.appearance.animationTypes.shake", "ar:es:button.reset": "button.reset", "ar:es:settings.appearance.loopAnimation": "settings.appearance.loopAnimation", "ar:es:settings.appearance.loopAnimationHint": "settings.appearance.loopAnimationHint", "ar:es:settings.appearance.loopAnimationTooltip": "settings.appearance.loopAnimationTooltip", "ar:id:tts.filter.title": "tts.filter.title", "ar:id:tts.title": "tts.title", "ar:id:tts.filter.description": "tts.filter.description", "ar:id:tts.enable": "tts.enable", "ar:id:tts.status.disabled": "tts.status.disabled", "ar:id:tts.filter.minLength": "tts.filter.minPanjang", "ar:id:tts.filter.maxLength": "tts.filter.maxLength", "ar:id:tts.voice.title": "tts.voice.title", "ar:id:tts.voice.description": "tts.voice.description", "ar:id:tts.filter.readRate": "tts.filter.readRate", "ar:id:tts.voice.select": "tts.voice.select", "ar:id:tts.filter.readRateHint": "tts.filter.readRateHint", "ar:id:tts.voice.speed": "tts.voice.speed", "ar:id:tts.filter.blockedWords": "tts.filter.blockedWords", "ar:id:tts.voice.volume": "tts.voice.volume", "ar:id:tts.voice.search": "tts.voice.search", "ar:id:tts.voice.test.label": "tts.voice.test.label", "ar:id:tts.voice.test.placeholder": "tts.voice.test.placeholder", "ar:id:tts.voice.test.button": "tts.voice.test.button", "ar:id:tts.filter.blockedWordsPlaceholder": "tts.filter.blockedWordsPlaceholder", "ar:id:settings.appearance.textColor": "settings.appearance.textColor", "ar:id:settings.appearance.animationTypes.pulse": "settings.appearance.animationTypes.pulse", "ar:id:settings.title": "settings.title", "ar:id:settings.general.overlayOpacity": "settings.general.overlayOpacity", "ar:id:settings.appearance.fontSize": "settings.appearance.fontSize", "ar:id:settings.appearance.enableSound": "settings.appearance.enableSound", "ar:id:settings.appearance.animationTypes.flip": "settings.appearance.animationTypes.flip", "ar:id:settings.appearance.accentColor": "settings.appearance.accentColor", "ar:id:settings.appearance.enableSoundTooltip": "settings.appearance.enableSoundTooltip", "ar:id:settings.general.title": "settings.general.title", "ar:id:settings.appearance.animations": "settings.appearance.animations", "ar:id:settings.general.maxItems": "settings.general.maxItems", "ar:id:settings.appearance.animationTypes.slideIn": "settings.appearance.animationTypes.slideIn", "ar:id:settings.appearance.soundVolume": "settings.appearance.soundVolume", "ar:id:settings.general.overlayWidth": "settings.general.overlayWidth", "ar:id:settings.appearance.fontFamily": "settings.appearance.fontFamily", "ar:id:settings.appearance.animationsTooltip": "settings.appearance.animationsTooltip", "ar:id:settings.appearance.title": "settings.appearance.title", "ar:id:settings.appearance.animationTypes.zoomIn": "settings.appearance.animationTypes.zoomIn", "ar:id:settings.preview.title": "settings.preview.title", "ar:id:settings.appearance.animationType": "settings.appearance.animationType", "ar:id:settings.appearance.backgroundColor": "settings.appearance.backgroundColor", "ar:id:settings.appearance.animationTypes.rotateIn": "settings.appearance.animationTypes.rotateIn", "ar:id:settings.preview.overlayPreview": "settings.preview.overlayPreview", "ar:id:settings.appearance.animationTypes.random": "settings.appearance.animationTypes.random", "ar:id:settings.appearance.withOpacity": "settings.appearance.withOpacity", "ar:id:settings.preview.sampleText": "settings.preview.sampleText", "ar:id:settings.appearance.animationTypes.bounce": "settings.appearance.animationTypes.bounce", "ar:id:settings.appearance.animationTypes.glow": "settings.appearance.animationTypes.glow", "ar:id:settings.appearance.backgroundOpacity": "settings.appearance.backgroundOpacity", "ar:id:button.reset": "button.reset", "ar:id:settings.appearance.animationTypes.shake": "settings.appearance.animationTypes.shake", "ar:id:settings.appearance.animationTypeHint": "settings.appearance.animationTypeHint", "ar:id:settings.appearance.backgroundOpacityHint": "settings.appearance.backgroundOpacityHint", "ar:id:settings.appearance.loopAnimation": "settings.appearance.loopAnimation", "ar:id:settings.appearance.loopAnimationTooltip": "settings.appearance.loopAnimationTooltip", "ar:id:profiles.section.importExportDescription": "profiles.section.importExportDescription", "ar:id:profiles.title": "profiles.title", "ar:id:settings.appearance.loopAnimationHint": "settings.appearance.loopAnimationHint", "ar:id:profiles.exportSettings": "profiles.exportSettings", "ar:id:profiles.section.profiles": "profiles.section.profiles", "ar:id:profiles.exportSettingsDescription": "profiles.exportSettingsDescription", "ar:id:profiles.section.profilesDescription": "profiles.section.profilesDescription", "ar:id:profiles.buttons.exportSettings": "profiles.buttons.exportSettings", "ar:id:profiles.buttons.addProfile": "profiles.buttons.addProfile", "ar:id:profiles.section.management": "profiles.section.management", "ar:id:profiles.importSettings": "profiles.importSettings", "ar:id:profiles.currentProfile": "profiles.currentProfile", "ar:id:profiles.importSettingsDescription": "profiles.importSettingsDescription", "ar:id:profiles.buttons.saveSettings": "profiles.buttons.saveSettings", "ar:id:profiles.buttons.importSettings": "profiles.buttons.importSettings", "ar:id:profiles.buttons.exportProfile": "profiles.buttons.exportProfile", "ar:id:profiles.modal.addProfile": "profiles.modal.addProfile", "ar:id:profiles.buttons.importProfile": "profiles.buttons.importProfile", "ar:id:profiles.modal.profileName": "profiles.modal.profileName", "ar:id:profiles.section.importExport": "profiles.section.importExport", "ar:id:profiles.modal.profileNamePlaceholder": "profiles.modal.profileNamePlaceholder", "ar:zh:tts.filter.title": "tts.filter.title", "ar:zh:tts.title": "tts.title", "ar:zh:tts.filter.description": "tts.filter.description", "ar:zh:tts.enable": "tts.enable", "ar:zh:tts.filter.minLength": "tts.filter.minLength", "ar:zh:tts.filter.maxLength": "tts.filter.maxLength", "ar:zh:tts.status.disabled": "tts.status.disabled", "ar:zh:tts.voice.title": "tts.voice.title", "ar:zh:tts.filter.readRate": "tts.filter.readRate", "ar:zh:tts.voice.description": "tts.voice.description", "ar:zh:tts.filter.readRateHint": "tts.filter.readRateHint", "ar:zh:tts.voice.select": "tts.voice.select", "ar:zh:tts.filter.blockedWords": "tts.filter.blockedWords", "ar:zh:tts.voice.speed": "tts.voice.speed", "ar:zh:tts.voice.search": "tts.voice.search", "ar:zh:tts.voice.volume": "tts.voice.volume", "ar:zh:tts.voice.test.placeholder": "tts.voice.test.placeholder", "ar:zh:tts.voice.test.label": "tts.voice.test.label", "ar:zh:tts.filter.blockedWordsPlaceholder": "tts.filter.blockedWordsPlaceholder", "ar:zh:tts.voice.test.button": "tts.voice.test.button", "ar:zh:settings.appearance.textColor": "settings.appearance.textColor", "ar:zh:settings.appearance.enableSound": "settings.appearance.enableSound", "ar:zh:settings.appearance.fontSize": "settings.appearance.fontSize", "ar:zh:settings.general.overlayOpacity": "settings.general.overlayOpacity", "ar:zh:settings.title": "settings.title", "ar:zh:settings.appearance.animationTypes.pulse": "settings.appearance.animationTypes.pulse", "ar:zh:settings.appearance.accentColor": "settings.appearance.accentColor", "ar:zh:settings.appearance.enableSoundTooltip": "settings.appearance.enableSoundTooltip", "ar:zh:settings.general.title": "settings.general.title", "ar:zh:settings.appearance.animations": "settings.appearance.animations", "ar:zh:settings.general.maxItems": "settings.general.maxItems", "ar:zh:settings.appearance.animationTypes.flip": "settings.appearance.animationTypes.flip", "ar:zh:settings.appearance.fontFamily": "settings.appearance.fontFamily", "ar:zh:settings.appearance.soundVolume": "settings.appearance.soundVolume", "ar:zh:settings.general.overlayWidth": "settings.general.overlayWidth", "ar:zh:settings.appearance.title": "settings.appearance.title", "ar:zh:settings.appearance.animationsTooltip": "settings.appearance.animationsTooltip", "ar:zh:settings.appearance.animationTypes.slideIn": "settings.appearance.animationTypes.slideIn", "ar:zh:settings.appearance.animationType": "settings.appearance.animationType", "ar:zh:settings.appearance.backgroundColor": "settings.appearance.backgroundColor", "ar:zh:settings.preview.title": "settings.preview.title", "ar:zh:settings.appearance.animationTypes.zoomIn": "settings.appearance.animationTypes.zoomIn", "ar:zh:settings.appearance.withOpacity": "settings.appearance.withOpacity", "ar:zh:settings.appearance.animationTypes.random": "settings.appearance.animationTypes.random", "ar:zh:settings.preview.overlayPreview": "settings.preview.overlayPreview", "ar:zh:settings.appearance.backgroundOpacity": "settings.appearance.backgroundOpacity", "ar:zh:settings.appearance.animationTypes.rotateIn": "settings.appearance.animationTypes.rotateIn", "ar:zh:settings.preview.sampleText": "settings.preview.sampleText", "ar:zh:settings.appearance.animationTypes.bounce": "settings.appearance.animationTypes.bounce", "ar:zh:settings.appearance.backgroundOpacityHint": "settings.appearance.backgroundOpacityHint", "ar:zh:settings.appearance.animationTypes.glow": "settings.appearance.animationTypes.glow", "ar:zh:settings.appearance.animationTypes.shake": "settings.appearance.animationTypes.shake", "ar:zh:button.reset": "button.reset", "ar:zh:settings.appearance.animationTypeHint": "settings.appearance.animationTypeHint", "ar:zh:profiles.section.importExportDescription": "profiles.section.importExportDescription", "ar:zh:profiles.title": "profiles.title", "ar:zh:settings.appearance.loopAnimation": "settings.appearance.loopAnimation", "ar:zh:profiles.exportSettings": "profiles.exportSettings", "ar:zh:profiles.section.profiles": "profiles.section.profiles", "ar:zh:settings.appearance.loopAnimationTooltip": "settings.appearance.loopAnimationTooltip", "ar:zh:profiles.section.profilesDescription": "profiles.section.profilesDescription", "ar:zh:profiles.exportSettingsDescription": "profiles.exportSettingsDescription", "ar:zh:settings.appearance.loopAnimationHint": "settings.appearance.loopAnimationHint", "ar:zh:profiles.buttons.addProfile": "profiles.buttons.addProfile", "ar:zh:profiles.buttons.exportSettings": "profiles.buttons.exportSettings", "ar:zh:profiles.section.management": "profiles.section.management", "ar:zh:profiles.importSettings": "profiles.importSettings", "ar:zh:profiles.currentProfile": "profiles.currentProfile", "ar:zh:profiles.importSettingsDescription": "profiles.importSettingsDescription", "ar:zh:profiles.buttons.saveSettings": "profiles.buttons.saveSettings", "ar:zh:profiles.buttons.importSettings": "profiles.buttons.importSettings", "ar:zh:profiles.buttons.exportProfile": "profiles.buttons.exportProfile", "ar:zh:profiles.modal.addProfile": "profiles.modal.addProfile", "ar:zh:profiles.buttons.importProfile": "profiles.buttons.importProfile", "ar:zh:profiles.modal.profileName": "profiles.modal.profileName", "ar:zh:profiles.section.importExport": "profiles.section.importExport", "ar:zh:profiles.modal.profileNamePlaceholder": "profiles.modal.profileNamePlaceholder", "ar:zh:contact.form.subject": "contact.form.subject", "ar:zh:contact.form.submit": "contact.form.submit", "ar:zh:contact.form.message": "contact.form.message", "ar:zh:contact.title": "contact.title", "ar:zh:contact.footer": "contact.footer", "ar:zh:contact.description": "contact.description", "ar:zh:contact.email": "contact.email", "ar:zh:contact.phone": "contact.phone", "ar:zh:contact.address": "contact.address", "ar:zh:contact.form.title": "contact.form.title", "ar:zh:contact.form.name": "contact.form.name", "ar:zh:contact.form.email": "contact.form.email"}