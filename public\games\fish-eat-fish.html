<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fish Eat Fish - لعبة الأسماك الاحترافية</title>
    <link href="https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Tajawal', sans-serif;
            background: linear-gradient(135deg, #001122 0%, #003366 50%, #004488 100%);
            overflow: hidden;
            user-select: none;
            touch-action: none;
        }

        #gameCanvas {
            display: block;
            background: linear-gradient(to bottom,
                #87CEEB 0%,
                #4682B4 20%,
                #1E90FF 40%,
                #0066CC 60%,
                #003366 80%,
                #001122 100%);
            cursor: none;
            border: 3px solid #00ffff;
            box-shadow: 0 0 30px rgba(0, 255, 255, 0.5);
        }

        .game-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .ui-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            pointer-events: none;
            z-index: 10;
        }

        /* قوائم قابلة للسحب والإفلات */
        .draggable-panel {
            cursor: move;
            user-select: none;
            transition: box-shadow 0.3s ease;
        }

        .draggable-panel:hover {
            box-shadow: 0 0 25px rgba(0, 255, 255, 0.5) !important;
        }

        .draggable-panel.dragging {
            z-index: 1000;
            transform: rotate(2deg);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5) !important;
        }

        .panel-header {
            background: rgba(255, 255, 255, 0.1);
            margin: -15px -15px 10px -15px;
            padding: 10px 15px;
            border-radius: 15px 15px 0 0;
            cursor: move;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .panel-header h3 {
            margin: 0;
            font-size: 14px;
        }

        .panel-controls {
            display: flex;
            gap: 5px;
        }

        .panel-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }

        .panel-btn:hover {
            background: rgba(255, 255, 255, 0.4);
            transform: scale(1.1);
        }

        .minimize-btn {
            background: rgba(255, 193, 7, 0.8);
        }

        .close-btn {
            background: rgba(220, 53, 69, 0.8);
        }

        .panel-content {
            max-height: 250px;
            overflow-y: auto;
        }

        .panel-content::-webkit-scrollbar {
            width: 6px;
        }

        .panel-content::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        .panel-content::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.3);
            border-radius: 3px;
        }

        .panel-content::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.5);
        }

        .minimized {
            height: 45px !important;
            overflow: hidden;
        }

        .minimized .panel-content {
            display: none;
        }

        .stats-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 15px;
            border: 2px solid #00ffff;
            box-shadow: 0 0 20px rgba(0, 255, 255, 0.3);
            backdrop-filter: blur(10px);
            pointer-events: auto;
            min-width: 200px;
        }

        .stats-panel h3 {
            color: #00ffff;
            margin-bottom: 10px;
            text-align: center;
            font-size: 18px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            font-size: 14px;
        }

        .stat-value {
            color: #00ff7f;
            font-weight: bold;
        }

        .players-panel {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 15px;
            border: 2px solid #ff6b6b;
            box-shadow: 0 0 20px rgba(255, 107, 107, 0.3);
            backdrop-filter: blur(10px);
            pointer-events: auto;
            max-width: 250px;
            max-height: 300px;
            overflow-y: auto;
        }

        .players-panel h3 {
            color: #ff6b6b;
            margin-bottom: 10px;
            text-align: center;
            font-size: 16px;
        }

        .player-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 3px 0;
            padding: 8px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            font-size: 11px;
            transition: all 0.2s ease;
            position: relative;
        }

        .player-item:hover {
            background: rgba(255, 255, 255, 0.2);
            transform: translateX(3px);
        }

        .player-rank {
            font-weight: bold;
            margin-left: 8px;
            min-width: 25px;
            text-align: center;
        }

        .player-name {
            color: #ffffff;
            max-width: 120px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        .player-fish-count {
            color: #00ff7f;
            font-weight: bold;
        }

        .achievements-panel {
            position: absolute;
            bottom: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 15px;
            border: 2px solid #ffd700;
            box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
            backdrop-filter: blur(10px);
            pointer-events: auto;
            max-width: 300px;
        }

        .achievements-panel h3 {
            color: #ffd700;
            margin-bottom: 10px;
            text-align: center;
            font-size: 16px;
        }

        .achievement-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
            padding: 8px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            font-size: 12px;
        }

        .achievement-icon {
            font-size: 20px;
            margin-left: 10px;
        }

        .achievement-text {
            flex: 1;
        }

        .achievement-title {
            color: #ffd700;
            font-weight: bold;
        }

        .achievement-desc {
            color: #cccccc;
            font-size: 10px;
        }

        .controls-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 15px;
            border-radius: 15px;
            border: 2px solid #9b59b6;
            box-shadow: 0 0 20px rgba(155, 89, 182, 0.3);
            backdrop-filter: blur(10px);
            pointer-events: auto;
        }

        .controls-panel h3 {
            color: #9b59b6;
            margin-bottom: 10px;
            text-align: center;
            font-size: 16px;
        }

        .control-item {
            margin: 5px 0;
            font-size: 12px;
            color: #cccccc;
        }

        .demo-panel {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 20px;
            border-radius: 15px;
            border: 2px solid #e74c3c;
            box-shadow: 0 0 25px rgba(231, 76, 60, 0.4);
            backdrop-filter: blur(10px);
            pointer-events: auto;
            text-align: center;
            max-width: 400px;
            max-height: 80vh;
            overflow-y: auto;
            z-index: 100;
            cursor: move;
            user-select: none;
            transition: all 0.3s ease;
        }

        .demo-panel:hover {
            background: rgba(0, 0, 0, 0.8);
            box-shadow: 0 0 35px rgba(231, 76, 60, 0.6);
        }

        .demo-panel.dragging {
            transform: rotate(1deg);
            z-index: 1001;
            box-shadow: 0 10px 40px rgba(0, 0, 0, 0.6);
        }

        .demo-panel h2 {
            color: #e74c3c;
            margin-bottom: 20px;
            font-size: 24px;
        }

        .demo-controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(110px, 1fr));
            gap: 8px;
            margin: 15px 0;
        }

        .demo-panel::-webkit-scrollbar {
            width: 8px;
        }

        .demo-panel::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        .demo-panel::-webkit-scrollbar-thumb {
            background: rgba(231, 76, 60, 0.6);
            border-radius: 4px;
        }

        .demo-panel::-webkit-scrollbar-thumb:hover {
            background: rgba(231, 76, 60, 0.8);
        }

        /* أنماط لوحة الإعدادات */
        .settings-panel .mapping-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 8px;
            padding: 5px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 5px;
        }

        .settings-panel .mapping-item span {
            font-size: 14px;
            min-width: 80px;
        }

        .settings-panel select {
            background: rgba(0, 0, 0, 0.7);
            color: white;
            border: 1px solid #9b59b6;
            border-radius: 5px;
            padding: 4px 8px;
            font-size: 12px;
            cursor: pointer;
        }

        .settings-panel select:focus {
            outline: none;
            border-color: #e74c3c;
            box-shadow: 0 0 5px rgba(231, 76, 60, 0.5);
        }

        .settings-panel .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 5px;
        }

        .settings-panel .setting-item label {
            display: flex;
            align-items: center;
            font-size: 14px;
            cursor: pointer;
        }

        .settings-panel input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
            cursor: pointer;
        }

        .settings-panel input[type="range"] {
            flex: 1;
            margin: 0 10px;
            cursor: pointer;
        }

        .settings-panel input[type="range"]::-webkit-slider-track {
            background: rgba(255, 255, 255, 0.2);
            height: 4px;
            border-radius: 2px;
        }

        .settings-panel input[type="range"]::-webkit-slider-thumb {
            background: #9b59b6;
            height: 16px;
            width: 16px;
            border-radius: 50%;
            cursor: pointer;
        }

        .settings-panel h4 {
            margin: 0 0 10px 0;
            font-size: 16px;
            border-bottom: 1px solid rgba(155, 89, 182, 0.3);
            padding-bottom: 5px;
        }

        .demo-btn {
            background: linear-gradient(45deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 8px;
            cursor: pointer;
            font-family: 'Tajawal', sans-serif;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .demo-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .demo-btn.danger {
            background: linear-gradient(45deg, #e74c3c, #c0392b);
        }

        .demo-btn.success {
            background: linear-gradient(45deg, #27ae60, #229954);
        }

        .close-demo {
            position: absolute;
            top: 10px;
            right: 15px;
            background: #e74c3c;
            color: white;
            border: none;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .loading-screen {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #001122 0%, #003366 50%, #004488 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            color: white;
        }

        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(0, 255, 255, 0.3);
            border-top: 4px solid #00ffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            font-size: 18px;
            color: #00ffff;
            margin-bottom: 10px;
        }

        .loading-progress {
            width: 300px;
            height: 6px;
            background: rgba(0, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
        }

        .loading-bar {
            height: 100%;
            background: linear-gradient(90deg, #00ffff, #0099cc);
            width: 0%;
            transition: width 0.3s ease;
        }

        @media (max-width: 768px) {
            .stats-panel, .players-panel, .achievements-panel, .controls-panel {
                font-size: 12px;
                padding: 10px;
                max-width: 200px;
            }

            .demo-panel {
                max-width: 90%;
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="loading-screen" id="loadingScreen">
        <div class="loading-spinner"></div>
        <div class="loading-text">جاري تحميل Fish Eat Fish...</div>
        <div class="loading-progress">
            <div class="loading-bar" id="loadingBar"></div>
        </div>
    </div>

    <div class="game-container">
        <canvas id="gameCanvas"></canvas>

        <div class="ui-overlay">
            <!-- لوحة الإحصائيات -->
            <div class="stats-panel draggable-panel" id="statsPanel">
                <div class="panel-header">
                    <h3>📊 إحصائيات اللعبة</h3>
                    <div class="panel-controls">
                        <button class="panel-btn minimize-btn" onclick="togglePanel('statsPanel')">−</button>
                        <button class="panel-btn close-btn" onclick="hidePanel('statsPanel')">×</button>
                    </div>
                </div>
                <div class="panel-content">
                    <div class="stat-item">
                        <span>🐟 إجمالي الأسماك:</span>
                        <span class="stat-value" id="totalFish">0</span>
                    </div>
                    <div class="stat-item">
                        <span>⚔️ المعارك:</span>
                        <span class="stat-value" id="battles">0</span>
                    </div>
                    <div class="stat-item">
                        <span>🔥 معارك نشطة:</span>
                        <span class="stat-value" id="activeBattles">0</span>
                    </div>
                    <div class="stat-item">
                        <span>💀 الوفيات:</span>
                        <span class="stat-value" id="totalDeaths">0</span>
                    </div>
                    <div class="stat-item">
                        <span>🌹 الورود المستلمة:</span>
                        <span class="stat-value" id="rosesReceived">0</span>
                    </div>
                    <div class="stat-item">
                        <span>⏱️ وقت اللعب:</span>
                        <span class="stat-value" id="gameTime">00:00</span>
                    </div>
                    <div class="stat-item">
                        <span>🎯 FPS:</span>
                        <span class="stat-value" id="fps">60</span>
                    </div>
                    <div class="stat-item">
                        <span>🖥️ الدقة:</span>
                        <span class="stat-value" id="resolution">1920x1080</span>
                    </div>
                    <div class="stat-item">
                        <span>🎮 أنواع الأسماك:</span>
                        <span class="stat-value" id="fishTypes">0</span>
                    </div>
                    <div class="stat-item">
                        <span>👑 أكبر سمكة:</span>
                        <span class="stat-value" id="biggestFish">0</span>
                    </div>
                </div>
            </div>

            <!-- لوحة اللاعبين -->
            <div class="players-panel draggable-panel" id="playersPanel">
                <div class="panel-header">
                    <h3>👥 اللاعبون النشطون</h3>
                    <div class="panel-controls">
                        <button class="panel-btn minimize-btn" onclick="togglePanel('playersPanel')">−</button>
                        <button class="panel-btn close-btn" onclick="hidePanel('playersPanel')">×</button>
                    </div>
                </div>
                <div class="panel-content" id="playersList">
                    <div class="player-item">
                        <span class="player-name">في انتظار اللاعبين...</span>
                        <span class="player-fish-count">0</span>
                    </div>
                </div>
            </div>

            <!-- لوحة الإنجازات -->
            <div class="achievements-panel draggable-panel" id="achievementsPanel">
                <div class="panel-header">
                    <h3>🏆 الإنجازات</h3>
                    <div class="panel-controls">
                        <button class="panel-btn minimize-btn" onclick="togglePanel('achievementsPanel')">−</button>
                        <button class="panel-btn close-btn" onclick="hidePanel('achievementsPanel')">×</button>
                    </div>
                </div>
                <div class="panel-content" id="achievementsList">
                    <div class="achievement-item">
                        <div class="achievement-icon">🎮</div>
                        <div class="achievement-text">
                            <div class="achievement-title">مرحباً بك!</div>
                            <div class="achievement-desc">ابدأ اللعب لكسب الإنجازات</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- لوحة التحكم -->
            <div class="controls-panel draggable-panel" id="controlsPanel">
                <div class="panel-header">
                    <h3>🎮 التحكم</h3>
                    <div class="panel-controls">
                        <button class="panel-btn minimize-btn" onclick="togglePanel('controlsPanel')">−</button>
                        <button class="panel-btn close-btn" onclick="hidePanel('controlsPanel')">×</button>
                    </div>
                </div>
                <div class="panel-content">
                    <div class="control-item">🖱️ حرك الماوس للتحكم</div>
                    <div class="control-item">📱 اللمس للجوال</div>
                    <div class="control-item">🌹 أرسل ورود لإضافة أسماك</div>
                    <div class="control-item">💬 اكتب "هجوم" للصيد</div>
                    <div class="control-item">💬 اكتب "سرعة" للتسريع</div>
                    <div class="control-item">💬 اكتب "متوحش" للقوة</div>
                    <div class="control-item">⚔️ معارك ديناميكية بين اللاعبين</div>
                    <div class="control-item">💀 نظام صحة وموت فوري</div>
                    <div class="control-item">🤝 أسماك نفس اللاعب لا تتقاتل</div>
                    <div class="control-item">🦈 أنواع الأسماك المختلفة</div>
                    <div class="control-item">🏆 اكسب الإنجازات</div>
                </div>
            </div>


        </div>
    </div>

    <script src="/socket.io/socket.io.js"></script>
    <script type="module" src="/js/firebase-config.js"></script>
    <script>
      // تأخير تحميل auth-guard حتى يتم تحميل Firebase
      setTimeout(() => {
        const script = document.createElement('script');
        script.src = '/js/auth-guard.js';
        document.head.appendChild(script);
      }, 500);
    </script>
    <script src="/js/fish-eat-fish-game.js"></script>

    <script>
        // متغيرات حالة الاشتراك
        let currentUser = null;
        let userSubscription = null;
        let hasActiveSubscription = false;
        let authCheckDone = false;
        let hasRedirected = false;

        // التحقق من المصادقة
        window.addEventListener('authStateChanged', (event) => {
          if (authCheckDone || hasRedirected) return;

          const user = event.detail.user;
          console.log('🔐 Fish Eat Fish - Auth state:', user?.email);

          if (!user) {
            console.log('❌ No user, redirecting to auth');
            hasRedirected = true;
            window.location.href = '/auth.html';
          } else if (!user.emailVerified) {
            console.log('❌ Email not verified, redirecting to verification');
            hasRedirected = true;
            window.location.href = '/email-verification.html';
          } else {
            console.log('✅ User authenticated and verified');
            authCheckDone = true;
            currentUser = user;

            // تحميل بيانات الاشتراك
            loadUserSubscription();
          }
        });

        // تحميل بيانات اشتراك المستخدم
        async function loadUserSubscription() {
          try {
            if (window.firebaseHelpers && currentUser) {
              userSubscription = await window.firebaseHelpers.getUserSubscription(currentUser.uid);
              hasActiveSubscription = userSubscription &&
                userSubscription.status === 'active' &&
                new Date() < (userSubscription.endDate?.toDate ? userSubscription.endDate.toDate() : new Date(userSubscription.endDate));

              console.log('User subscription status:', hasActiveSubscription ? 'Active' : 'None');

              if (!hasActiveSubscription) {
                showSubscriptionRequired();
              } else {
                hideSubscriptionMessage();
                initializeGame();
              }
            }
          } catch (error) {
            console.error('Error loading user subscription:', error);
            hasActiveSubscription = false;
            showSubscriptionRequired();
          }
        }

        // عرض رسالة الاشتراك المطلوب
        function showSubscriptionRequired() {
          // إخفاء اللعبة
          const gameContainer = document.querySelector('.game-container');
          if (gameContainer) {
            gameContainer.style.display = 'none';
          }

          // إنشاء رسالة الاشتراك
          const subscriptionMessage = document.createElement('div');
          subscriptionMessage.id = 'subscription-message';
          subscriptionMessage.innerHTML = `
            <div style="
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              display: flex;
              align-items: center;
              justify-content: center;
              z-index: 10000;
              color: white;
              text-align: center;
              font-family: 'Tajawal', sans-serif;
            ">
              <div style="
                max-width: 600px;
                padding: 40px;
                background: rgba(255,255,255,0.1);
                border-radius: 20px;
                backdrop-filter: blur(10px);
                box-shadow: 0 20px 40px rgba(0,0,0,0.2);
              ">
                <div style="font-size: 4rem; margin-bottom: 20px;">🔒</div>
                <h1 style="font-size: 2.5rem; margin-bottom: 20px;">لعبة Fish Eat Fish مقفلة</h1>
                <p style="font-size: 1.3rem; margin-bottom: 30px; opacity: 0.9;">
                  هذه اللعبة متاحة فقط للمشتركين في الباقة الشاملة
                </p>

                <div style="
                  background: rgba(255,255,255,0.1);
                  border-radius: 15px;
                  padding: 25px;
                  margin: 30px 0;
                ">
                  <h3 style="font-size: 1.5rem; margin-bottom: 20px;">🐟 ما ستحصل عليه:</h3>
                  <div style="text-align: right; margin: 20px 0;">
                    <div style="margin: 10px 0;">🌹 تفاعل مباشر مع TikTok Live</div>
                    <div style="margin: 10px 0;">🦈 أسماك تنمو وتتطور</div>
                    <div style="margin: 10px 0;">⚔️ معارك ملحمية في أعماق المحيط</div>
                    <div style="margin: 10px 0;">🏆 نظام إنجازات متقدم</div>
                    <div style="margin: 10px 0;">🎨 رسوميات احترافية وتأثيرات مذهلة</div>
                    <div style="margin: 10px 0;">⚙️ إعدادات متقدمة وتخصيص كامل</div>
                  </div>
                </div>

                <div style="margin: 30px 0;">
                  <div style="
                    display: inline-block;
                    background: rgba(255,255,255,0.2);
                    padding: 15px 25px;
                    border-radius: 50px;
                    margin-bottom: 10px;
                  ">
                    <span style="font-size: 2rem; font-weight: bold; color: #00ff7f;">$10</span>
                    <span style="font-size: 1rem; opacity: 0.8;"> / شهرياً</span>
                  </div>
                  <p style="font-size: 1.1rem; opacity: 0.9;">باقة واحدة شاملة - جميع الميزات</p>
                </div>

                <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                  <button onclick="window.location.href='/subscriptions.html'" style="
                    background: linear-gradient(45deg, #00ff7f, #00cc66);
                    color: white;
                    border: none;
                    padding: 15px 30px;
                    border-radius: 50px;
                    font-size: 1.1rem;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    font-family: 'Tajawal', sans-serif;
                    box-shadow: 0 10px 20px rgba(0,255,127,0.3);
                  " onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 15px 30px rgba(0,255,127,0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 20px rgba(0,255,127,0.3)'">
                    ⚡ اشترك الآن
                  </button>
                  <button onclick="window.location.href='/games.html'" style="
                    background: rgba(255,255,255,0.2);
                    color: white;
                    border: 2px solid rgba(255,255,255,0.3);
                    padding: 15px 30px;
                    border-radius: 50px;
                    font-size: 1.1rem;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    font-family: 'Tajawal', sans-serif;
                    backdrop-filter: blur(10px);
                  " onmouseover="this.style.background='rgba(255,255,255,0.3)'; this.style.transform='translateY(-3px)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(0)'">
                    🔙 العودة للألعاب
                  </button>
                </div>
              </div>
            </div>
          `;

          document.body.appendChild(subscriptionMessage);
        }

        // إخفاء رسالة الاشتراك
        function hideSubscriptionMessage() {
          const subscriptionMessage = document.getElementById('subscription-message');
          if (subscriptionMessage) {
            subscriptionMessage.remove();
          }
        }

        // تهيئة اللعبة للمشتركين
        function initializeGame() {
          const gameContainer = document.querySelector('.game-container');
          if (gameContainer) {
            gameContainer.style.display = 'flex';
          }

          // تهيئة اللعبة هنا
          console.log('🎮 Initializing Fish Eat Fish game for subscriber');
        }

        // وظائف السحب والإفلات المحسنة للقوائم
        let isDragging = false;
        let currentPanel = null;
        let startX, startY, startLeft, startTop;
        let dragOffset = { x: 0, y: 0 };

        // إعداد السحب والإفلات لجميع القوائم
        document.addEventListener('DOMContentLoaded', function() {
            const panels = document.querySelectorAll('.draggable-panel');

            panels.forEach(panel => {
                const header = panel.querySelector('.panel-header');
                if (header) {
                    header.addEventListener('mousedown', startDrag);
                    header.addEventListener('touchstart', startDrag);
                }

                // إضافة السحب للوحة كاملة إذا لم يكن لها header
                if (!header) {
                    panel.addEventListener('mousedown', startDrag);
                    panel.addEventListener('touchstart', startDrag);
                }
            });

            // إضافة السحب للوحة التجربة
            setTimeout(() => {
                const demoPanel = document.getElementById('demoPanel');
                if (demoPanel) {
                    demoPanel.addEventListener('mousedown', startDrag);
                    demoPanel.addEventListener('touchstart', startDrag);
                }
            }, 1000);

            document.addEventListener('mousemove', drag);
            document.addEventListener('touchmove', drag);
            document.addEventListener('mouseup', stopDrag);
            document.addEventListener('touchend', stopDrag);
        });

        function startDrag(e) {
            // تجاهل النقر على الأزرار
            if (e.target.classList.contains('panel-btn') ||
                e.target.classList.contains('demo-btn') ||
                e.target.classList.contains('close-demo')) {
                return;
            }

            e.preventDefault();
            e.stopPropagation();
            isDragging = true;

            // البحث عن اللوحة (قد تكون demo-panel أو draggable-panel)
            currentPanel = e.target.closest('.draggable-panel') || e.target.closest('.demo-panel');

            if (!currentPanel) return;

            currentPanel.classList.add('dragging');

            const clientX = e.type === 'touchstart' ? e.touches[0].clientX : e.clientX;
            const clientY = e.type === 'touchstart' ? e.touches[0].clientY : e.clientY;

            const rect = currentPanel.getBoundingClientRect();

            // حساب الإزاحة من نقطة النقر إلى زاوية اللوحة
            dragOffset.x = clientX - rect.left;
            dragOffset.y = clientY - rect.top;

            startX = clientX;
            startY = clientY;
            startLeft = rect.left;
            startTop = rect.top;

            // التأكد من أن اللوحة في وضع fixed
            currentPanel.style.position = 'fixed';
            currentPanel.style.zIndex = '1000';
            currentPanel.style.transition = 'none'; // إيقاف الانتقالات أثناء السحب
        }

        function drag(e) {
            if (!isDragging || !currentPanel) return;

            e.preventDefault();
            e.stopPropagation();

            const clientX = e.type === 'touchmove' ? e.touches[0].clientX : e.clientX;
            const clientY = e.type === 'touchmove' ? e.touches[0].clientY : e.clientY;

            // حساب الموقع الجديد مع مراعاة الإزاحة
            let newLeft = clientX - dragOffset.x;
            let newTop = clientY - dragOffset.y;

            // التأكد من بقاء اللوحة داخل الشاشة مع هامش
            const margin = 10;
            const maxLeft = window.innerWidth - currentPanel.offsetWidth - margin;
            const maxTop = window.innerHeight - currentPanel.offsetHeight - margin;

            newLeft = Math.max(margin, Math.min(maxLeft, newLeft));
            newTop = Math.max(margin, Math.min(maxTop, newTop));

            currentPanel.style.left = newLeft + 'px';
            currentPanel.style.top = newTop + 'px';
        }

        function stopDrag() {
            if (!isDragging || !currentPanel) return;

            isDragging = false;
            currentPanel.classList.remove('dragging');

            // إعادة تفعيل الانتقالات
            currentPanel.style.transition = '';

            // إعادة تعيين z-index حسب نوع اللوحة
            if (currentPanel.classList.contains('demo-panel')) {
                currentPanel.style.zIndex = '100';
            } else {
                currentPanel.style.zIndex = '10';
            }

            currentPanel = null;
        }

        // وظائف التحكم في القوائم
        function togglePanel(panelId) {
            const panel = document.getElementById(panelId);
            if (panel) {
                panel.classList.toggle('minimized');
                const btn = panel.querySelector('.minimize-btn');
                if (btn) {
                    btn.textContent = panel.classList.contains('minimized') ? '+' : '−';
                }
            }
        }

        function hidePanel(panelId) {
            const panel = document.getElementById(panelId);
            if (panel) {
                panel.style.display = 'none';
            }
        }

        function showPanel(panelId) {
            const panel = document.getElementById(panelId);
            if (panel) {
                panel.style.display = 'block';
                panel.classList.remove('minimized');
                const btn = panel.querySelector('.minimize-btn');
                if (btn) {
                    btn.textContent = '−';
                }
            }
        }

        // إضافة قائمة سياقية لإظهار القوائم المخفية
        document.addEventListener('contextmenu', function(e) {
            e.preventDefault();
            showContextMenu(e.clientX, e.clientY);
        });

        function showContextMenu(x, y) {
            // إزالة القائمة السياقية الموجودة
            const existingMenu = document.getElementById('contextMenu');
            if (existingMenu) {
                existingMenu.remove();
            }

            const menu = document.createElement('div');
            menu.id = 'contextMenu';
            menu.style.cssText = `
                position: fixed;
                left: ${x}px;
                top: ${y}px;
                background: rgba(0, 0, 0, 0.9);
                border: 2px solid #00ffff;
                border-radius: 8px;
                padding: 10px;
                z-index: 2000;
                color: white;
                font-family: 'Tajawal', sans-serif;
                min-width: 150px;
            `;

            const panels = [
                { id: 'statsPanel', name: '📊 الإحصائيات' },
                { id: 'playersPanel', name: '👥 اللاعبون' },
                { id: 'achievementsPanel', name: '🏆 الإنجازات' },
                { id: 'controlsPanel', name: '🎮 التحكم' }
            ];

            panels.forEach(panel => {
                const panelElement = document.getElementById(panel.id);
                if (panelElement && panelElement.style.display === 'none') {
                    const item = document.createElement('div');
                    item.textContent = `إظهار ${panel.name}`;
                    item.style.cssText = `
                        padding: 5px 10px;
                        cursor: pointer;
                        border-radius: 4px;
                        transition: background 0.2s;
                    `;
                    item.addEventListener('mouseenter', () => {
                        item.style.background = 'rgba(0, 255, 255, 0.2)';
                    });
                    item.addEventListener('mouseleave', () => {
                        item.style.background = 'transparent';
                    });
                    item.addEventListener('click', () => {
                        showPanel(panel.id);
                        menu.remove();
                    });
                    menu.appendChild(item);
                }
            });

            if (menu.children.length === 0) {
                const noItems = document.createElement('div');
                noItems.textContent = 'جميع القوائم مرئية';
                noItems.style.padding = '5px 10px';
                noItems.style.color = '#888';
                menu.appendChild(noItems);
            }

            document.body.appendChild(menu);

            // إزالة القائمة عند النقر في مكان آخر
            setTimeout(() => {
                document.addEventListener('click', function removeMenu() {
                    menu.remove();
                    document.removeEventListener('click', removeMenu);
                }, 100);
            });
        }

        // منع السحب على الأزرار والعناصر التفاعلية
        document.addEventListener('DOMContentLoaded', function() {
            // منع السحب على أزرار القوائم
            const buttons = document.querySelectorAll('.panel-btn, .demo-btn, .close-demo');
            buttons.forEach(btn => {
                btn.addEventListener('mousedown', (e) => e.stopPropagation());
                btn.addEventListener('touchstart', (e) => e.stopPropagation());
            });

            // منع السحب على المحتوى القابل للتمرير
            const scrollableContent = document.querySelectorAll('.panel-content, .demo-controls, select, input');
            scrollableContent.forEach(content => {
                content.addEventListener('mousedown', (e) => {
                    e.stopPropagation();
                });
                content.addEventListener('touchstart', (e) => {
                    e.stopPropagation();
                });
            });

            // تحميل الإعدادات المحفوظة (سيتم تحميلها تلقائياً في اللعبة)
            console.log('🎮 تم تهيئة اللعبة - الإعدادات ستُحمل تلقائياً');
        });

        // رسالة توضيحية للإعدادات
        console.log('⚙️ لتخصيص إعدادات اللعبة، افتح صفحة الإعدادات من الرابط في أسفل الشاشة');
    </script>
</body>
</html>
