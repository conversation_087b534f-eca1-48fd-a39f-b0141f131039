directories:
  output: dist
  buildResources: build
appId: com.streamtok.streamtok
productName: StreamTok
files:
  - filter:
      - '**/*'
      - '!node_modules/@jitsi/robotjs/build'
      - '!node_modules/robotjs/build'
extraFiles:
  - from: node_modules/electron/dist/node.exe
    to: node.exe
win:
  target: nsis
  icon: public/favicon.ico
nsis:
  oneClick: false
  allowToChangeInstallationDirectory: true
nodeGypRebuild: false
buildDependenciesFromSource: false
npmRebuild: false
asar: false
beforeBuild: echo 'Skipping native rebuild'
electronVersion: 28.3.3
