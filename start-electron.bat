@echo off
chcp 65001 >nul
cls

echo.
echo ========================================
echo    TikTok Live Overlay - Electron
echo ========================================
echo.

:: تحقق من وجود Node.js
where node >nul 2>nul
if %ERRORLEVEL% neq 0 (
  echo [خطأ] لم يتم العثور على Node.js
  echo الرجاء تثبيت Node.js من https://nodejs.org
  echo.
  pause
  exit /b 1
)

:: تحقق من تثبيت المكتبات
if not exist "node_modules" (
  echo تثبيت المكتبات المطلوبة...
  echo.
  npm install
  if %ERRORLEVEL% neq 0 (
    echo [خطأ] فشل في تثبيت المكتبات
    pause
    exit /b 1
  )
)

:: تحقق من وجود Electron
if not exist "node_modules\electron" (
  echo تثبيت Electron...
  echo.
  npm install electron --save
  if %ERRORLEVEL% neq 0 (
    echo [خطأ] فشل في تثبيت Electron
    pause
    exit /b 1
  )
)

:: تشغيل التطبيق
echo جاري تشغيل TikTok Live Overlay...
echo.
echo اضغط Ctrl+C لإيقاف التطبيق عند الانتهاء.
echo.

:: تشغيل Electron
npm run electron

echo.
echo تم إغلاق التطبيق.
pause
