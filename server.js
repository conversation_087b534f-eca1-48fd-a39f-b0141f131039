const express = require('express');
const http = require('http');
const WebSocket = require('ws');
const path = require('path');
const { WebcastPushConnection } = require('tiktok-live-connector');
const { exec } = require('child_process');
const fs = require('fs');
const util = require('util');

const app = express();
const server = http.createServer(app);
// تعديل WebSocket لدعم الـ paths المتعددة
const mainWss = new WebSocket.Server({ noServer: true });
const overlayWss = new WebSocket.Server({ noServer: true });

// توجيه طلبات WebSocket حسب المسار
server.on('upgrade', function (request, socket, head) {
    const pathname = new URL(request.url, `http://${request.headers.host}`).pathname;

    if (pathname === '/overlay' || /^\/overlay[1-9][0-9]?$|^\/overlay50$/.test(pathname)) {
        overlayWss.handleUpgrade(request, socket, head, function (ws) {
            overlayWss.emit('connection', ws, request);
        });
    } else {
        mainWss.handleUpgrade(request, socket, head, function (ws) {
            mainWss.emit('connection', ws, request);
        });
    }
});

// تحويل exec إلى وعد (promise)
const execPromise = util.promisify(exec);

// حفظ اتصالات العملاء WebSocket
const clients = new Map();
const overlayClients = new Map();

// حفظ اتصال TikTok
let tiktokConnection = null;
let currentUsername = null;

// إعدادات Overlay
const overlaySettings = new Map();
const defaultOverlaySettings = {
    position: 'top',
    duration: 5000,
    maxAlerts: 5,
    allowVideos: true,
    allowImages: true,
    allowSound: true,
    enabled: true
};

// التعامل مع البيانات JSON
app.use(express.json());

// تقديم الملفات الثابتة من المجلد الحالي
app.use(express.static(__dirname));
// حفظ الملفات الصوتية في مجلد مؤقت
const tempAudioDir = path.join(__dirname, 'temp_audio');

// التأكد من وجود المجلد المؤقت
if (!fs.existsSync(tempAudioDir)) {
    fs.mkdirSync(tempAudioDir);
}

// مسار خاص للـ overlay screen
app.get('/overlay', (req, res) => {
    res.sendFile(path.join(__dirname, 'overlay.html'));
});

// إضافة دعم لمسارات overlay متعددة (overlay1, overlay2, ... إلخ)
app.get('/overlay:num', (req, res) => {
    const overlayNum = req.params.num;
    // التأكد من أن الرقم صالح (رقم بين 1 و 50)
    if (overlayNum && /^[1-9][0-9]?$|^50$/.test(overlayNum)) {
        // استخدام نفس ملف overlay.html لكل المسارات
        res.sendFile(path.join(__dirname, 'overlay.html'));
    } else {
        res.status(404).send('Overlay not found');
    }
});

// مسار خاص لصفحة الاشتراكات
// مسار صفحة الاشتراكات
app.get('/subscriptions', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'subscriptions.html'));
});

// مسار صفحة المصادقة
app.get('/auth', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'auth.html'));
});

// مسار صفحة تأكيد البريد الإلكتروني
app.get('/email-verification', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'email-verification.html'));
});

// مسار صفحة إعداد Firestore
app.get('/setup-firestore', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'setup-firestore.html'));
});

// مسار الصفحة الرئيسية
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'connection.html'));
});

// ملاحظة: تم نقل نظام الاشتراكات إلى Firebase
// لم تعد هناك حاجة لـ API endpoints محلية للاشتراكات

// مسار الحصول على هدايا TikTok
app.get('/tiktok-gifts', async (req, res) => {
    try {
        const axios = require('axios');

        // استخدام وكيل للتغلب على قيود CORS
        const response = await axios.get('https://webcast.tiktok.com/webcast/gift/list/?aid=1988', {
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.110 Safari/537.36'
            }
        });

        res.json(response.data);
    } catch (error) {
        console.error('Error fetching TikTok gifts:', error.message);
        res.status(500).json({
            success: false,
            message: error.message || 'Failed to fetch TikTok gifts'
        });
    }
});

// مسار الاتصال بـ TikTok
app.post('/connect', async (req, res) => {
    try {
        const { username } = req.body;

        if (!username) {
            return res.status(400).json({ success: false, message: 'Username is required' });
        }

        // إغلاق أي اتصال سابق
        if (tiktokConnection) {
            await disconnectFromTikTok();
        }

        // إنشاء اتصال جديد
        tiktokConnection = new WebcastPushConnection(username);
        currentUsername = username;

        // محاولة الاتصال بـ TikTok Live
        await tiktokConnection.connect();

        console.log(`Connected to TikTok Live for user: ${username}`);

        // إعداد معالجات الأحداث
        setupTikTokEventHandlers();

        return res.json({ success: true });
    } catch (error) {
        console.error('Error connecting to TikTok:', error.message);
        return res.status(500).json({
            success: false,
            message: error.message || 'Failed to connect to TikTok Live'
        });
    }
});

// مسار محاكاة الضغط على المفاتيح
app.post('/simulate-keypress', async (req, res) => {
    try {
        const { combo, modifiers, mainKey } = req.body;

        // التحقق من توفر بيانات المفاتيح
        if (!combo) {
            return res.status(400).json({
                success: false,
                message: 'Missing key combination data'
            });
        }

        console.log(`محاولة محاكاة الضغط على المفاتيح: ${combo}`);
        let simulationSuccess = false;

        // محاولة استخدام مكتبة robot.js إذا كانت متاحة
        try {
            const robot = require('robotjs');

            // ضغط مفاتيح التعديل
            if (modifiers.ctrl) robot.keyToggle('control', 'down');
            if (modifiers.alt) robot.keyToggle('alt', 'down');
            if (modifiers.shift) robot.keyToggle('shift', 'down');

            // ضغط المفتاح الرئيسي
            if (mainKey) {
                robot.keyTap(mainKey.toLowerCase());
            }

            // رفع مفاتيح التعديل
            if (modifiers.shift) robot.keyToggle('shift', 'up');
            if (modifiers.alt) robot.keyToggle('alt', 'up');
            if (modifiers.ctrl) robot.keyToggle('control', 'up');

            console.log('تم تنفيذ محاكاة المفاتيح بنجاح باستخدام robotjs');
            simulationSuccess = true;
        } catch (robotError) {
            console.log('RobotJS غير متاح، محاولة استخدام node-key-sender...');

            // محاولة استخدام node-key-sender كبديل
            try {
                const ks = require('node-key-sender');

                // تكوين مفاتيح التعديل
                if (modifiers.ctrl) ks.sendCombination(['control']);
                if (modifiers.alt) ks.sendCombination(['alt']);
                if (modifiers.shift) ks.sendCombination(['shift']);

                // ضغط المفتاح الرئيسي مع أي مفاتيح تعديل
                if (mainKey) {
                    const combination = [];
                    if (modifiers.ctrl) combination.push('control');
                    if (modifiers.alt) combination.push('alt');
                    if (modifiers.shift) combination.push('shift');
                    combination.push(mainKey.toLowerCase());

                    ks.sendCombination(combination);
                }

                console.log('تم تنفيذ محاكاة المفاتيح بنجاح باستخدام node-key-sender');
                simulationSuccess = true;
            } catch (ksError) {
                console.error('فشل استخدام node-key-sender:', ksError.message);

                // محاولة أخرى باستخدام child_process لتشغيل سكريبت خارجي
                try {
                    const { execSync } = require('child_process');
                    const os = require('os');

                    // إنشاء أمر محدد للنظام لمحاكاة المفاتيح
                    let command = '';
                    const platform = os.platform();

                    if (platform === 'win32') {
                        // استخدام SendKeys في Windows
                        const fullCombo = [];
                        if (modifiers.ctrl) fullCombo.push('^');
                        if (modifiers.alt) fullCombo.push('%');
                        if (modifiers.shift) fullCombo.push('+');
                        if (mainKey) fullCombo.push(mainKey);

                        const comboStr = fullCombo.join('');
                        command = `powershell -Command "Add-Type -AssemblyName System.Windows.Forms; [System.Windows.Forms.SendKeys]::SendWait('${comboStr}')"`;
                    } else if (platform === 'linux') {
                        // استخدام xdotool في Linux
                        const linuxKeys = [];
                        if (modifiers.ctrl) linuxKeys.push('ctrl');
                        if (modifiers.alt) linuxKeys.push('alt');
                        if (modifiers.shift) linuxKeys.push('shift');
                        if (mainKey) linuxKeys.push(mainKey.toLowerCase());

                        command = `xdotool key ${linuxKeys.join('+')}`;
                    } else if (platform === 'darwin') {
                        // استخدام AppleScript في macOS
                        const macKeys = [];
                        if (modifiers.ctrl) macKeys.push('control');
                        if (modifiers.alt) macKeys.push('option');
                        if (modifiers.shift) macKeys.push('shift');
                        if (mainKey) macKeys.push(mainKey.toLowerCase());

                        const keyStr = macKeys.join(' down ') + (macKeys.length > 0 ? ' down ' : '');
                        command = `osascript -e 'tell application "System Events" to key down {${keyStr}}' -e 'delay 0.1' -e 'tell application "System Events" to key up {${keyStr}}'`;
                    }

                    if (command) {
                        execSync(command);
                        console.log('تم تنفيذ محاكاة المفاتيح بنجاح باستخدام أمر النظام');
                        simulationSuccess = true;
                    } else {
                        console.warn('نظام التشغيل غير مدعوم للمحاكاة:', platform);
                    }
                } catch (execError) {
                    console.error('فشل تنفيذ أمر النظام:', execError.message);
                }
            }
        }

        if (simulationSuccess) {
            return res.json({
                success: true,
                message: `Key press simulated: ${combo}`
            });
        } else {
            return res.status(500).json({
                success: false,
                message: 'Key simulation not available on this server'
            });
        }
    } catch (error) {
        console.error('Error during key simulation:', error);
        return res.status(500).json({
            success: false,
            message: error.message || 'Failed to simulate keypress'
        });
    }
});

// مسار قطع الاتصال
app.post('/disconnect', async (req, res) => {
    try {
        await disconnectFromTikTok();
        return res.json({ success: true });
    } catch (error) {
        console.error('Error disconnecting:', error);
        return res.status(500).json({
            success: false,
            message: error.message || 'Failed to disconnect'
        });
    }
});

// وظيفة قطع الاتصال بـ TikTok
async function disconnectFromTikTok() {
    if (tiktokConnection) {
        tiktokConnection.disconnect();
        tiktokConnection = null;
        currentUsername = null;
        console.log('Disconnected from TikTok Live');

        // إخطار جميع العملاء بقطع الاتصال
        broadcastToAll({
            type: 'connectionUpdate',
            status: 'disconnected',
            message: 'Disconnected from TikTok Live'
        });
    }
}

// إعداد معالجات أحداث TikTok
function setupTikTokEventHandlers() {
    if (!tiktokConnection) return;

    // استقبال رسائل الدردشة
    tiktokConnection.on('chat', data => {
        console.log(`${data.uniqueId} (${data.nickname}): ${data.comment}`);
        broadcastToAll({
            type: 'chat',
            uniqueId: data.uniqueId,
            nickname: data.nickname,
            comment: data.comment
        });
    });

    // استقبال الهدايا
    tiktokConnection.on('gift', data => {
        if (data.giftType === 1 && !data.repeatEnd) {
            // تجاهل Gifts المستمرة حتى ينتهي التكرار
            return;
        }

        console.log(`${data.uniqueId} (${data.nickname}) sent gift: ${data.giftName} x${data.repeatCount}`);
        broadcastToAll({
            type: 'gift',
            uniqueId: data.uniqueId,
            nickname: data.nickname,
            giftId: data.giftId,
            giftName: data.giftName,
            diamondCount: data.diamondCount,
            repeatCount: data.repeatCount,
            repeatEnd: data.repeatEnd
        });

        // إرسال إلى Overlay
        const giftData = {
            type: 'alert',
            alertType: 'gift',
            message: `${data.uniqueId} أرسل هدية: ${data.giftName} (×${data.repeatCount})`,
            position: 'top',
            duration: 5000
        };

        broadcastToAllOverlays(giftData);
    });

    // استقبال الإعجابات
    tiktokConnection.on('like', data => {
        console.log(`${data.uniqueId} (${data.nickname}) liked the stream ${data.likeCount} times`);
        broadcastToAll({
            type: 'like',
            uniqueId: data.uniqueId,
            nickname: data.nickname,
            likeCount: data.likeCount,
            totalLikeCount: data.totalLikeCount
        });

        if (data.likeCount % 10 === 0) { // للتقليل من عدد الإشعارات، فقط عند كل 10 إعجابات
            const likeData = {
                type: 'alert',
                alertType: 'like',
                message: `${data.uniqueId} أعجب بالبث (${data.likeCount} إعجاب)`,
                position: 'middle',
                duration: 3000
            };

            broadcastToAllOverlays(likeData);
        }
    });

    // استقبال المتابعين الجدد
    tiktokConnection.on('follow', data => {
        console.log(`${data.uniqueId} (${data.nickname}) followed the streamer`);
        broadcastToAll({
            type: 'follow',
            uniqueId: data.uniqueId,
            nickname: data.nickname
        });

        const followData = {
            type: 'alert',
            alertType: 'follow',
            message: `${data.uniqueId} تابع البث!`,
            position: 'top',
            duration: 4000
        };

        broadcastToAllOverlays(followData);
    });

    // معالجة انضمام المشاهدين
    tiktokConnection.on('member', data => {
        console.log(`${data.uniqueId} (${data.nickname}) joined the stream`);
        broadcastToAll({
            type: 'member',
            uniqueId: data.uniqueId,
            nickname: data.nickname
        });
    });

    // معالجة حدث الاشتراك
    tiktokConnection.on('share', data => {
        console.log(`${data.uniqueId} (${data.nickname}) shared the stream`);
        broadcastToAll({
            type: 'share',
            uniqueId: data.uniqueId,
            nickname: data.nickname
        });

        const shareData = {
            type: 'alert',
            alertType: 'share',
            message: `${data.uniqueId} شارك البث!`,
            position: 'top',
            duration: 4000
        };

        broadcastToAllOverlays(shareData);
    });

    tiktokConnection.on('subscribe', data => {
        console.log(`${data.uniqueId} (${data.nickname}) subscribed to the streamer`);
        broadcastToAll({
            type: 'subscribe',
            uniqueId: data.uniqueId,
            nickname: data.nickname
        });
    });

    // معالجة الأخطاء
    tiktokConnection.on('error', err => {
        console.error('TikTok connection error:', err);
        broadcastToAll({
            type: 'connectionUpdate',
            status: 'error',
            message: err.message || 'Connection error occurred'
        });
    });

    // معالجة إغلاق الاتصال
    tiktokConnection.on('disconnected', () => {
        console.log('TikTok connection disconnected');
        broadcastToAll({
            type: 'connectionUpdate',
            status: 'disconnected',
            message: 'Connection to TikTok Live ended'
        });

        tiktokConnection = null;
        currentUsername = null;
    });
}

// معالجة اتصالات WebSocket الرئيسية
mainWss.on('connection', (ws, req) => {
    const id = Date.now();

    // استخراج اسم المستخدم من عنوان URL
    const url = new URL(req.url, `http://${req.headers.host}`);
    const username = url.searchParams.get('username');

    // تخزين معلومات العميل
    clients.set(id, { ws, username });

    console.log(`WebSocket client connected. ID: ${id}, Username: ${username}`);

    // إرسال معلومات الحالة الحالية
    if (currentUsername) {
        ws.send(JSON.stringify({
            type: 'connectionUpdate',
            status: 'connected',
            username: currentUsername
        }));
    }

    // معالجة إغلاق الاتصال
    ws.on('close', () => {
        console.log(`WebSocket client disconnected. ID: ${id}`);
        clients.delete(id);
    });
});

// إعداد WebSocket للـ Overlays
overlayWss.on('connection', (ws, req) => {
    const overlayId = new URL(req.url, `http://${req.headers.host}`).searchParams.get('id') || 'default';
    console.log(`Overlay متصل: ${overlayId}`);

    // حفظ الاتصال
    if (!overlayClients.has(overlayId)) {
        overlayClients.set(overlayId, []);
    }
    overlayClients.get(overlayId).push(ws);

    // إرسال الإعدادات الحالية للـ Overlay
    const settings = overlaySettings.get(overlayId) || { ...defaultOverlaySettings };
    ws.send(JSON.stringify({
        type: 'settings',
        settings
    }));

    ws.on('message', (message) => {
        try {
            const data = JSON.parse(message);

            // معالجة طلبات الـ Overlay
            if (data.type === 'getSettings') {
                const settings = overlaySettings.get(data.overlayId || overlayId) || { ...defaultOverlaySettings };
                ws.send(JSON.stringify({
                    type: 'settings',
                    settings
                }));
            }
        } catch (error) {
            console.error('خطأ في معالجة رسالة Overlay:', error.message);
        }
    });

    ws.on('close', () => {
        console.log(`Overlay انقطع اتصاله: ${overlayId}`);

        // إزالة العميل من القائمة
        if (overlayClients.has(overlayId)) {
            const clients = overlayClients.get(overlayId);
            const index = clients.indexOf(ws);
            if (index !== -1) {
                clients.splice(index, 1);
            }

            // إزالة المعرف إذا لم يعد هناك عملاء متصلين
            if (clients.length === 0) {
                overlayClients.delete(overlayId);
            }
        }
    });
});

// نشر رسالة لجميع العملاء المتصلين
function broadcastToAll(data) {
    const message = JSON.stringify(data);

    clients.forEach(({ ws }) => {
        if (ws.readyState === WebSocket.OPEN) {
            ws.send(message);
        }
    });
}

// وظيفة لإرسال البيانات إلى Overlay محدد
function broadcastToOverlay(overlayId, data) {
    if (!overlayClients.has(overlayId)) return;

    const clients = overlayClients.get(overlayId);
    const settings = overlaySettings.get(overlayId) || { ...defaultOverlaySettings };

    // التحقق مما إذا كان الـ Overlay مفعّل
    if (!settings.enabled) return;

    const jsonData = JSON.stringify(data);

    clients.forEach(client => {
        if (client.readyState === WebSocket.OPEN) {
            client.send(jsonData);
        }
    });
}

// إرسال البيانات إلى جميع الـ Overlays
function broadcastToAllOverlays(data) {
    overlayClients.forEach((clients, overlayId) => {
        const settings = overlaySettings.get(overlayId) || { ...defaultOverlaySettings };

        // التحقق مما إذا كان الـ Overlay مفعّل
        if (!settings.enabled) return;

        const jsonData = JSON.stringify(data);

        clients.forEach(client => {
            if (client.readyState === WebSocket.OPEN) {
                client.send(jsonData);
            }
        });
    });
}

// مسار للحصول على قائمة الأصوات المتاحة في Edge TTS
app.get('/tts-voices', async (req, res) => {
    try {
        // تنفيذ أمر للحصول على قائمة الأصوات
        const { stdout } = await execPromise('edge-tts --list-voices');

        // معالجة المخرجات لاستخراج قائمة الأصوات - التنسيق الجديد
        const voiceLines = stdout.split('\n').filter(line => line.trim());
        const voices = [];

        // تخطي عنوان الجدول (السطران الأولان)
        for (let i = 2; i < voiceLines.length; i++) {
            const line = voiceLines[i];
            // تقسيم السطر بناءً على مسافات متعددة
            const parts = line.split(/\s{2,}/g).map(part => part.trim()).filter(part => part);

            if (parts.length >= 3) {
                const name = parts[0];
                const gender = parts[1];
                // استخراج رمز اللغة من اسم الصوت
                const localeParts = name.split('-');
                if (localeParts.length >= 2) {
                    const locale = `${localeParts[0]}-${localeParts[1]}`;

                    voices.push({
                        name,
                        locale,
                        gender,
                        isArabic: locale.startsWith('ar')
                    });
                }
            }
        }

        // تسجيل معلومات التشخيص
        console.log(`تم استخراج ${voices.length} صوت من stdout المكون من ${voiceLines.length} سطر`);
        if (voices.length === 0) {
            console.log("لا توجد أصوات تم استخراجها. المخرجات الأصلية:", stdout);
        } else {
            console.log("نموذج للأصوات المستخرجة:", voices.slice(0, 3));
        }

        return res.json({
            success: true,
            voices
        });
    } catch (error) {
        console.error('Error fetching TTS voices:', error);
        return res.status(500).json({
            success: false,
            message: error.message || 'Failed to fetch TTS voices'
        });
    }
});

// إضافة مسار جديد لـ TTS
app.post('/tts', async (req, res) => {
    try {
        const { text, voice, rate, pitch, volume } = req.body;

        if (!text) {
            return res.status(400).json({ success: false, message: 'Text is required' });
        }

        // إنشاء اسم ملف فريد
        const timestamp = Date.now();
        const filename = `tts_${timestamp}.mp3`;
        const outputPath = path.join(tempAudioDir, filename);

        // إعداد أمر Edge TTS
        let command = `edge-tts --text "${text.replace(/"/g, '\\"')}" --write-media "${outputPath}"`;

        // إضافة إعدادات الصوت إذا كانت متوفرة
        if (voice) {
            command += ` --voice ${voice}`;
        } else {
            // استخدام صوت عربي افتراضي
            command += ' --voice ar-SA-HamedNeural';
        }

        // إضافة إعدادات السرعة إذا تم توفيرها
        if (rate !== undefined) {
            // التأكد من وجود علامة + أو - قبل القيمة
            const rateStr = String(rate);
            // صيغة rate يجب أن تكون +X% أو -X%
            if (rateStr.startsWith('+') || rateStr.startsWith('-')) {
                command += ` --rate=${rateStr}%`;
            } else {
                // إضافة علامة + للقيم الموجبة
                const rateSign = parseInt(rate) >= 0 ? '+' : '';
                command += ` --rate=${rateSign}${rate}%`;
            }
        }

        // إضافة إعدادات النغمة إذا تم توفيرها - تغيير من % إلى Hz
        if (pitch !== undefined) {
            // التأكد من وجود علامة + أو - قبل القيمة
            const pitchStr = String(pitch);
            // إذا كانت القيمة تتضمن Hz بالفعل، استخدمها كما هي
            if (pitchStr.endsWith('Hz')) {
                if (pitchStr.startsWith('+') || pitchStr.startsWith('-')) {
                    command += ` --pitch=${pitchStr}`;
                } else {
                    // إضافة علامة + للقيم الموجبة
                    const pitchSign = parseInt(pitchStr) >= 0 ? '+' : '';
                    command += ` --pitch=${pitchSign}${pitchStr}`;
                }
            } else {
                // تحويل القيمة إلى Hz
                const pitchValue = parseInt(pitchStr.replace(/[+\-]/g, ''));
                if (pitchStr.startsWith('+') || pitchStr.startsWith('-')) {
                    command += ` --pitch=${pitchStr.charAt(0)}${pitchValue}Hz`;
                } else {
                    // إضافة علامة + للقيم الموجبة
                    const pitchSign = pitchValue >= 0 ? '+' : '';
                    command += ` --pitch=${pitchSign}${pitchValue}Hz`;
                }
            }
        }

        // إضافة إعدادات مستوى الصوت إذا تم توفيرها
        if (volume !== undefined) {
            // مستوى الصوت يجب أن يكون مسبوقاً بعلامة + أو -
            const volumeStr = String(volume);
            if (volumeStr.startsWith('+') || volumeStr.startsWith('-')) {
                command += ` --volume=${volumeStr}%`;
            } else {
                const volumeSign = parseInt(volume) >= 0 ? '+' : '';
                command += ` --volume=${volumeSign}${volume}%`;
            }
        }

        console.log(`تنفيذ أمر TTS: ${command}`);

        // تنفيذ الأمر
        await execPromise(command);

        // التحقق من وجود الملف
        if (!fs.existsSync(outputPath)) {
            throw new Error('Failed to create audio file');
        }

        // إرسال مسار الملف للتشغيل
        const audioUrl = `/temp_audio/${filename}`;
        return res.json({
            success: true,
            audioUrl,
            message: 'TTS audio created successfully'
        });

    } catch (error) {
        console.error('Error in TTS processing:', error);
        return res.status(500).json({
            success: false,
            message: error.message || 'Failed to process TTS request'
        });
    }
});

// حذف الملفات المؤقتة القديمة
function cleanupTempAudio() {
    try {
        // الحصول على قائمة الملفات في المجلد المؤقت
        const files = fs.readdirSync(tempAudioDir);

        // الوقت الحالي
        const now = Date.now();

        // حذف الملفات الأقدم من ساعة واحدة
        const oneHour = 60 * 60 * 1000;

        files.forEach(file => {
            const filePath = path.join(tempAudioDir, file);

            // الحصول على إحصائيات الملف
            const stats = fs.statSync(filePath);

            // حساب عمر الملف
            const fileAge = now - stats.mtimeMs;

            // حذف الملفات القديمة
            if (fileAge > oneHour) {
                fs.unlinkSync(filePath);
                console.log(`تم حذف ملف مؤقت قديم: ${file}`);
            }
        });
    } catch (error) {
        console.error('Error cleaning up temp audio files:', error);
    }
}

// تنظيف الملفات المؤقتة كل ساعة
setInterval(cleanupTempAudio, 60 * 60 * 1000);

// مسار لتحديث إعدادات الوسائط
app.post('/updateMediaSettings', (req, res) => {
    try {
        const { settings } = req.body;

        if (!settings) {
            return res.status(400).json({ success: false, message: 'إعدادات الوسائط مطلوبة' });
        }

        console.log('تم استلام إعدادات وسائط جديدة:', settings);

        // إرسال الإعدادات الجديدة إلى جميع العملاء
        broadcastToAll({
            type: 'mediaSettingsUpdated',
            settings
        });

        return res.json({ success: true });
    } catch (error) {
        console.error('خطأ في تحديث إعدادات الوسائط:', error.message);
        return res.status(500).json({
            success: false,
            message: error.message || 'فشل تحديث إعدادات الوسائط'
        });
    }
});

// إضافة مسارات لإدارة الـ Overlay
app.post('/overlay/settings', (req, res) => {
    try {
        const { overlayId, settings } = req.body;

        if (!overlayId) {
            return res.status(400).json({ success: false, message: 'معرف الـ Overlay مطلوب' });
        }

        // حفظ أو تحديث إعدادات Overlay
        const currentSettings = overlaySettings.get(overlayId) || { ...defaultOverlaySettings };
        const updatedSettings = { ...currentSettings, ...settings };
        overlaySettings.set(overlayId, updatedSettings);

        // إرسال الإعدادات الجديدة إلى Overlay المتصلة
        broadcastToOverlay(overlayId, {
            type: 'settings',
            settings: updatedSettings
        });

        return res.json({ success: true, settings: updatedSettings });
    } catch (error) {
        console.error('خطأ في تحديث إعدادات Overlay:', error.message);
        return res.status(500).json({
            success: false,
            message: error.message || 'فشل تحديث إعدادات Overlay'
        });
    }
});

app.get('/overlay/settings', (req, res) => {
    try {
        const { overlayId } = req.query;

        if (!overlayId) {
            // إرجاع قائمة بجميع الـ Overlays المتاحة
            const allSettings = Array.from(overlaySettings.entries()).reduce((acc, [id, settings]) => {
                acc[id] = settings;
                return acc;
            }, {});

            return res.json({ success: true, overlays: allSettings });
        }

        // إرجاع إعدادات overlay محددة
        const settings = overlaySettings.get(overlayId) || { ...defaultOverlaySettings };
        return res.json({ success: true, settings });
    } catch (error) {
        console.error('خطأ في الحصول على إعدادات Overlay:', error.message);
        return res.status(500).json({
            success: false,
            message: error.message || 'فشل الحصول على إعدادات Overlay'
        });
    }
});

app.post('/overlay/send', (req, res) => {
    try {
        const { overlayId, data } = req.body;

        if (!overlayId || !data) {
            return res.status(400).json({ success: false, message: 'معرف الـ Overlay والبيانات مطلوبة' });
        }

        // إرسال البيانات إلى Overlay
        broadcastToOverlay(overlayId, data);

        return res.json({ success: true });
    } catch (error) {
        console.error('خطأ في إرسال البيانات إلى Overlay:', error.message);
        return res.status(500).json({
            success: false,
            message: error.message || 'فشل إرسال البيانات إلى Overlay'
        });
    }
});

// بدء تشغيل الخادم
const PORT = process.env.PORT || 3000;
server.listen(PORT, () => {
    console.log(`الخادم يعمل على المنفذ ${PORT}`);
    console.log(`الصفحة الرئيسية: http://localhost:${PORT}`);
    console.log(`صفحة الـ Overlay: http://localhost:${PORT}/overlay`);
});