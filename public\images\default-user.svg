<svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="userGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ff3b5c;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#ff6b8a;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- خلفية دائرية -->
  <circle cx="60" cy="60" r="60" fill="url(#userGradient)"/>
  
  <!-- رمز المستخدم -->
  <g fill="white">
    <!-- الرأس -->
    <circle cx="60" cy="45" r="18"/>
    <!-- الجسم -->
    <path d="M60 70 C45 70, 30 80, 30 95 L30 110 L90 110 L90 95 C90 80, 75 70, 60 70 Z"/>
  </g>
</svg>
