// Team Racing Game - Main Game Logic
class TeamRacingGame {
    constructor() {
        this.canvas = null;
        this.ctx = null;
        this.gameWidth = 0;
        this.gameHeight = 0;
        
        // Game state
        this.teams = [];
        this.supporters = new Map();
        this.gameSettings = {
            raceDistance: 500,
            animationSpeed: 5,
            autoReset: true,
            showEffects: true,
            showSupporterNames: true,
            fontSize: 16,
            teamSize: 60, // Size of team racers
            trackAngle: 15, // Perspective angle for 3D effect
            trackDepth: 0.6 // How much perspective depth
        };
        
        // Default teams
        this.defaultTeams = [
            {
                name: 'الفريق الأحمر',
                color: '#e74c3c',
                joinGift: '🌹',
                progress: 0,
                supporters: [],
                customImage: null
            },
            {
                name: 'الفريق الأزرق',
                color: '#3498db',
                joinGift: '🎁',
                progress: 0,
                supporters: [],
                customImage: null
            },
            {
                name: 'الفريق الأخضر',
                color: '#27ae60',
                joinGift: '🚗',
                progress: 0,
                supporters: [],
                customImage: null
            },
            {
                name: 'الفريق الذهبي',
                color: '#f39c12',
                joinGift: '💎',
                progress: 0,
                supporters: [],
                customImage: null
            }
        ];
        
        // Progress gifts
        this.progressGifts = {
            '🎉': 5,
            '🚀': 50,
            '👑': 20,
            '💰': 30,
            '⭐': 10,
            '🔥': 15,
            '💝': 25,
            '🎊': 8
        };
        
        // Socket and demo
        this.socket = null;
        this.isConnected = false;
        this.demoMode = false;
        this.demoInterval = null;
        
        // Animation and Performance
        this.animationFrame = null;
        this.lastTime = 0;
        this.deltaTime = 0;
        this.targetFPS = 60;
        this.frameInterval = 1000 / this.targetFPS; // 16.67ms for 60fps
        this.frameCount = 0;
        this.fpsCounter = 0;
        this.lastFPSUpdate = 0;
        this.currentFPS = 0;

        // Performance optimization flags
        this.enableVSync = true;
        this.enablePerformanceMode = false;

        // Track particles for effects
        this.particles = [];
    }
    
    // Initialize the game
    init() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        
        this.resizeCanvas();
        window.addEventListener('resize', () => this.resizeCanvas());
        
        // Clean up any existing overlays from previous sessions
        this.cleanupAllOverlays();

        // Load settings
        this.loadSettings();

        // Initialize teams
        if (this.teams.length === 0) {
            this.teams = JSON.parse(JSON.stringify(this.defaultTeams));
        }
        
        // Setup UI
        this.setupUI();
        this.updateStatsPanel();
        
        // Connect socket
        this.connectSocket();
        
        // Check URL parameters
        this.checkURLParams();
        
        // Start game loop
        this.startGameLoop();
    }
    
    cleanupAllOverlays() {
        // Remove any existing image overlays from previous sessions
        const existingOverlays = document.querySelectorAll('img[style*="position: absolute"][style*="pointer-events: none"]');
        existingOverlays.forEach(overlay => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        });

        // Clean up team overlays
        if (this.teams) {
            this.teams.forEach(team => {
                if (team.imageOverlay) {
                    if (team.imageOverlay.parentNode) {
                        team.imageOverlay.parentNode.removeChild(team.imageOverlay);
                    }
                    team.imageOverlay = null;
                }
            });
        }
    }

    validateOverlays() {
        // Check and clean up corrupted overlays
        if (this.teams) {
            this.teams.forEach((team, index) => {
                if (team.imageOverlay) {
                    // Check if overlay is still in DOM and has valid style
                    if (!team.imageOverlay.parentNode || !team.imageOverlay.style) {
                        console.warn(`Cleaning up corrupted overlay for team ${index}`);
                        team.imageOverlay = null;
                    }
                    // Check if overlay is orphaned (no custom image)
                    else if (!team.customImage || !team.customImage.src) {
                        try {
                            team.imageOverlay.style.display = 'none';
                        } catch (error) {
                            console.warn(`Error hiding overlay for team ${index}:`, error);
                            team.imageOverlay = null;
                        }
                    }
                }
            });
        }
    }

    checkPerformance() {
        // Monitor performance and adjust settings if needed
        if (this.currentFPS < 45) {
            if (!this.enablePerformanceMode) {
                console.warn(`⚠️ Low FPS detected (${this.currentFPS}), enabling performance mode`);
                this.enablePerformanceMode = true;
                this.gameSettings.showEffects = false; // Disable effects for better performance
            }
        } else if (this.currentFPS > 55 && this.enablePerformanceMode) {
            console.log(`✅ FPS improved (${this.currentFPS}), disabling performance mode`);
            this.enablePerformanceMode = false;
            this.gameSettings.showEffects = true; // Re-enable effects
        }
    }

    resizeCanvas() {
        this.gameWidth = window.innerWidth;
        this.gameHeight = window.innerHeight;
        this.canvas.width = this.gameWidth;
        this.canvas.height = this.gameHeight;
    }
    
    connectSocket() {
        try {
            this.socket = io();
            
            this.socket.on('connect', () => {
                console.log('🔗 Connected to server');
                this.isConnected = true;
            });
            
            this.socket.on('disconnect', () => {
                console.log('❌ Disconnected from server');
                this.isConnected = false;
            });
            
            // Listen for TikTok Live events
            this.socket.on('gift', (data) => this.handleGift(data));
            this.socket.on('comment', (data) => this.handleComment(data));
            this.socket.on('like', (data) => this.handleLike(data));
            
        } catch (error) {
            console.error('Socket connection error:', error);
            this.isConnected = false;
        }
    }
    
    checkURLParams() {
        const urlParams = new URLSearchParams(window.location.search);
        
        if (urlParams.get('demo') === 'true') {
            setTimeout(() => {
                this.startDemo();
            }, 1000);
        }
        
        if (window.location.hash === '#settings') {
            setTimeout(() => {
                this.toggleSettings();
            }, 500);
        }
    }
    
    startGameLoop() {
        const gameLoop = (currentTime) => {
            // Calculate delta time
            this.deltaTime = currentTime - this.lastTime;

            // Frame rate limiting for consistent 60fps
            if (this.enableVSync && this.deltaTime < this.frameInterval) {
                this.animationFrame = requestAnimationFrame(gameLoop);
                return;
            }

            this.lastTime = currentTime;
            this.frameCount++;

            // Update FPS counter
            this.updateFPSCounter(currentTime);

            // Update game logic
            this.update(this.deltaTime);

            // Render frame
            this.render();

            // Continue loop
            this.animationFrame = requestAnimationFrame(gameLoop);
        };

        // Initialize timing
        this.lastTime = performance.now();
        this.lastFPSUpdate = this.lastTime;

        // Start the loop
        this.animationFrame = requestAnimationFrame(gameLoop);

        console.log(`🎮 Game loop started - Target: ${this.targetFPS} FPS`);
    }
    
    updateFPSCounter(currentTime) {
        this.fpsCounter++;

        // Update FPS display every second
        if (currentTime - this.lastFPSUpdate >= 1000) {
            this.currentFPS = this.fpsCounter;
            this.fpsCounter = 0;
            this.lastFPSUpdate = currentTime;

            // Log FPS occasionally for debugging
            if (this.frameCount % 300 === 0) { // Every 5 seconds at 60fps
                console.log(`📊 Current FPS: ${this.currentFPS}`);
            }
        }
    }

    update(deltaTime) {
        // Normalize delta time for consistent animation speed
        const normalizedDelta = Math.min(deltaTime / 16.67, 2); // Cap at 2x normal speed

        // Update particles with normalized delta
        this.updateParticles(normalizedDelta);

        // Update any animations with normalized delta
        this.updateAnimations(normalizedDelta);

        // Validate overlays periodically (every 60 frames ≈ 1 second)
        if (this.frameCount % 60 === 0) {
            this.validateOverlays();
        }

        // Performance monitoring
        if (this.frameCount % 180 === 0) { // Every 3 seconds
            this.checkPerformance();
        }
    }
    
    render() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.gameWidth, this.gameHeight);
        
        // Draw background
        this.drawBackground();
        
        // Draw race track with perspective
        this.drawPerspectiveTrack();
        
        // Draw teams
        this.drawTeams();
        
        // Draw particles
        this.drawParticles();
        
        // Draw UI elements
        this.drawUI();
    }
    
    drawBackground() {
        // Sky gradient
        const skyGradient = this.ctx.createLinearGradient(0, 0, 0, this.gameHeight * 0.4);
        skyGradient.addColorStop(0, '#87CEEB');
        skyGradient.addColorStop(0.5, '#4682B4');
        skyGradient.addColorStop(1, '#1E90FF');
        
        this.ctx.fillStyle = skyGradient;
        this.ctx.fillRect(0, 0, this.gameWidth, this.gameHeight * 0.4);
        
        // Ground gradient
        const groundGradient = this.ctx.createLinearGradient(0, this.gameHeight * 0.4, 0, this.gameHeight);
        groundGradient.addColorStop(0, '#228B22');
        groundGradient.addColorStop(0.5, '#32CD32');
        groundGradient.addColorStop(1, '#90EE90');
        
        this.ctx.fillStyle = groundGradient;
        this.ctx.fillRect(0, this.gameHeight * 0.4, this.gameWidth, this.gameHeight * 0.6);
        
        // Add some clouds
        this.drawClouds();
    }
    
    drawClouds() {
        this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
        
        // Simple cloud shapes
        const clouds = [
            { x: this.gameWidth * 0.2, y: this.gameHeight * 0.1, size: 40 },
            { x: this.gameWidth * 0.6, y: this.gameHeight * 0.15, size: 30 },
            { x: this.gameWidth * 0.8, y: this.gameHeight * 0.08, size: 35 }
        ];
        
        clouds.forEach(cloud => {
            this.ctx.beginPath();
            this.ctx.arc(cloud.x, cloud.y, cloud.size, 0, Math.PI * 2);
            this.ctx.arc(cloud.x + cloud.size * 0.5, cloud.y, cloud.size * 0.8, 0, Math.PI * 2);
            this.ctx.arc(cloud.x - cloud.size * 0.5, cloud.y, cloud.size * 0.8, 0, Math.PI * 2);
            this.ctx.fill();
        });
    }
    
    drawPerspectiveTrack() {
        const trackStartX = this.gameWidth * 0.1;
        const trackEndX = this.gameWidth * 0.9;

        // Track with vertical perspective - bottom wider (close), top narrower (far)
        const trackTopY = this.gameHeight * 0.3;
        const trackBottomY = this.gameHeight * 0.8;

        const laneCount = Math.max(this.teams.length, 1);

        // Draw track base (asphalt) with perspective trapezoid
        this.ctx.fillStyle = '#2C3E50';
        this.ctx.beginPath();

        // Calculate perspective - bottom wider, top narrower
        const perspectiveRatio = 0.6; // Top is 60% of bottom width
        const trackWidth = trackEndX - trackStartX;
        const topTrackWidth = trackWidth * perspectiveRatio;
        const topTrackStartX = trackStartX + (trackWidth - topTrackWidth) / 2;
        const topTrackEndX = topTrackStartX + topTrackWidth;

        // Draw trapezoid track (wider at bottom, narrower at top)
        this.ctx.moveTo(trackStartX, trackBottomY); // Bottom left (wide)
        this.ctx.lineTo(trackEndX, trackBottomY); // Bottom right (wide)
        this.ctx.lineTo(topTrackEndX, trackTopY); // Top right (narrow)
        this.ctx.lineTo(topTrackStartX, trackTopY); // Top left (narrow)
        this.ctx.closePath();
        this.ctx.fill();

        // Draw lane dividers with perspective
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
        this.ctx.lineWidth = 3;
        this.ctx.setLineDash([20, 15]);

        for (let i = 1; i < laneCount; i++) {
            const laneRatio = i / laneCount;

            // Calculate Y position for this lane
            const laneY = trackBottomY - ((trackBottomY - trackTopY) * laneRatio);

            // Calculate X positions with perspective (top wide, bottom narrow)
            const currentPerspective = 1 - ((1 - perspectiveRatio) * laneRatio);
            const currentTrackWidth = trackWidth * currentPerspective;
            const currentStartX = trackStartX + (trackWidth - currentTrackWidth) / 2;
            const currentEndX = currentStartX + currentTrackWidth;

            // Draw horizontal lane divider
            this.ctx.beginPath();
            this.ctx.moveTo(currentStartX, laneY);
            this.ctx.lineTo(currentEndX, laneY);
            this.ctx.stroke();
        }

        this.ctx.setLineDash([]);

        // Draw start line
        this.drawStartLine(trackStartX, trackTopY, trackBottomY, topTrackStartX);

        // Draw finish line
        this.drawFinishLine(trackEndX, trackTopY, trackBottomY, topTrackEndX);

        // Draw side barriers
        this.drawTrackBarriers(trackStartX, trackEndX, trackTopY, trackBottomY, topTrackStartX, topTrackEndX);
    }
    
    drawStartLine(trackStartX, trackTopY, trackBottomY, topTrackStartX) {
        // Calculate perspective for start line
        const perspectiveRatio = 0.6;
        const trackWidth = this.gameWidth * 0.8;
        const topTrackWidth = trackWidth * perspectiveRatio;
        const actualTopStartX = trackStartX + (trackWidth - topTrackWidth) / 2;

        this.ctx.strokeStyle = '#00ff00';
        this.ctx.lineWidth = 6;
        this.ctx.beginPath();
        this.ctx.moveTo(trackStartX, trackBottomY);
        this.ctx.lineTo(actualTopStartX, trackTopY);
        this.ctx.stroke();

        // Start line text
        this.ctx.fillStyle = '#00ff00';
        this.ctx.font = `bold ${this.gameSettings.fontSize + 4}px Tajawal`;
        this.ctx.textAlign = 'center';
        this.ctx.save();
        this.ctx.translate(trackStartX - 25, (trackTopY + trackBottomY) / 2);
        this.ctx.rotate(-Math.PI / 2);
        this.ctx.fillText('🏁 البداية', 0, 0);
        this.ctx.restore();
    }

    drawFinishLine(trackEndX, trackTopY, trackBottomY, topTrackEndX) {
        // Simple finish line that follows track edge exactly
        this.ctx.strokeStyle = '#ff0000';
        this.ctx.lineWidth = 8;
        this.ctx.beginPath();
        this.ctx.moveTo(trackEndX, trackBottomY);
        this.ctx.lineTo(topTrackEndX, trackTopY);
        this.ctx.stroke();

        // Animated checkered pattern along the finish line (60fps smooth)
        const time = performance.now() * 0.005; // Slower, smoother animation
        const checkerSize = 10;
        const height = trackBottomY - trackTopY;

        this.ctx.save();

        for (let y = 0; y < height; y += checkerSize) {
            const yRatio = y / height;
            const currentX = trackEndX - ((trackEndX - topTrackEndX) * (1 - yRatio));

            for (let xOffset = -5; xOffset <= 5; xOffset += checkerSize) {
                const isBlack = ((Math.floor(y / checkerSize) + Math.floor(xOffset / checkerSize)) % 2) === 0;
                this.ctx.fillStyle = isBlack ? '#000000' : '#ffffff';

                const rectX = currentX + xOffset + Math.sin(time + y * 0.1) * 1;
                const rectY = trackTopY + y;

                this.ctx.fillRect(rectX, rectY, checkerSize, checkerSize);
            }
        }

        this.ctx.restore();

        // Finish line text
        this.ctx.fillStyle = '#ff0000';
        this.ctx.font = `bold ${this.gameSettings.fontSize + 4}px Tajawal`;
        this.ctx.textAlign = 'center';
        this.ctx.save();
        this.ctx.translate(trackEndX + 30, (trackTopY + trackBottomY) / 2);
        this.ctx.rotate(-Math.PI / 2);
        this.ctx.fillText('🏆 النهاية', 0, 0);
        this.ctx.restore();
    }

    drawTrackBarriers(startX, endX, topY, bottomY, topTrackStartX, topTrackEndX) {
        // Top barrier - simple rectangle
        this.ctx.fillStyle = '#8B4513'; // Brown color instead of red
        this.ctx.fillRect(topTrackStartX, topY - 10, topTrackEndX - topTrackStartX, 10);

        // Bottom barrier - simple rectangle
        this.ctx.fillStyle = '#8B4513'; // Brown color instead of red
        this.ctx.fillRect(startX, bottomY, endX - startX, 10);

        // Side barriers with perspective
        this.ctx.fillStyle = '#654321'; // Darker brown for sides

        // Left side barrier
        this.ctx.beginPath();
        this.ctx.moveTo(startX - 10, bottomY);
        this.ctx.lineTo(startX, bottomY);
        this.ctx.lineTo(topTrackStartX, topY);
        this.ctx.lineTo(topTrackStartX - 5, topY);
        this.ctx.closePath();
        this.ctx.fill();

        // Right side barrier
        this.ctx.beginPath();
        this.ctx.moveTo(endX, bottomY);
        this.ctx.lineTo(endX + 10, bottomY);
        this.ctx.lineTo(topTrackEndX + 5, topY);
        this.ctx.lineTo(topTrackEndX, topY);
        this.ctx.closePath();
        this.ctx.fill();
    }

    drawTeams() {
        const trackStartX = this.gameWidth * 0.1;
        const trackEndX = this.gameWidth * 0.9;
        const trackTopY = this.gameHeight * 0.3;
        const trackBottomY = this.gameHeight * 0.8;
        const trackLength = trackEndX - trackStartX;

        // Perspective settings
        const perspectiveRatio = 0.6;
        const trackWidth = trackEndX - trackStartX;

        this.teams.forEach((team, index) => {
            try {
                const laneRatio = (this.teams.length - index - 0.5) / this.teams.length;
                const progressRatio = Math.min(team.progress / this.gameSettings.raceDistance, 1);

                // Calculate lane Y position with perspective - raised slightly
                const laneY = trackBottomY - ((trackBottomY - trackTopY) * laneRatio) - 10;

                // Calculate track width at this lane (top wide, bottom narrow)
                const currentPerspective = 1 - ((1 - perspectiveRatio) * laneRatio);
                const currentTrackWidth = trackWidth * currentPerspective;
                const currentStartX = trackStartX + (trackWidth - currentTrackWidth) / 2;

                // Calculate position along the track (horizontal movement)
                const trackX = currentStartX + (currentTrackWidth * progressRatio);

                // Team size from settings (customizable)
                const teamSize = this.gameSettings.teamSize || 60;

                // Draw team
                this.drawTeamRacer(trackX, laneY, team, teamSize, progressRatio);
            } catch (error) {
                console.warn(`Error drawing team ${index}:`, error);
                // Clean up problematic overlay
                if (team.imageOverlay) {
                    try {
                        if (team.imageOverlay.parentNode) {
                            team.imageOverlay.parentNode.removeChild(team.imageOverlay);
                        }
                        team.imageOverlay = null;
                    } catch (cleanupError) {
                        console.warn('Error cleaning up overlay:', cleanupError);
                    }
                }
            }
        });

        // Hide image overlays when showing instructions
        const hasActiveSupporters = this.teams.some(team => team.supporters.length > 0);
        if (!hasActiveSupporters && !this.demoMode) {
            this.teams.forEach(team => {
                if (team.imageOverlay && team.imageOverlay.style) {
                    team.imageOverlay.style.display = 'none';
                }
            });
        }
    }

    drawTeamRacer(x, y, team, size, progressRatio) {
        const radius = size / 2;

        // Remove wobble effect - keep teams stable
        const wobble = 0; // No vertical movement
        const speedLines = team.supporters.length > 0;

        // Draw shadow (perspective shadow)
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        this.ctx.beginPath();
        this.ctx.ellipse(x + 3, y + 8, radius * 0.8, radius * 0.3, 0, 0, Math.PI * 2);
        this.ctx.fill();

        // Draw speed lines if moving
        if (speedLines && this.gameSettings.showEffects) {
            this.drawSpeedLines(x, y, team.color, radius);
        }

        // Handle custom image with HTML overlay (better for GIFs)
        if (team.customImage && team.customImage.complete && team.customImage.src) {
            try {
                this.positionTeamImageOverlay(team, x, y + wobble, radius * 2);
            } catch (error) {
                console.warn('Error positioning overlay for team:', team.name, error);
                // Fallback to drawing shape
                this.drawTeamShape(x, y + wobble, team, radius);
            }
        } else {
            // Hide overlay if no custom image
            try {
                if (team.imageOverlay && team.imageOverlay.style) {
                    team.imageOverlay.style.display = 'none';
                }
            } catch (error) {
                console.warn('Error hiding overlay for team:', team.name, error);
                // Clean up corrupted overlay
                team.imageOverlay = null;
            }
            this.drawTeamShape(x, y + wobble, team, radius);
        }

        // Draw supporter count badge
        if (team.supporters.length > 0) {
            this.drawSupporterBadge(x + radius, y - radius + wobble, team.supporters.length, team.color);
        }

        // Draw team name above
        if (this.gameSettings.showSupporterNames) {
            this.ctx.fillStyle = team.color;
            this.ctx.font = `bold ${Math.max(10, this.gameSettings.fontSize - 4)}px Tajawal`;
            this.ctx.textAlign = 'center';
            this.ctx.fillText(team.name, x, y - radius - 15);
        }
    }

    positionTeamImageOverlay(team, x, y, size) {
        try {
            // Check if team has custom image
            if (!team.customImage || !team.customImage.src) {
                if (team.imageOverlay && team.imageOverlay.style) {
                    team.imageOverlay.style.display = 'none';
                }
                return;
            }

            // Create or update HTML image overlay for GIFs
            if (!team.imageOverlay) {
                team.imageOverlay = document.createElement('img');
                team.imageOverlay.src = team.customImage.src;
                team.imageOverlay.style.position = 'absolute';
                team.imageOverlay.style.pointerEvents = 'none';
                team.imageOverlay.style.zIndex = '5';
                team.imageOverlay.style.borderRadius = '50%';
                team.imageOverlay.style.boxShadow = '0 2px 8px rgba(0,0,0,0.3)';
                document.body.appendChild(team.imageOverlay);
            }

            // Verify overlay still exists and has style
            if (!team.imageOverlay || !team.imageOverlay.style) {
                console.warn('Image overlay missing or corrupted, recreating...');
                team.imageOverlay = null;
                return this.positionTeamImageOverlay(team, x, y, size);
            }

            // Update image source if changed
            if (team.imageOverlay.src !== team.customImage.src) {
                team.imageOverlay.src = team.customImage.src;
            }

            // Calculate position relative to canvas
            if (!this.canvas) {
                console.warn('Canvas not found, cannot position overlay');
                return;
            }

            const canvasRect = this.canvas.getBoundingClientRect();
            const left = canvasRect.left + x - size / 2;
            const top = canvasRect.top + y - size / 2;

            // Update position and size safely
            if (team.imageOverlay.style) {
                team.imageOverlay.style.left = left + 'px';
                team.imageOverlay.style.top = top + 'px';
                team.imageOverlay.style.width = size + 'px';
                team.imageOverlay.style.height = size + 'px';
                team.imageOverlay.style.display = 'block';
            }
        } catch (error) {
            console.warn('Error positioning team image overlay:', error);
            // Clean up corrupted overlay
            if (team.imageOverlay) {
                try {
                    if (team.imageOverlay.parentNode) {
                        team.imageOverlay.parentNode.removeChild(team.imageOverlay);
                    }
                } catch (cleanupError) {
                    console.warn('Error cleaning up corrupted overlay:', cleanupError);
                }
                team.imageOverlay = null;
            }
        }
    }

    drawCustomImage(x, y, image, size) {
        // Fallback for non-GIF images in canvas
        if (image && image.complete) {
            this.ctx.drawImage(image, x - size / 2, y - size / 2, size, size);
        }
    }

    drawTeamShape(x, y, team, radius) {
        // Draw only the join gift emoji (no circle or color)
        this.ctx.fillStyle = 'white';
        this.ctx.strokeStyle = 'black';
        this.ctx.lineWidth = 2;
        this.ctx.font = `${Math.floor(radius * 1.8)}px Arial`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';

        // Add text stroke for better visibility
        this.ctx.strokeText(team.joinGift, x, y);
        this.ctx.fillText(team.joinGift, x, y);
    }



    drawSpeedLines(x, y, color, radius) {
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 2;
        this.ctx.setLineDash([5, 10]);

        const lineCount = 3;
        const lineLength = 30;

        for (let i = 0; i < lineCount; i++) {
            const offsetX = (i - 1) * 8;
            const alpha = 1 - (i * 0.3);

            this.ctx.globalAlpha = alpha;
            this.ctx.beginPath();
            this.ctx.moveTo(x - radius - 5 + offsetX, y);
            this.ctx.lineTo(x - radius - lineLength + offsetX, y);
            this.ctx.stroke();
        }

        this.ctx.globalAlpha = 1;
        this.ctx.setLineDash([]);
    }

    drawSupporterBadge(x, y, count, color) {
        const badgeRadius = 12;

        // Badge background
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        this.ctx.beginPath();
        this.ctx.arc(x, y, badgeRadius, 0, Math.PI * 2);
        this.ctx.fill();

        // Badge border
        this.ctx.strokeStyle = color;
        this.ctx.lineWidth = 2;
        this.ctx.stroke();

        // Badge text
        this.ctx.fillStyle = 'white';
        this.ctx.font = `bold ${badgeRadius}px Arial`;
        this.ctx.textAlign = 'center';
        this.ctx.textBaseline = 'middle';
        this.ctx.fillText(count.toString(), x, y);
    }



    // Event handlers
    handleGift(data) {
        if (this.demoMode) return;

        const giftName = data.giftName || data.gift_name || '';
        const senderName = data.uniqueId || data.sender || 'مجهول';

        console.log(`🎁 Gift received: ${giftName} from ${senderName}`);

        // Check if it's a join gift
        const joinTeam = this.teams.find(team => team.joinGift === giftName);
        if (joinTeam && !this.supporters.has(senderName)) {
            // New supporter joins team
            this.supporters.set(senderName, joinTeam.name);
            joinTeam.supporters.push(senderName);
            console.log(`👥 ${senderName} joined ${joinTeam.name}`);

            this.showNotification(`${senderName} انضم إلى ${joinTeam.name}!`, joinTeam.color);
            this.updateStatsPanel();
            return;
        }

        // Check if it's a progress gift
        const progressValue = this.progressGifts[giftName];
        if (progressValue && this.supporters.has(senderName)) {
            const teamName = this.supporters.get(senderName);
            const team = this.teams.find(t => t.name === teamName);
            if (team) {
                team.progress += progressValue;
                console.log(`⚡ ${teamName} advanced by ${progressValue} (${senderName})`);

                this.showNotification(`${teamName} تقدم +${progressValue} بفضل ${senderName}`, team.color);
                this.createProgressParticles(team, progressValue);

                // Check for winner
                if (team.progress >= this.gameSettings.raceDistance) {
                    this.announceWinner(team);
                }

                this.updateStatsPanel();
            }
        } else if (progressValue && !this.supporters.has(senderName)) {
            this.showNotification(`${senderName}: يجب الانضمام لفريق أولاً!`, '#ff6b6b');
        }
    }

    handleComment(data) {
        const comment = (data.comment || '').toLowerCase();
        const senderName = data.uniqueId || data.sender || 'مجهول';

        if (comment.includes('انضمام') || comment.includes('فريق')) {
            console.log(`💬 ${senderName} asking about teams`);
        }
    }

    handleLike(data) {
        if (this.gameSettings.likesGiveProgress) {
            this.teams.forEach(team => {
                if (team.supporters.length > 0) {
                    team.progress += 1;
                }
            });
            this.updateStatsPanel();
        }
    }

    // Game logic
    announceWinner(winningTeam) {
        console.log(`🏆 ${winningTeam.name} WINS!`);
        this.showWinnerAnimation(winningTeam);

        if (this.gameSettings.autoReset) {
            setTimeout(() => {
                this.resetRace();
            }, 5000);
        }
    }

    resetRace() {
        this.teams.forEach(team => {
            team.progress = 0;
            team.supporters = [];

            // Clean up image overlays safely
            if (team.imageOverlay && team.imageOverlay.style) {
                team.imageOverlay.style.display = 'none';
            }
        });
        this.supporters.clear();
        this.particles = [];
        this.updateStatsPanel();
        console.log('🔄 Race reset');
    }

    // Particle system
    createProgressParticles(team, value) {
        if (!this.gameSettings.showEffects) return;

        const particleCount = Math.min(value, 10);
        for (let i = 0; i < particleCount; i++) {
            this.particles.push({
                x: Math.random() * this.gameWidth,
                y: Math.random() * this.gameHeight,
                vx: (Math.random() - 0.5) * 4,
                vy: (Math.random() - 0.5) * 4,
                color: team.color,
                life: 1.0,
                decay: 0.02,
                size: Math.random() * 8 + 4
            });
        }
    }

    updateParticles(normalizedDelta) {
        this.particles = this.particles.filter(particle => {
            // Update position with frame-rate independent movement
            particle.x += particle.vx * normalizedDelta;
            particle.y += particle.vy * normalizedDelta;
            particle.life -= particle.decay * normalizedDelta;
            particle.size *= Math.pow(0.99, normalizedDelta);

            return particle.life > 0 && particle.size > 0.5;
        });
    }

    drawParticles() {
        this.particles.forEach(particle => {
            this.ctx.save();
            this.ctx.globalAlpha = particle.life;
            this.ctx.fillStyle = particle.color;
            this.ctx.beginPath();
            this.ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
            this.ctx.fill();
            this.ctx.restore();
        });
    }

    updateAnimations(deltaTime) {
        // Update any ongoing animations
    }

    drawUI() {
        // Draw connection status
        const statusText = this.isConnected ? '🟢 متصل' : '🔴 غير متصل';
        const statusColor = this.isConnected ? '#00ff00' : '#ff0000';

        this.ctx.fillStyle = statusColor;
        this.ctx.font = `${this.gameSettings.fontSize}px Tajawal`;
        this.ctx.textAlign = 'left';
        this.ctx.fillText(statusText, 20, 30);

        // Draw FPS counter (if enabled)
        if (this.gameSettings.showFPS !== false) { // Default to true if not set
            const fpsColor = this.currentFPS >= 55 ? '#00ff00' : this.currentFPS >= 30 ? '#ffa500' : '#ff0000';
            this.ctx.fillStyle = fpsColor;
            this.ctx.fillText(`📊 ${this.currentFPS} FPS`, 20, 55);
        }

        if (this.demoMode) {
            this.ctx.fillStyle = '#ffa500';
            this.ctx.fillText('🧪 وضع التجربة', 20, 80);
        }

        // Performance mode indicator
        if (this.enablePerformanceMode) {
            this.ctx.fillStyle = '#ff6b6b';
            this.ctx.fillText('⚡ وضع الأداء', 20, this.demoMode ? 105 : 80);
        }

        // Draw instructions if no active supporters
        const hasActiveSupporters = this.teams.some(team => team.supporters.length > 0);
        if (!hasActiveSupporters && !this.demoMode) {
            this.drawInstructions();
        }
    }

    drawInstructions() {
        const centerX = this.gameWidth / 2;
        const centerY = this.gameHeight / 2;

        // Semi-transparent overlay
        this.ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        this.ctx.fillRect(0, 0, this.gameWidth, this.gameHeight);

        // Main title
        this.ctx.fillStyle = 'white';
        this.ctx.font = `bold ${this.gameSettings.fontSize + 8}px Tajawal`;
        this.ctx.textAlign = 'center';
        this.ctx.fillText('🏁 مرحباً بك في سباق الفرق!', centerX, centerY - 120);

        // Instructions
        this.ctx.font = `${this.gameSettings.fontSize + 2}px Tajawal`;
        this.ctx.fillText('كيفية اللعب:', centerX, centerY - 80);

        const instructions = [
            '1️⃣ انضم لفريق بإرسال هدية الانضمام',
            '2️⃣ ادعم فريقك بإرسال هدايا أخرى',
            '3️⃣ أول فريق يصل للنهاية يفوز!',
            '',
            'الفرق المتاحة:'
        ];

        this.ctx.font = `${this.gameSettings.fontSize}px Tajawal`;
        instructions.forEach((instruction, index) => {
            this.ctx.fillText(instruction, centerX, centerY - 40 + (index * 25));
        });

        // Team join instructions
        this.teams.forEach((team, index) => {
            const y = centerY + 40 + (index * 30);
            this.ctx.fillStyle = team.color;
            this.ctx.fillText(`${team.joinGift} ${team.name}`, centerX, y);
        });

        // Demo hint
        this.ctx.fillStyle = '#ffa500';
        this.ctx.font = `${this.gameSettings.fontSize - 2}px Tajawal`;
        this.ctx.fillText('💡 اضغط "تجربة" لمشاهدة عرض توضيحي', centerX, this.gameHeight - 50);
    }

    // UI Management
    setupUI() {
        this.setupSettingsUI();
        this.setupImageUpload();
    }

    setupImageUpload() {
        // Create file input for team images
        this.teams.forEach((team, index) => {
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = 'image/*';
            fileInput.style.display = 'none';
            fileInput.id = `teamImage_${index}`;

            fileInput.addEventListener('change', (e) => {
                this.handleImageUpload(e, index);
            });

            document.body.appendChild(fileInput);
        });
    }

    handleImageUpload(event, teamIndex) {
        const file = event.target.files[0];
        if (!file) return;

        const reader = new FileReader();
        reader.onload = (e) => {
            const img = new Image();
            img.onload = () => {
                this.teams[teamIndex].customImage = img;
                this.saveSettings();
                console.log(`✅ Image uploaded for ${this.teams[teamIndex].name}`);
            };
            img.src = e.target.result;
        };
        reader.readAsDataURL(file);
    }

    setupSettingsUI() {
        // This will be called from the HTML file
        window.gameInstance = this;
    }

    updateStatsPanel() {
        const statsContainer = document.getElementById('teamStats');
        if (!statsContainer) return;

        const sortedTeams = [...this.teams].sort((a, b) => b.progress - a.progress);

        statsContainer.innerHTML = sortedTeams.map((team, index) => {
            const progressRatio = Math.min(team.progress / this.gameSettings.raceDistance, 1);
            const isWinning = index === 0 && team.progress > 0;

            return `
                <div class="team-stat" style="border: 2px solid ${team.color}; ${isWinning ? 'box-shadow: 0 0 15px ' + team.color + ';' : ''}">
                    <div class="team-name" style="color: ${team.color};">
                        ${isWinning ? '👑 ' : ''}${team.name}
                    </div>
                    <div class="team-progress">
                        ${team.progress}/${this.gameSettings.raceDistance}
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${progressRatio * 100}%; color: ${team.color};"></div>
                    </div>
                    <div class="team-supporters">
                        الداعمون: ${team.supporters.length}
                        ${team.supporters.length > 0 ? '<br>' + team.supporters.slice(0, 2).join(', ') + (team.supporters.length > 2 ? '...' : '') : ''}
                    </div>
                </div>
            `;
        }).join('');
    }

    // Demo mode
    startDemo() {
        this.demoMode = !this.demoMode;
        const btn = document.querySelector('.demo-btn');

        if (this.demoMode) {
            btn.textContent = '⏹️ إيقاف التجربة';
            btn.style.background = '#e74c3c';
            this.startDemoSimulation();
        } else {
            btn.textContent = '🧪 تجربة';
            btn.style.background = '';
            this.stopDemoSimulation();
        }
    }

    startDemoSimulation() {
        const demoSupporters = ['أحمد', 'فاطمة', 'محمد', 'عائشة', 'علي', 'زينب', 'خالد', 'مريم'];
        const allGifts = [...Object.keys(this.progressGifts), ...this.teams.map(t => t.joinGift)];

        // Ensure each team has supporters
        this.teams.forEach((team, index) => {
            if (team.supporters.length === 0 && index < demoSupporters.length) {
                this.handleGift({
                    giftName: team.joinGift,
                    uniqueId: demoSupporters[index]
                });
            }
        });

        this.demoInterval = setInterval(() => {
            const randomSupporter = demoSupporters[Math.floor(Math.random() * demoSupporters.length)];

            let randomGift;
            if (Math.random() < 0.7 && this.supporters.has(randomSupporter)) {
                const progressGiftKeys = Object.keys(this.progressGifts);
                randomGift = progressGiftKeys[Math.floor(Math.random() * progressGiftKeys.length)];
            } else {
                randomGift = allGifts[Math.floor(Math.random() * allGifts.length)];
            }

            this.handleGift({
                giftName: randomGift,
                uniqueId: randomSupporter
            });
        }, 1500 + Math.random() * 1000);
    }

    stopDemoSimulation() {
        if (this.demoInterval) {
            clearInterval(this.demoInterval);
            this.demoInterval = null;
        }
    }

    // Settings management
    saveSettings() {
        const settings = {
            teams: this.teams.map(team => ({
                ...team,
                customImage: null // Don't save image data, just the settings
            })),
            progressGifts: this.progressGifts,
            gameSettings: this.gameSettings,
            performanceSettings: {
                targetFPS: this.targetFPS,
                enableVSync: this.enableVSync,
                enablePerformanceMode: this.enablePerformanceMode
            }
        };
        localStorage.setItem('teamRacingSettings', JSON.stringify(settings));
    }

    loadSettings() {
        try {
            const saved = localStorage.getItem('teamRacingSettings');
            if (saved) {
                const settings = JSON.parse(saved);
                if (settings.teams) this.teams = settings.teams;
                if (settings.progressGifts) this.progressGifts = settings.progressGifts;
                if (settings.gameSettings) {
                    this.gameSettings = { ...this.gameSettings, ...settings.gameSettings };
                }
                if (settings.performanceSettings) {
                    this.targetFPS = settings.performanceSettings.targetFPS || 60;
                    this.frameInterval = 1000 / this.targetFPS;
                    this.enableVSync = settings.performanceSettings.enableVSync !== false;
                    this.enablePerformanceMode = settings.performanceSettings.enablePerformanceMode || false;
                }
            }
        } catch (error) {
            console.error('Error loading settings:', error);
        }
    }

    // Utility functions
    showNotification(message, color = '#3498db') {
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${color};
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            font-family: 'Tajawal', sans-serif;
            font-size: 14px;
            font-weight: bold;
            z-index: 1001;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            animation: slideDown 0.3s ease-out;
        `;

        notification.textContent = message;
        document.body.appendChild(notification);

        setTimeout(() => {
            notification.style.animation = 'slideDown 0.3s ease-out reverse';
            setTimeout(() => notification.remove(), 300);
        }, 3000);
    }

    showWinnerAnimation(team) {
        // Create winner overlay with enhanced effects
        const winnerOverlay = document.createElement('div');
        winnerOverlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(45deg, rgba(0, 0, 0, 0.9), rgba(${this.hexToRgb(team.color)}, 0.3));
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 1000;
            color: white;
            font-family: 'Tajawal', sans-serif;
            text-align: center;
            animation: fadeIn 0.5s ease-in;
        `;

        winnerOverlay.innerHTML = `
            <div style="font-size: 8rem; margin-bottom: 30px; animation: bounce 2s infinite;">🏆</div>
            <div style="font-size: 4rem; color: ${team.color}; margin-bottom: 30px; font-weight: bold; text-shadow: 3px 3px 6px rgba(0,0,0,0.5);">
                ${team.name}
            </div>
            <div style="font-size: 2.5rem; margin-bottom: 40px; color: #ffd700;">
                🎉 فاز بالسباق! 🎉
            </div>
            <div style="font-size: 1.6rem; margin-bottom: 30px; background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px; max-width: 80%;">
                <strong>الداعمون الأبطال:</strong><br>
                ${team.supporters.slice(0, 8).join(' • ')}
                ${team.supporters.length > 8 ? `<br><small>و ${team.supporters.length - 8} آخرين</small>` : ''}
            </div>
        `;

        document.body.appendChild(winnerOverlay);
        this.createConfetti(team.color);

        setTimeout(() => winnerOverlay.remove(), 5000);
    }

    createConfetti(color) {
        // Enhanced confetti effect
        const confettiCount = 100;
        for (let i = 0; i < confettiCount; i++) {
            this.particles.push({
                x: Math.random() * this.gameWidth,
                y: -20,
                vx: (Math.random() - 0.5) * 8,
                vy: Math.random() * 3 + 2,
                color: Math.random() > 0.5 ? color : '#ffd700',
                life: 1.0,
                decay: 0.005,
                size: Math.random() * 12 + 6,
                rotation: Math.random() * Math.PI * 2,
                rotationSpeed: (Math.random() - 0.5) * 0.2
            });
        }
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ?
            `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` :
            '255, 255, 255';
    }

    toggleSettings() {
        const panel = document.getElementById('settingsPanel');
        if (panel) {
            panel.classList.toggle('show');
        }
    }

    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen();
        } else {
            document.exitFullscreen();
        }
    }

    // Cleanup
    destroy() {
        if (this.animationFrame) {
            cancelAnimationFrame(this.animationFrame);
        }
        if (this.demoInterval) {
            clearInterval(this.demoInterval);
        }
        if (this.socket) {
            this.socket.disconnect();
        }

        // Clean up image overlays
        this.teams.forEach(team => {
            if (team.imageOverlay && team.imageOverlay.parentNode) {
                team.imageOverlay.parentNode.removeChild(team.imageOverlay);
            }
        });
    }
}

// Global instance
let gameInstance = null;

// Initialize when page loads
window.addEventListener('load', () => {
    gameInstance = new TeamRacingGame();
    gameInstance.init();
});

// Global functions for HTML interface
window.toggleSettings = () => gameInstance?.toggleSettings();
window.startDemo = () => gameInstance?.startDemo();
window.resetRace = () => gameInstance?.resetRace();
window.toggleFullscreen = () => gameInstance?.toggleFullscreen();

// Performance functions
window.setTargetFPS = (fps) => {
    if (gameInstance) {
        gameInstance.targetFPS = fps;
        gameInstance.frameInterval = 1000 / fps;
        gameInstance.saveSettings();
        console.log(`🎯 Target FPS set to ${fps}`);
    }
};

window.toggleVSync = () => {
    if (gameInstance) {
        gameInstance.enableVSync = !gameInstance.enableVSync;
        gameInstance.saveSettings();
        console.log(`🔄 VSync ${gameInstance.enableVSync ? 'enabled' : 'disabled'}`);
    }
};

window.getPerformanceInfo = () => {
    if (gameInstance) {
        return {
            currentFPS: gameInstance.currentFPS,
            targetFPS: gameInstance.targetFPS,
            enableVSync: gameInstance.enableVSync,
            performanceMode: gameInstance.enablePerformanceMode,
            frameCount: gameInstance.frameCount
        };
    }
    return null;
};
