<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>⚙️ إعدادات اللعبة - محسن</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            min-height: 100vh;
            color: white;
            padding: 10px;
            font-size: 13px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 15px;
            background: rgba(0, 0, 0, 0.2);
            padding: 12px;
            border-radius: 8px;
        }

        .header h1 {
            font-size: 1.6em;
            margin-bottom: 3px;
        }

        .header p {
            font-size: 12px;
            opacity: 0.8;
        }

        .tabs {
            display: flex;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            margin-bottom: 15px;
            overflow: hidden;
        }

        .tab {
            flex: 1;
            padding: 10px 6px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 12px;
            border: none;
            background: transparent;
            color: white;
        }

        .tab.active {
            background: #3498db;
        }

        .tab:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .tab-content {
            display: none;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 15px;
        }

        .tab-content.active {
            display: block;
        }

        .section {
            margin-bottom: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 6px;
            padding: 12px;
        }

        .section-title {
            font-size: 14px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #3498db;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding-bottom: 5px;
        }

        .setting-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 10px;
        }

        .setting-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 6px 10px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 4px;
            font-size: 12px;
        }

        .setting-label {
            font-weight: 500;
            min-width: 100px;
        }

        .setting-control {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        input, select {
            background: rgba(0, 0, 0, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            padding: 3px 6px;
            color: white;
            font-size: 11px;
        }

        input[type="checkbox"] {
            transform: scale(1.1);
        }

        input[type="range"] {
            width: 70px;
        }

        input[type="number"] {
            width: 60px;
        }

        select {
            min-width: 80px;
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 11px;
            margin: 3px;
            transition: all 0.3s ease;
        }

        .btn-primary { background: #3498db; color: white; }
        .btn-success { background: #27ae60; color: white; }
        .btn-danger { background: #e74c3c; color: white; }
        .btn-warning { background: #f39c12; color: white; }

        .btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
        }

        .actions {
            text-align: center;
            margin-top: 15px;
            padding: 10px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 6px;
        }

        .volume-display {
            min-width: 35px;
            text-align: center;
            font-size: 10px;
            color: #3498db;
        }

        .assignment-item {
            background: rgba(255, 255, 255, 0.08);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            margin-bottom: 8px;
            padding: 8px;
        }

        .assignment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        }

        .assignment-fields {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 8px;
        }

        .field-group {
            display: flex;
            flex-direction: column;
            gap: 3px;
        }

        .field-group label {
            font-size: 10px;
            color: #bdc3c7;
        }

        .field-group input,
        .field-group select {
            font-size: 10px;
            padding: 2px 4px;
        }

        .preview-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            margin-top: 10px;
        }

        .preview-btn {
            padding: 4px 8px;
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 3px;
            color: white;
            cursor: pointer;
            font-size: 10px;
            transition: all 0.3s ease;
        }

        .preview-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        .sound-upload-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 8px;
            margin-top: 10px;
        }

        .sound-upload-item {
            background: rgba(0, 0, 0, 0.3);
            padding: 8px;
            border-radius: 6px;
            border: 2px dashed rgba(255, 255, 255, 0.2);
            text-align: center;
            font-size: 10px;
        }

        .sound-upload-item.has-file {
            border-color: #27ae60;
            background: rgba(39, 174, 96, 0.1);
        }

        .sound-upload-item label {
            display: block;
            font-weight: bold;
            margin-bottom: 5px;
            color: #ecf0f1;
        }

        .sound-upload-item input[type="file"] {
            width: 100%;
            font-size: 9px;
            padding: 2px;
        }

        .remove-custom-btn {
            background: #e74c3c;
            color: white;
            border: none;
            padding: 2px 6px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 9px;
            margin-top: 3px;
            display: none;
        }

        .status-message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 10px 15px;
            border-radius: 6px;
            color: white;
            font-weight: bold;
            z-index: 1000;
            transform: translateX(300px);
            transition: transform 0.3s ease;
            font-size: 12px;
        }

        .status-message.show {
            transform: translateX(0);
        }

        .status-success { background: #27ae60; }
        .status-error { background: #e74c3c; }
        .status-info { background: #3498db; }

        @media (max-width: 768px) {
            .setting-grid {
                grid-template-columns: 1fr;
            }

            .assignment-fields {
                grid-template-columns: 1fr;
            }

            .sound-upload-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>⚙️ إعدادات اللعبة - النسخة المحسنة</h1>
            <p>تصميم مضغوط واحترافي مع جميع الوظائف</p>
        </div>

        <div class="tabs">
            <button class="tab active" onclick="showTab('events')">🎁 أحداث</button>
            <button class="tab" onclick="showTab('audio')">🔊 صوت</button>
            <button class="tab" onclick="showTab('game')">🎮 لعبة</button>
            <button class="tab" onclick="showTab('visual')">🎨 مرئي</button>
            <button class="tab" onclick="showTab('advanced')">🔧 متقدم</button>
        </div>

        <!-- Events Tab -->
        <div id="events" class="tab-content active">
            <div class="section">
                <div class="section-title">🎁 نظام تعيينات الأحداث</div>
                <div class="setting-item">
                    <span class="setting-label">إدارة التعيينات</span>
                    <div class="setting-control">
                        <button class="btn btn-success" onclick="addNewAssignment()">➕ إضافة</button>
                        <button class="btn btn-info" onclick="loadDefaultAssignments()">📋 تحميل افتراضي</button>
                        <button class="btn btn-warning" onclick="mergeWithCurrentAssignments()">🔗 دمج افتراضي</button>
                        <button class="btn btn-danger" onclick="clearAllAssignments()">🗑️ مسح الكل</button>
                    </div>
                </div>
                <div class="setting-item">
                    <span class="setting-label">معلومات التعيينات</span>
                    <div class="setting-control">
                        <span id="assignmentsCount" style="font-size: 11px; color: #3498db;">0 تعيين</span>
                        <button class="btn btn-primary" onclick="exportAssignments()" style="font-size: 10px; padding: 3px 6px;">📤 تصدير</button>
                        <input type="file" id="importAssignments" accept=".json" onchange="importAssignments(this)" style="display: none;">
                        <button class="btn btn-success" onclick="document.getElementById('importAssignments').click()" style="font-size: 10px; padding: 3px 6px;">📥 استيراد</button>
                        <input type="file" id="importCompleteData" accept=".json" onchange="importCompleteData(this)" style="display: none;">
                        <button class="btn btn-warning" onclick="document.getElementById('importCompleteData').click()" style="font-size: 10px; padding: 3px 6px;">📥 استيراد شامل</button>
                        <button class="btn btn-secondary" onclick="debugCurrentData()" style="font-size: 10px; padding: 3px 6px;">🔍 تشخيص</button>
                    </div>
                </div>
                <div id="assignmentsList">
                    <!-- سيتم إضافة التعيينات هنا -->
                </div>
            </div>
        </div>

        <!-- Audio Tab -->
        <div id="audio" class="tab-content">
            <div class="section">
                <div class="section-title">🔊 إعدادات الصوت الأساسية</div>
                <div class="setting-grid">
                    <div class="setting-item">
                        <span class="setting-label">تفعيل الأصوات</span>
                        <div class="setting-control">
                            <input type="checkbox" id="soundEnabled" checked>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">تفعيل الموسيقى</span>
                        <div class="setting-control">
                            <input type="checkbox" id="musicEnabled" checked>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">مستوى الأصوات</span>
                        <div class="setting-control">
                            <input type="range" id="effectsVolume" min="0" max="1" step="0.1" value="0.7">
                            <span class="volume-display" id="effectsVolumeValue">70%</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">مستوى الموسيقى</span>
                        <div class="setting-control">
                            <input type="range" id="musicVolume" min="0" max="1" step="0.1" value="0.3">
                            <span class="volume-display" id="musicVolumeValue">30%</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">أصوات مرعبة</span>
                        <div class="setting-control">
                            <input type="checkbox" id="scaryEnabled" checked>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">أصوات واقعية</span>
                        <div class="setting-control">
                            <input type="checkbox" id="realisticEating" checked>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">أصوات المحيط</span>
                        <div class="setting-control">
                            <input type="checkbox" id="ambientSounds" checked>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">مستوى أصوات المحيط</span>
                        <div class="setting-control">
                            <input type="range" id="ambientVolume" min="0" max="1" step="0.1" value="0.3">
                            <span class="volume-display" id="ambientVolumeValue">30%</span>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <div class="section-title">🎵 معاينة الأصوات</div>
                <div class="preview-buttons">
                    <button class="preview-btn" onclick="previewSound('eat_small')">🍽️ أكل صغير</button>
                    <button class="preview-btn" onclick="previewSound('eat_large')">🍖 أكل كبير</button>
                    <button class="preview-btn" onclick="previewSound('shark_roar')">🦈 زئير قرش</button>
                    <button class="preview-btn" onclick="previewSound('whale_call')">🐋 نداء حوت</button>
                    <button class="preview-btn" onclick="previewSound('spawn')">✨ ظهور</button>
                    <button class="preview-btn" onclick="previewSound('ocean_waves')">🌊 أمواج</button>
                    <button class="preview-btn" onclick="previewSound('underwater_bubbles')">💧 فقاعات</button>
                    <button class="preview-btn" onclick="previewSound('deep_sea')">🌑 أعماق</button>
                    <button class="preview-btn" onclick="previewSound('whale_song')">🎵 أغنية حوت</button>
                </div>
            </div>

            <div class="section">
                <div class="section-title">🎵 رفع أصوات مخصصة</div>
                <div class="sound-upload-grid">
                    <div class="sound-upload-item">
                        <label>🍽️ أكل صغير</label>
                        <input type="file" id="upload_eat_small" accept="audio/*" onchange="uploadCustomSound('eat_small', this)">
                        <button class="remove-custom-btn" onclick="removeCustomSound('eat_small')">🗑️</button>
                    </div>
                    <div class="sound-upload-item">
                        <label>🍖 أكل كبير</label>
                        <input type="file" id="upload_eat_large" accept="audio/*" onchange="uploadCustomSound('eat_large', this)">
                        <button class="remove-custom-btn" onclick="removeCustomSound('eat_large')">🗑️</button>
                    </div>
                    <div class="sound-upload-item">
                        <label>🦈 زئير قرش</label>
                        <input type="file" id="upload_shark_roar" accept="audio/*" onchange="uploadCustomSound('shark_roar', this)">
                        <button class="remove-custom-btn" onclick="removeCustomSound('shark_roar')">🗑️</button>
                    </div>
                    <div class="sound-upload-item">
                        <label>🐋 نداء حوت</label>
                        <input type="file" id="upload_whale_call" accept="audio/*" onchange="uploadCustomSound('whale_call', this)">
                        <button class="remove-custom-btn" onclick="removeCustomSound('whale_call')">🗑️</button>
                    </div>
                    <div class="sound-upload-item">
                        <label>✨ ظهور</label>
                        <input type="file" id="upload_spawn" accept="audio/*" onchange="uploadCustomSound('spawn', this)">
                        <button class="remove-custom-btn" onclick="removeCustomSound('spawn')">🗑️</button>
                    </div>
                    <div class="sound-upload-item">
                        <label>🌊 أمواج البحر</label>
                        <input type="file" id="upload_ocean_waves" accept="audio/*" onchange="uploadCustomSound('ocean_waves', this)">
                        <button class="remove-custom-btn" onclick="removeCustomSound('ocean_waves')">🗑️</button>
                    </div>
                    <div class="sound-upload-item">
                        <label>💧 فقاعات</label>
                        <input type="file" id="upload_underwater_bubbles" accept="audio/*" onchange="uploadCustomSound('underwater_bubbles', this)">
                        <button class="remove-custom-btn" onclick="removeCustomSound('underwater_bubbles')">🗑️</button>
                    </div>
                    <div class="sound-upload-item">
                        <label>🌑 أعماق البحر</label>
                        <input type="file" id="upload_deep_sea" accept="audio/*" onchange="uploadCustomSound('deep_sea', this)">
                        <button class="remove-custom-btn" onclick="removeCustomSound('deep_sea')">🗑️</button>
                    </div>
                    <div class="sound-upload-item">
                        <label>🎵 أغنية الحوت</label>
                        <input type="file" id="upload_whale_song" accept="audio/*" onchange="uploadCustomSound('whale_song', this)">
                        <button class="remove-custom-btn" onclick="removeCustomSound('whale_song')">🗑️</button>
                    </div>
                </div>
                <div style="margin-top: 10px; padding: 8px; background: rgba(231, 76, 60, 0.1); border-radius: 4px; font-size: 10px;">
                    <p><strong>📁 الصيغ المدعومة:</strong> MP3, WAV, OGG, M4A | <strong>📏 الحد الأقصى:</strong> 10MB</p>
                    <p><strong>🔇 ملاحظة:</strong> عند رفع أي صوت مخصص، تُغلق جميع الأصوات الافتراضية</p>
                </div>
            </div>
        </div>

        <!-- Game Tab -->
        <div id="game" class="tab-content">
            <div class="section">
                <div class="section-title">🎮 سلوك اللعبة</div>
                <div class="setting-grid">
                    <div class="setting-item">
                        <span class="setting-label">سرعة نمو الأسماك</span>
                        <div class="setting-control">
                            <select id="growthSpeed">
                                <option value="slow">🐌 بطيء</option>
                                <option value="normal" selected>⚖️ عادي</option>
                                <option value="fast">🚀 سريع</option>
                            </select>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">عدوانية الأسماك</span>
                        <div class="setting-control">
                            <input type="range" id="fishAggression" min="0.1" max="2" step="0.1" value="1">
                            <span class="volume-display" id="aggressionValue">100%</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">كثافة الأسماك القصوى</span>
                        <div class="setting-control">
                            <input type="number" id="maxFish" min="50" max="300" value="150">
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">تفعيل المعارك التلقائية</span>
                        <div class="setting-control">
                            <input type="checkbox" id="autoBattles" checked>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <div class="section-title">🗑️ حذف الأسماك التلقائي</div>
                <div class="setting-grid">
                    <div class="setting-item">
                        <span class="setting-label">تفعيل الحذف التلقائي</span>
                        <div class="setting-control">
                            <input type="checkbox" id="autoDeleteEnabled">
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">مدة بقاء السمكة (ثانية)</span>
                        <div class="setting-control">
                            <input type="number" id="fishLifetime" min="10" max="300" value="60">
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">الحد الأقصى للأسماك</span>
                        <div class="setting-control">
                            <input type="number" id="maxFishLimit" min="10" max="500" value="100">
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <div class="section-title">👁️ إخفاء/إظهار القوائم</div>
                <div class="setting-grid">
                    <div class="setting-item">
                        <span class="setting-label">قائمة اللاعبين النشطين</span>
                        <div class="setting-control">
                            <input type="checkbox" id="showActivePlayersToggle">
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">إحصائيات اللعبة</span>
                        <div class="setting-control">
                            <input type="checkbox" id="showGameStatsToggle">
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">قائمة التحكم</span>
                        <div class="setting-control">
                            <input type="checkbox" id="showControlsToggle">
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">قائمة الإنجازات</span>
                        <div class="setting-control">
                            <input type="checkbox" id="showAchievementsToggle">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Visual Tab -->
        <div id="visual" class="tab-content">
            <div class="section">
                <div class="section-title">🎨 الإعدادات البصرية</div>
                <div class="setting-grid">
                    <div class="setting-item">
                        <span class="setting-label">جودة الرسوميات</span>
                        <div class="setting-control">
                            <select id="graphicsQuality">
                                <option value="low">📱 منخفضة</option>
                                <option value="medium" selected>💻 متوسطة</option>
                                <option value="high">🖥️ عالية</option>
                                <option value="ultra">🎮 فائقة</option>
                            </select>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">عدد الجزيئات</span>
                        <div class="setting-control">
                            <input type="range" id="particleCount" min="0.1" max="2" step="0.1" value="1">
                            <span class="volume-display" id="particleValue">100%</span>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">تأثيرات التوهج</span>
                        <div class="setting-control">
                            <input type="checkbox" id="glowEffects" checked>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">عرض أسماء اللاعبين</span>
                        <div class="setting-control">
                            <input type="checkbox" id="showPlayerNames" checked>
                        </div>
                    </div>
                </div>
            </div>

            <div class="section">
                <div class="section-title">🔍 إعدادات الكاميرا</div>
                <div class="setting-grid">
                    <div class="setting-item">
                        <span class="setting-label">مستوى التكبير الافتراضي</span>
                        <div class="setting-control">
                            <select id="defaultZoom">
                                <option value="0.5">🔍--- بعيد جداً (0.5x)</option>
                                <option value="0.8">🔍-- بعيد (0.8x)</option>
                                <option value="1.0">🔍- طبيعي (1.0x)</option>
                                <option value="1.2">🔍 قريب قليلاً (1.2x)</option>
                                <option value="1.5">🔍+ قريب (1.5x)</option>
                                <option value="2.0" selected>🔍++ عادي (2.0x)</option>
                                <option value="2.5">🔍+++ أكثر (2.5x)</option>
                                <option value="3.0">🔍++++ عالي (3.0x)</option>
                                <option value="4.0">🔍+++++ أقصى (4.0x)</option>
                            </select>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">متابعة الأسماك الكبيرة</span>
                        <div class="setting-control">
                            <input type="checkbox" id="autoFollowLargeFish" checked>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">حد المتابعة التلقائية</span>
                        <div class="setting-control">
                            <select id="autoFollowThreshold">
                                <option value="0">🐠 جميع الأحجام (0)</option>
                                <option value="5">🐟 صغيرة+ (5)</option>
                                <option value="10">🐡 متوسطة صغيرة+ (10)</option>
                                <option value="20" selected>🦈 متوسطة+ (20)</option>
                                <option value="30">🐋 كبيرة+ (30)</option>
                                <option value="50">🦑 كبيرة جداً+ (50)</option>
                                <option value="80">🐙 عملاقة+ (80)</option>
                                <option value="100">🦕 ضخمة+ (100)</option>
                            </select>
                            <small style="display: block; color: #666; margin-top: 5px;">
                                الحد الأدنى لحجم المخلوق للمتابعة التلقائية
                            </small>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">سرعة حركة الكاميرا</span>
                        <div class="setting-control">
                            <select id="cameraSpeed">
                                <option value="0.02">🐌 بطيئة جداً</option>
                                <option value="0.05">🚶 بطيئة</option>
                                <option value="0.08" selected>⚖️ عادية</option>
                                <option value="0.12">🏃 سريعة</option>
                                <option value="0.15">🚀 سريعة جداً</option>
                            </select>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">تحكم عجلة الماوس</span>
                        <div class="setting-control">
                            <input type="checkbox" id="mouseWheelZoom" checked>
                        </div>
                    </div>
                </div>
                <div style="margin-top: 10px;">
                    <div class="setting-item">
                        <span class="setting-label">تحكم سريع في الكاميرا</span>
                        <div class="setting-control" style="flex-wrap: wrap; gap: 2px;">
                            <button class="btn btn-primary" style="font-size: 7px; padding: 1px 2px;" onclick="if(window.game) window.game.setZoom(0.5)">🔍--- بعيد جداً</button>
                            <button class="btn btn-primary" style="font-size: 7px; padding: 1px 2px;" onclick="if(window.game) window.game.setZoom(0.8)">🔍-- بعيد</button>
                            <button class="btn btn-primary" style="font-size: 7px; padding: 1px 2px;" onclick="if(window.game) window.game.setZoom(1.0)">🔍- طبيعي</button>
                            <button class="btn btn-primary" style="font-size: 7px; padding: 1px 2px;" onclick="if(window.game) window.game.setZoom(1.5)">🔍+ قريب</button>
                            <button class="btn btn-primary" style="font-size: 7px; padding: 1px 2px;" onclick="if(window.game) window.game.setZoom(2.0)">🔍++ عادي</button>
                            <button class="btn btn-primary" style="font-size: 7px; padding: 1px 2px;" onclick="if(window.game) window.game.setZoom(3.0)">🔍+++ عالي</button>
                            <button class="btn btn-success" style="font-size: 7px; padding: 1px 2px;" onclick="if(window.game) window.game.followLargestFish()">👁️ متابعة</button>
                            <button class="btn btn-danger" style="font-size: 7px; padding: 1px 2px;" onclick="if(window.game) window.game.stopFollowing()">🎯 إيقاف</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Advanced Tab -->
        <div id="advanced" class="tab-content">
            <div class="section">
                <div class="section-title">🔧 إعدادات متقدمة</div>
                <div class="setting-grid">
                    <div class="setting-item">
                        <span class="setting-label">معدل الإطارات المستهدف</span>
                        <div class="setting-control">
                            <select id="targetFPS">
                                <option value="30">30 FPS</option>
                                <option value="60" selected>60 FPS</option>
                                <option value="120">120 FPS</option>
                            </select>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">دقة النافذة</span>
                        <div class="setting-control">
                            <select id="gameResolution" onchange="updateResolutionPreview()">
                                <option value="800x600">📱 صغيرة (800x600)</option>
                                <option value="1024x768">💻 متوسطة (1024x768)</option>
                                <option value="1280x720">🖥️ HD (1280x720)</option>
                                <option value="1366x768">📺 عادية (1366x768)</option>
                                <option value="1600x900">🖥️ كبيرة (1600x900)</option>
                                <option value="1920x1080" selected>🎮 Full HD (1920x1080)</option>
                                <option value="2560x1440">🖥️ 2K (2560x1440)</option>
                                <option value="3840x2160">📺 4K (3840x2160)</option>
                                <option value="auto">🔄 تلقائي (حسب الشاشة)</option>
                            </select>
                            <small id="resolutionPreview" style="display: block; color: #666; margin-top: 5px;">
                                النافذة ستفتح بدقة: 1920x1080
                            </small>
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">تحسين الأداء</span>
                        <div class="setting-control">
                            <input type="checkbox" id="performanceMode">
                        </div>
                    </div>
                    <div class="setting-item">
                        <span class="setting-label">حفظ تلقائي للإعدادات</span>
                        <div class="setting-control">
                            <input type="checkbox" id="autoSave" checked>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="actions">
            <button class="btn btn-primary" onclick="openGame()">🎮 فتح اللعبة</button>
            <button class="btn btn-success" onclick="openGameCleanWindow()" style="background: linear-gradient(45deg, #00b894, #00cec9);">🖥️ فتح نافذة منفصلة بالدقة المحددة</button>
            <button class="btn btn-success" onclick="saveAllSettings()">💾 حفظ جميع الإعدادات</button>
            <button class="btn btn-warning" onclick="loadAllSettings()">📂 تحميل الإعدادات</button>

            <button class="btn btn-primary" onclick="generateTikTokLink()" style="background: linear-gradient(45deg, #ff0050, #00f2ea);">📱 إنشاء رابط TikTok Live</button>

            <button class="btn btn-info" onclick="runSystemTest()">🔍 اختبار النظام</button>
            <button class="btn btn-danger" onclick="resetAllSettings()">🔄 إعادة تعيين شاملة</button>
        </div>
    </div>

    <!-- Status Message -->
    <div id="statusMessage" class="status-message"></div>

    <script>
        // متغيرات عامة
        let assignments = [];
        let audioContext;
        let soundBuffers = new Map();
        let customSounds = new Map();

        // تبديل التبويبات
        function showTab(tabName) {
            document.querySelectorAll('.tab-content').forEach(tab => {
                tab.classList.remove('active');
            });

            document.querySelectorAll('.tab').forEach(btn => {
                btn.classList.remove('active');
            });

            document.getElementById(tabName).classList.add('active');
            event.target.classList.add('active');
        }

        // عرض رسالة الحالة
        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('statusMessage');
            statusEl.textContent = message;
            statusEl.className = `status-message status-${type} show`;

            setTimeout(() => {
                statusEl.classList.remove('show');
            }, 3000);
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            initAudioSystem();
            initializeAssignments();
            setupEventListeners();
            loadAllSettings();
            loadCustomSoundsInfo();
            updateResolutionPreview(); // تحديث معاينة الدقة
            showStatus('تم تحميل الإعدادات بنجاح', 'success');
        });

        // تهيئة نظام الصوت
        async function initAudioSystem() {
            try {
                audioContext = new (window.AudioContext || window.webkitAudioContext)();
                await generateSounds();
                console.log('🔊 تم تهيئة نظام الصوت للمعاينة');
            } catch (error) {
                console.warn('⚠️ فشل في تهيئة نظام الصوت:', error);
            }
        }

        // إعداد مستمعي الأحداث
        function setupEventListeners() {
            // تحديث قيم المنزلقات
            document.getElementById('effectsVolume').addEventListener('input', function() {
                document.getElementById('effectsVolumeValue').textContent = Math.round(this.value * 100) + '%';
            });

            document.getElementById('musicVolume').addEventListener('input', function() {
                document.getElementById('musicVolumeValue').textContent = Math.round(this.value * 100) + '%';
            });

            document.getElementById('ambientVolume').addEventListener('input', function() {
                document.getElementById('ambientVolumeValue').textContent = Math.round(this.value * 100) + '%';
            });

            document.getElementById('fishAggression').addEventListener('input', function() {
                document.getElementById('aggressionValue').textContent = Math.round(this.value * 100) + '%';
            });

            document.getElementById('particleCount').addEventListener('input', function() {
                document.getElementById('particleValue').textContent = Math.round(this.value * 100) + '%';
            });

            document.getElementById('autoFollowThreshold').addEventListener('input', function() {
                document.getElementById('autoFollowValue').textContent = this.value;
            });

            // حفظ تلقائي عند التغيير
            const inputs = document.querySelectorAll('input, select');
            inputs.forEach(input => {
                input.addEventListener('change', function() {
                    if (document.getElementById('autoSave').checked) {
                        // حفظ في كلا المفتاحين مباشرة
                        const settings = getAllSettings();
                        localStorage.setItem('fishGameSettings', JSON.stringify(settings));
                        localStorage.setItem('fishEatFishSettings', JSON.stringify(settings));
                        saveAssignments();
                    }
                });
            });
        }

        // فتح اللعبة
        function openGame() {
            // اكتشاف المسار الصحيح تلقائياً
            const currentPath = window.location.pathname;
            const gamePath = currentPath.replace('fish-settings.html', 'fish-eat-fish.html');
            window.open(gamePath, '_blank');
        }

        // فتح اللعبة في نافذة منفصلة حرة بإعدادات الدقة المحددة
        function openGameCleanWindow() {
            try {
                // حفظ الإعدادات أولاً
                saveAllSettings(false);

                // الحصول على إعدادات الدقة
                const resolution = document.getElementById('gameResolution').value || '1920x1080';
                let windowWidth, windowHeight;

                if (resolution === 'auto') {
                    // استخدام دقة الشاشة الحالية
                    windowWidth = Math.min(window.screen.availWidth, window.screen.width);
                    windowHeight = Math.min(window.screen.availHeight, window.screen.height);
                } else {
                    // استخدام الدقة المحددة
                    const [width, height] = resolution.split('x').map(Number);
                    windowWidth = width;
                    windowHeight = height;
                }

                // اكتشاف المسار الصحيح
                const currentPath = window.location.pathname;
                const gamePath = currentPath.replace('fish-settings.html', 'fish-eat-fish.html');

                // إعدادات النافذة المنفصلة الحرة
                const windowFeatures = [
                    `width=${windowWidth}`,
                    `height=${windowHeight}`,
                    'left=50',
                    'top=50',
                    'resizable=yes',
                    'scrollbars=no',
                    'toolbar=no',
                    'menubar=no',
                    'location=no',
                    'status=no'
                ].join(',');

                // فتح النافذة المنفصلة
                const gameWindow = window.open(gamePath, 'FishGameWindow', windowFeatures);

                if (gameWindow) {
                    gameWindow.focus();
                    showStatus(`✅ تم فتح اللعبة في نافذة منفصلة بدقة ${windowWidth}x${windowHeight}!`, 'success');

                    // رسالة توضيحية
                    setTimeout(() => {
                        alert(`🎮 تم فتح اللعبة في نافذة منفصلة!\n\n🖥️ الدقة: ${windowWidth}x${windowHeight}\n\n💡 للحصول على عرض نظيف:\n- اضغط F11 للشاشة الكاملة\n- أو استخدم مشاركة الشاشة في TikTok Live\n- النافذة قابلة لتغيير الحجم حسب الحاجة`);
                    }, 1000);

                } else {
                    showStatus('❌ فشل في فتح النافذة - تحقق من إعدادات المتصفح', 'error');
                }

            } catch (error) {
                console.error('خطأ في فتح النافذة:', error);
                showStatus('❌ فشل في فتح النافذة', 'error');
            }
        }

        // تحديث معاينة الدقة
        function updateResolutionPreview() {
            const resolution = document.getElementById('gameResolution').value;
            const previewElement = document.getElementById('resolutionPreview');

            if (resolution === 'auto') {
                const screenWidth = window.screen.availWidth;
                const screenHeight = window.screen.availHeight;
                previewElement.textContent = `النافذة ستفتح بدقة: ${screenWidth}x${screenHeight} (تلقائي)`;
            } else {
                previewElement.textContent = `النافذة ستفتح بدقة: ${resolution}`;
            }
        }

        // تم حذف وظيفة fixSettingsForTikTok لأن generateTikTokLink تقوم بنفس المهمة بشكل أفضل

        // إنشاء رابط TikTok Live مع الإعدادات مضمنة
        async function generateTikTokLink() {
            showStatus('📱 إنشاء رابط TikTok Live...', 'info');

            try {
                // جمع الإعدادات الحالية
                const settings = getAllSettings();

                // حفظ الإعدادات في localStorage أيضاً (للنسخ الاحتياطية)
                localStorage.setItem('fishGameSettings', JSON.stringify(settings));
                localStorage.setItem('fishEatFishSettings', JSON.stringify(settings));
                saveAssignments();

                // جمع التعيينات والأصوات المخصصة
                const assignments = JSON.parse(localStorage.getItem('fishGameAssignments') || '[]');

                // جمع الأصوات المخصصة وتحويلها إلى Base64
                const customSounds = {};
                let soundsProcessed = 0;
                const maxSounds = 10; // حد أقصى 10 أصوات مخصصة

                showStatus('🔊 معالجة الأصوات المخصصة...', 'info');

                // معالجة الأصوات المخصصة بشكل غير متزامن
                const processCustomSounds = async () => {
                    for (const assignment of assignments) {
                        if (assignment.soundFile && assignment.soundFile !== 'default' && soundsProcessed < maxSounds) {
                            try {
                                // البحث عن الملف الصوتي في localStorage
                                const soundKey = 'customSound_' + assignment.soundFile;
                                const soundData = localStorage.getItem(soundKey);

                                if (soundData) {
                                    // إذا كان الصوت محفوظ كـ Base64، استخدمه مباشرة
                                    customSounds[assignment.soundFile] = soundData;
                                    soundsProcessed++;
                                    console.log('✅ تم تضمين صوت مخصص:', assignment.soundFile);
                                }
                            } catch (error) {
                                console.warn('⚠️ فشل في معالجة الصوت:', assignment.soundFile, error);
                            }
                        }
                    }

                    console.log('🔊 تم معالجة', soundsProcessed, 'صوت مخصص');
                    return customSounds;
                };

                // تنفيذ معالجة الأصوات
                const processedSounds = await processCustomSounds();

                // ضغط الإعدادات والتعيينات والأصوات المهمة فقط لتقليل طول الرابط
                const compactSettings = {
                    zoom: settings.defaultZoom || 1.0,
                    follow: settings.autoFollowLargeFish !== false,
                    sound: settings.soundEnabled !== false,
                    music: settings.musicEnabled !== false,
                    vol: Math.round((settings.effectsVolume || 0.7) * 10),
                    ui: {
                        players: settings.showActivePlayers === true,
                        stats: settings.showGameStats === true,
                        controls: settings.showControls === true
                    },
                    // إضافة التعيينات المضغوطة
                    assignments: assignments.map(a => ({
                        gift: a.giftName,
                        type: a.fishType,
                        size: a.fishSize,
                        sound: a.soundFile || 'default'
                    })).slice(0, 20), // أول 20 تعيين فقط لتوفير المساحة

                    // إضافة الأصوات المخصصة
                    sounds: processedSounds
                };

                // تشفير مضغوط
                const compressedData = btoa(JSON.stringify(compactSettings));

                // إنشاء رابط مع الإعدادات المضغوطة - اكتشاف المسار تلقائياً
                const currentPath = window.location.pathname;
                const gamePath = currentPath.replace('fish-settings.html', 'fish-eat-fish.html');
                const baseUrl = window.location.origin + gamePath;
                const shortLink = `${baseUrl}?s=${compressedData}`;

                // نسخ الرابط إلى الحافظة
                navigator.clipboard.writeText(shortLink).then(() => {
                    showStatus('✅ تم نسخ رابط TikTok Live إلى الحافظة!', 'success');

                    // عرض الرابط في نافذة منبثقة
                    const message = [
                        '📱 تم إنشاء رابط TikTok Live بنجاح!',
                        '',
                        '✅ الرابط قصير ومقبول في TikTok Live',
                        '✅ الإعدادات مضمنة في الرابط نفسه',
                        '✅ سيعمل في TikTok Live بإعداداتك المخصصة',
                        '✅ لا يحتاج localStorage - يعمل في أي بيئة',
                        '',
                        '🎯 الرابط المضغوط:',
                        shortLink,
                        '',
                        '📋 الصق الرابط في TikTok Live الآن!',
                        '',
                        '🔧 الإعدادات المضمنة:',
                        `   - التكبير: ${compactSettings.zoom}x`,
                        `   - المتابعة التلقائية: ${compactSettings.follow ? 'مفعلة' : 'معطلة'}`,
                        `   - الصوت: ${compactSettings.sound ? 'مفعل' : 'معطل'}`,
                        `   - الموسيقى: ${compactSettings.music ? 'مفعلة' : 'معطلة'}`,
                        `   - التعيينات: ${compactSettings.assignments.length} تعيين مضمن`,
                        `   - الأصوات المخصصة: ${Object.keys(compactSettings.sounds).length} صوت مضمن`
                    ].join('\n');

                    alert(message);

                }).catch(() => {
                    // إذا فشل النسخ التلقائي، اعرض الرابط للنسخ اليدوي
                    const message = [
                        '📱 تم إنشاء رابط TikTok Live!',
                        '',
                        '📋 انسخ الرابط التالي إلى TikTok Live:',
                        '',
                        shortLink,
                        '',
                        '💡 الرابط قصير ومقبول في TikTok Live'
                    ].join('\n');

                    alert(message);
                    showStatus('📋 انسخ الرابط من النافذة المنبثقة', 'warning');
                });

            } catch (error) {
                console.error('خطأ في إنشاء رابط TikTok Live:', error);
                showStatus('❌ فشل في إنشاء رابط TikTok Live: ' + error.message, 'error');
            }
        }

        // تم حذف وظيفة updateGameDefaults لأن generateTikTokLink تحل المشكلة بشكل أفضل

        // وظيفة لتحويل ملف صوتي إلى Base64 وحفظه
        function saveAudioAsBase64(file, soundName) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    try {
                        const base64Data = e.target.result;
                        const soundKey = 'customSound_' + soundName;
                        localStorage.setItem(soundKey, base64Data);
                        console.log('✅ تم حفظ الصوت كـ Base64:', soundName);
                        resolve(base64Data);
                    } catch (error) {
                        console.error('❌ فشل في حفظ الصوت:', error);
                        reject(error);
                    }
                };
                reader.onerror = reject;
                reader.readAsDataURL(file);
            });
        }

        // حفظ جميع الإعدادات
        function saveAllSettings(showMessage = true) {
            const settings = getAllSettings();

            // حفظ في كلا المفتاحين للتوافق
            localStorage.setItem('fishGameSettings', JSON.stringify(settings));
            localStorage.setItem('fishEatFishSettings', JSON.stringify(settings));

            saveAssignments();

            // إشعار اللعبة بالتحديث
            localStorage.setItem('fishGameSoundUpdate', Date.now().toString());

            // إرسال رسالة للنوافذ الأخرى
            if (window.postMessage) {
                window.postMessage({ type: 'settingsUpdate', settings: settings }, '*');
            }

            if (showMessage) {
                showStatus('تم حفظ جميع الإعدادات بنجاح!', 'success');
            }
        }

        // تحميل جميع الإعدادات
        function loadAllSettings() {
            // البحث في كلا المفتاحين
            let savedSettings = localStorage.getItem('fishGameSettings');
            if (!savedSettings) {
                savedSettings = localStorage.getItem('fishEatFishSettings');
            }

            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                applySettings(settings);

                // تأكيد الحفظ في كلا المفتاحين
                localStorage.setItem('fishGameSettings', savedSettings);
                localStorage.setItem('fishEatFishSettings', savedSettings);
            }
            loadAssignments();
        }

        // إعادة تعيين جميع الإعدادات
        function resetAllSettings() {
            if (confirm('هل أنت متأكد من إعادة تعيين جميع الإعدادات؟ سيتم حذف جميع التعيينات والأصوات المخصصة!')) {
                // حذف كلا مفتاحي الإعدادات
                localStorage.removeItem('fishGameSettings');
                localStorage.removeItem('fishEatFishSettings');
                localStorage.removeItem('fishGameAssignments');

                // حذف الأصوات المخصصة
                for (let i = localStorage.length - 1; i >= 0; i--) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('fishGameCustomSound_')) {
                        localStorage.removeItem(key);
                    }
                }
                localStorage.removeItem('fishGameCustomSounds');

                location.reload();
            }
        }

        // جمع جميع الإعدادات
        function getAllSettings() {
            return {
                // إعدادات الصوت
                soundEnabled: document.getElementById('soundEnabled').checked,
                musicEnabled: document.getElementById('musicEnabled').checked,
                effectsVolume: parseFloat(document.getElementById('effectsVolume').value),
                musicVolume: parseFloat(document.getElementById('musicVolume').value),
                ambientSounds: document.getElementById('ambientSounds').checked,
                ambientVolume: parseFloat(document.getElementById('ambientVolume').value),
                scaryEnabled: document.getElementById('scaryEnabled').checked,
                realisticEating: document.getElementById('realisticEating').checked,

                // سلوك اللعبة
                growthSpeed: document.getElementById('growthSpeed').value,
                fishAggression: parseFloat(document.getElementById('fishAggression').value),
                maxFish: parseInt(document.getElementById('maxFish').value),
                autoBattles: document.getElementById('autoBattles').checked,

                // حذف الأسماك التلقائي
                autoDeleteEnabled: document.getElementById('autoDeleteEnabled').checked,
                fishLifetime: parseInt(document.getElementById('fishLifetime').value),
                maxFishLimit: parseInt(document.getElementById('maxFishLimit').value),

                // إخفاء/إظهار القوائم
                showActivePlayers: document.getElementById('showActivePlayersToggle').checked,
                showGameStats: document.getElementById('showGameStatsToggle').checked,
                showControls: document.getElementById('showControlsToggle').checked,
                showAchievements: document.getElementById('showAchievementsToggle').checked,

                // الإعدادات البصرية
                graphicsQuality: document.getElementById('graphicsQuality').value,
                particleCount: parseFloat(document.getElementById('particleCount').value),
                glowEffects: document.getElementById('glowEffects').checked,
                showPlayerNames: document.getElementById('showPlayerNames').checked,

                // إعدادات الكاميرا
                defaultZoom: parseFloat(document.getElementById('defaultZoom').value),
                autoFollowLargeFish: document.getElementById('autoFollowLargeFish').checked,
                autoFollowThreshold: parseInt(document.getElementById('autoFollowThreshold').value),
                cameraSpeed: parseFloat(document.getElementById('cameraSpeed').value),
                mouseWheelZoom: document.getElementById('mouseWheelZoom').checked,

                // إعدادات متقدمة
                targetFPS: document.getElementById('targetFPS').value,
                gameResolution: document.getElementById('gameResolution').value,
                performanceMode: document.getElementById('performanceMode').checked,
                autoSave: document.getElementById('autoSave').checked
            };
        }

        // تطبيق الإعدادات على الواجهة
        function applySettings(settings) {
            // إعدادات الصوت
            document.getElementById('soundEnabled').checked = settings.soundEnabled !== false;
            document.getElementById('musicEnabled').checked = settings.musicEnabled !== false;
            document.getElementById('effectsVolume').value = settings.effectsVolume || 0.7;
            document.getElementById('musicVolume').value = settings.musicVolume || 0.3;
            document.getElementById('ambientSounds').checked = settings.ambientSounds !== false;
            document.getElementById('ambientVolume').value = settings.ambientVolume || 0.3;
            document.getElementById('scaryEnabled').checked = settings.scaryEnabled !== false;
            document.getElementById('realisticEating').checked = settings.realisticEating !== false;

            // سلوك اللعبة
            document.getElementById('growthSpeed').value = settings.growthSpeed || 'normal';
            document.getElementById('fishAggression').value = settings.fishAggression || 1;
            document.getElementById('maxFish').value = settings.maxFish || 150;
            document.getElementById('autoBattles').checked = settings.autoBattles !== false;

            // حذف الأسماك التلقائي
            document.getElementById('autoDeleteEnabled').checked = settings.autoDeleteEnabled === true;
            document.getElementById('fishLifetime').value = settings.fishLifetime || 60;
            document.getElementById('maxFishLimit').value = settings.maxFishLimit || 100;

            // إخفاء/إظهار القوائم
            document.getElementById('showActivePlayersToggle').checked = settings.showActivePlayers === true;
            document.getElementById('showGameStatsToggle').checked = settings.showGameStats === true;
            document.getElementById('showControlsToggle').checked = settings.showControls === true;
            document.getElementById('showAchievementsToggle').checked = settings.showAchievements === true;

            // الإعدادات البصرية
            document.getElementById('graphicsQuality').value = settings.graphicsQuality || 'medium';
            document.getElementById('particleCount').value = settings.particleCount || 1;
            document.getElementById('glowEffects').checked = settings.glowEffects !== false;
            document.getElementById('showPlayerNames').checked = settings.showPlayerNames !== false;

            // إعدادات الكاميرا
            document.getElementById('defaultZoom').value = settings.defaultZoom || 2.0;
            document.getElementById('autoFollowLargeFish').checked = settings.autoFollowLargeFish !== false;
            document.getElementById('autoFollowThreshold').value = settings.autoFollowThreshold || 20;
            document.getElementById('cameraSpeed').value = settings.cameraSpeed || 0.08;
            document.getElementById('mouseWheelZoom').checked = settings.mouseWheelZoom !== false;

            // إعدادات متقدمة
            document.getElementById('targetFPS').value = settings.targetFPS || '60';
            document.getElementById('gameResolution').value = settings.gameResolution || '1920x1080';
            document.getElementById('performanceMode').checked = settings.performanceMode || false;
            document.getElementById('autoSave').checked = settings.autoSave !== false;

            // تحديث عرض القيم
            document.getElementById('effectsVolumeValue').textContent = Math.round((settings.effectsVolume || 0.7) * 100) + '%';
            document.getElementById('musicVolumeValue').textContent = Math.round((settings.musicVolume || 0.3) * 100) + '%';
            document.getElementById('ambientVolumeValue').textContent = Math.round((settings.ambientVolume || 0.3) * 100) + '%';
            document.getElementById('aggressionValue').textContent = Math.round((settings.fishAggression || 1) * 100) + '%';
            document.getElementById('particleValue').textContent = Math.round((settings.particleCount || 1) * 100) + '%';
            document.getElementById('autoFollowValue').textContent = settings.autoFollowThreshold || 80;
        }

        // === وظائف التعيينات ===

        // تهيئة التعيينات
        function initializeAssignments() {
            const savedAssignments = loadAssignments();
            if (savedAssignments && savedAssignments.length > 0) {
                assignments = savedAssignments;
                console.log('📂 تم تحميل التعيينات المحفوظة:', assignments.length);
                showStatus(`📂 تم تحميل ${assignments.length} تعيين محفوظ`, 'info');
            } else {
                // بدء بقائمة فارغة بدلاً من التعيينات الافتراضية
                assignments = [];
                console.log('📝 بدء بقائمة تعيينات فارغة');
                showStatus('📝 لا توجد تعيينات محفوظة - ابدأ بإضافة تعيينات جديدة', 'info');
            }
            renderAssignments();
        }

        // دمج التعيينات الحالية مع الافتراضية
        function mergeWithCurrentAssignments() {
            const savedAssignments = loadAssignments();
            const defaultAssignments = getDefaultAssignments();

            if (savedAssignments && savedAssignments.length > 0) {
                // دمج التعيينات: الحالية أولاً، ثم الافتراضية الجديدة
                const existingIds = savedAssignments.map(a => a.id);
                const newDefaults = defaultAssignments.filter(a => !existingIds.includes(a.id));

                assignments = [...savedAssignments, ...newDefaults];
                console.log(`🔄 تم دمج ${savedAssignments.length} تعيين حالي مع ${newDefaults.length} تعيين افتراضي جديد`);
                showStatus(`🔄 تم دمج ${savedAssignments.length} تعيين حالي مع ${newDefaults.length} تعيين جديد`, 'success');
            } else {
                assignments = defaultAssignments;
                console.log('✨ تم إنشاء التعيينات الافتراضية');
                showStatus('✨ تم إنشاء التعيينات الافتراضية', 'success');
            }

            renderAssignments();
            saveAssignments();
        }

        // الحصول على التعيينات الافتراضية
        function getDefaultAssignments() {
            return [
                // الهدايا الأساسية
                { id: 'rose_1', giftName: 'Rose', eventType: 'gift', creatureType: 'random', minCount: 1, maxCount: 5, sizeMultiplier: 1, specialEffect: 'none', soundEffect: 'spawn' },
                { id: 'rose_5', giftName: 'Rose', eventType: 'gift', creatureType: 'salmon', minCount: 5, maxCount: 10, sizeMultiplier: 1.2, specialEffect: 'speed_boost', soundEffect: 'spawn' },
                { id: 'rose_10', giftName: 'Rose', eventType: 'gift', creatureType: 'shark', minCount: 10, maxCount: 999, sizeMultiplier: 1.5, specialEffect: 'wild_mode', soundEffect: 'shark_roar' },

                { id: 'diamond_1', giftName: 'Diamond', eventType: 'gift', creatureType: 'shark', minCount: 1, maxCount: 999, sizeMultiplier: 1.8, specialEffect: 'wild_mode', soundEffect: 'shark_roar' },
                { id: 'crown_1', giftName: 'Crown', eventType: 'gift', creatureType: 'whale', minCount: 1, maxCount: 999, sizeMultiplier: 2.2, specialEffect: 'size_boost', soundEffect: 'whale_call' },
                { id: 'lightning_1', giftName: 'Lightning', eventType: 'gift', creatureType: 'eel', minCount: 1, maxCount: 999, sizeMultiplier: 1.3, specialEffect: 'electric', soundEffect: 'spawn' },
                { id: 'butterfly_1', giftName: 'Butterfly', eventType: 'gift', creatureType: 'jellyfish', minCount: 1, maxCount: 999, sizeMultiplier: 1.1, specialEffect: 'poison', soundEffect: 'spawn' },
                { id: 'star_1', giftName: 'Star', eventType: 'gift', creatureType: 'starfish', minCount: 1, maxCount: 999, sizeMultiplier: 1.2, specialEffect: 'regeneration', soundEffect: 'spawn' },

                // الأحداث التفاعلية
                { id: 'join_1', giftName: 'Join', eventType: 'join', creatureType: 'normal', minCount: 1, maxCount: 1, sizeMultiplier: 0.9, specialEffect: 'welcome_glow', soundEffect: 'spawn' },
                { id: 'like_1', giftName: 'Like', eventType: 'like', creatureType: 'seahorse', minCount: 1, maxCount: 10, sizeMultiplier: 0.7, specialEffect: 'speed_boost', soundEffect: 'spawn' },
                { id: 'like_10', giftName: 'Like', eventType: 'like', creatureType: 'dolphin', minCount: 10, maxCount: 50, sizeMultiplier: 1.1, specialEffect: 'speed_boost', soundEffect: 'spawn' },
                { id: 'like_50', giftName: 'Like', eventType: 'like', creatureType: 'ray', minCount: 50, maxCount: 999, sizeMultiplier: 1.4, specialEffect: 'electric', soundEffect: 'spawn' },

                { id: 'follow_1', giftName: 'Follow', eventType: 'follow', creatureType: 'dolphin', minCount: 1, maxCount: 1, sizeMultiplier: 1.5, specialEffect: 'loyalty_boost', soundEffect: 'spawn' },
                { id: 'share_1', giftName: 'Share', eventType: 'share', creatureType: 'turtle', minCount: 1, maxCount: 1, sizeMultiplier: 1.7, specialEffect: 'shell_defense', soundEffect: 'spawn' },

                // تعيينات التعليقات
                { id: 'comment_fish', giftName: 'fish', eventType: 'comment', creatureType: 'random', minCount: 1, maxCount: 1, sizeMultiplier: 1, specialEffect: 'none', soundEffect: 'spawn' },
                { id: 'comment_shark', giftName: 'shark', eventType: 'comment', creatureType: 'shark', minCount: 1, maxCount: 1, sizeMultiplier: 1.3, specialEffect: 'wild_mode', soundEffect: 'shark_roar' },
                { id: 'comment_whale', giftName: 'whale', eventType: 'comment', creatureType: 'whale', minCount: 1, maxCount: 1, sizeMultiplier: 1.8, specialEffect: 'size_boost', soundEffect: 'whale_call' },
                { id: 'comment_battle', giftName: 'battle', eventType: 'comment', creatureType: 'random', minCount: 1, maxCount: 1, sizeMultiplier: 1.2, specialEffect: 'battle_mode', soundEffect: 'spawn' }
            ];
        }

        // تحميل التعيينات الافتراضية
        function loadDefaultAssignments() {
            if (confirm('هل تريد تحميل التعيينات الافتراضية؟\n\nسيتم استبدال جميع التعيينات الحالية.')) {
                assignments = getDefaultAssignments();
                renderAssignments();
                saveAssignments();
                showStatus('✨ تم تحميل التعيينات الافتراضية الموسعة', 'success');
            }
        }

        // مسح جميع التعيينات
        function clearAllAssignments() {
            if (confirm('هل تريد مسح جميع التعيينات؟\n\nلا يمكن التراجع عن هذا الإجراء.')) {
                assignments = [];
                renderAssignments();
                saveAssignments();
                showStatus('🗑️ تم مسح جميع التعيينات', 'info');
            }
        }

        // إضافة تعيين جديد
        function addNewAssignment() {
            const newAssignment = {
                id: 'custom_' + Date.now(),
                giftName: '',
                eventType: 'gift',
                creatureType: 'random',
                minCount: 1,
                maxCount: 999,
                sizeMultiplier: 1,
                specialEffect: 'none',
                soundEffect: 'spawn'
            };

            assignments.push(newAssignment);
            renderAssignments();
            saveAssignments();
            showStatus('✅ تم إضافة تعيين جديد', 'success');
        }

        // حذف تعيين
        function removeAssignment(button) {
            const assignmentItem = button.closest('.assignment-item');
            const index = Array.from(assignmentItem.parentNode.children).indexOf(assignmentItem);

            assignments.splice(index, 1);
            renderAssignments();
            saveAssignments();
            showStatus('🗑️ تم حذف التعيين', 'info');
        }

        // عرض التعيينات
        function renderAssignments() {
            const container = document.getElementById('assignmentsList');
            container.innerHTML = '';

            assignments.forEach((assignment, index) => {
                const assignmentElement = createAssignmentElement(assignment, index);
                container.appendChild(assignmentElement);
            });

            // تحديث عداد التعيينات
            updateAssignmentsCount();
        }

        // تحديث عداد التعيينات
        function updateAssignmentsCount() {
            const countElement = document.getElementById('assignmentsCount');
            if (countElement) {
                countElement.textContent = `${assignments.length} تعيين`;
            }
        }

        // مسح جميع التعيينات
        function clearAllAssignments() {
            if (confirm('هل أنت متأكد من حذف جميع التعيينات؟ هذا الإجراء لا يمكن التراجع عنه!')) {
                assignments = [];
                renderAssignments();
                saveAssignments();
                showStatus('🗑️ تم حذف جميع التعيينات', 'info');
            }
        }

        // تصدير التعيينات
        function exportAssignments() {
            try {
                const dataStr = JSON.stringify(assignments, null, 2);
                const dataBlob = new Blob([dataStr], {type: 'application/json'});

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `fish-game-assignments-${new Date().toISOString().split('T')[0]}.json`;
                link.click();

                showStatus('📤 تم تصدير التعيينات بنجاح', 'success');
            } catch (error) {
                showStatus('❌ فشل في تصدير التعيينات', 'error');
                console.error('خطأ في التصدير:', error);
            }
        }

        // استيراد التعيينات (محسن)
        function importAssignments(fileInput) {
            const file = fileInput.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const importedData = JSON.parse(e.target.result);

                    let importedAssignments = [];

                    // التحقق من نوع البيانات
                    if (Array.isArray(importedData)) {
                        // ملف تعيينات مباشر
                        importedAssignments = importedData;
                    } else if (importedData.assignments && Array.isArray(importedData.assignments)) {
                        // ملف يحتوي على تعيينات ضمن كائن
                        importedAssignments = importedData.assignments;
                    } else if (typeof importedData === 'object' && importedData.assignments) {
                        // محاولة استخراج التعيينات من أي مكان
                        if (Array.isArray(importedData.assignments)) {
                            importedAssignments = importedData.assignments;
                        } else if (typeof importedData.assignments === 'object') {
                            importedAssignments = Object.values(importedData.assignments);
                        }
                    } else {
                        throw new Error('تنسيق الملف غير مدعوم');
                    }

                    // تنظيف وتصحيح التعيينات
                    const validAssignments = importedAssignments.filter(assignment => {
                        if (!assignment || typeof assignment !== 'object') return false;
                        if (!assignment.giftName || typeof assignment.giftName !== 'string') return false;

                        // إضافة الحقول المفقودة
                        if (!assignment.id) assignment.id = 'imported_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                        if (!assignment.eventType) assignment.eventType = 'gift';
                        if (!assignment.creatureType) assignment.creatureType = 'random';
                        if (!assignment.minCount) assignment.minCount = 1;
                        if (!assignment.maxCount) assignment.maxCount = 999;
                        if (!assignment.sizeMultiplier) assignment.sizeMultiplier = 1;
                        if (!assignment.specialEffect) assignment.specialEffect = 'none';
                        if (!assignment.soundEffect) assignment.soundEffect = 'spawn';

                        return true;
                    });

                    if (validAssignments.length === 0) {
                        throw new Error('لا توجد تعيينات صحيحة في الملف');
                    }

                    // دمج مع التعيينات الحالية
                    const existingIds = assignments.map(a => a.id);
                    const newAssignments = validAssignments.filter(a => !existingIds.includes(a.id));

                    assignments = [...assignments, ...newAssignments];
                    renderAssignments();
                    saveAssignments();

                    showStatus(`📥 تم استيراد ${newAssignments.length} تعيين جديد من أصل ${importedAssignments.length}`, 'success');

                } catch (error) {
                    showStatus('❌ فشل في استيراد التعيينات: ' + error.message, 'error');
                    console.error('خطأ في الاستيراد:', error);
                    console.error('محتوى الملف:', e.target.result);
                }

                fileInput.value = '';
            };

            reader.readAsText(file);
        }

        // استيراد البيانات الشاملة من الصفحة القديمة
        function importCompleteData(fileInput) {
            const file = fileInput.files[0];
            if (!file) return;

            showStatus('📥 جاري استيراد البيانات الشاملة...', 'info');

            const reader = new FileReader();
            reader.onload = async function(e) {
                try {
                    const importedData = JSON.parse(e.target.result);

                    // طباعة معلومات الملف للتشخيص
                    console.log('📥 معلومات الملف المستورد:');
                    console.log('- الإصدار:', importedData.version);
                    console.log('- المصدر:', importedData.source);
                    console.log('- التاريخ:', importedData.timestamp);
                    console.log('- التعيينات:', importedData.assignments);
                    console.log('- نوع التعيينات:', Array.isArray(importedData.assignments) ? 'مصفوفة' : typeof importedData.assignments);
                    console.log('- عدد التعيينات:', importedData.assignments ? (Array.isArray(importedData.assignments) ? importedData.assignments.length : Object.keys(importedData.assignments).length) : 0);

                    // التحقق من صحة الملف
                    if (!importedData.version || !importedData.source) {
                        throw new Error('ملف غير صحيح أو تالف');
                    }

                    let importResults = [];

                    // 1. استيراد الإعدادات
                    if (importedData.settings) {
                        try {
                            applySettings(importedData.settings);
                            localStorage.setItem('fishGameSettings', JSON.stringify(importedData.settings));
                            importResults.push(`✅ الإعدادات: ${Object.keys(importedData.settings).length} إعداد`);
                        } catch (error) {
                            importResults.push(`❌ فشل في استيراد الإعدادات: ${error.message}`);
                        }
                    }

                    // 2. استيراد التعيينات
                    if (importedData.assignments) {
                        try {
                            let importedAssignments = [];

                            // التحقق من نوع البيانات
                            if (Array.isArray(importedData.assignments)) {
                                importedAssignments = importedData.assignments;
                            } else if (typeof importedData.assignments === 'object') {
                                // إذا كانت البيانات كـ object، حولها لـ array
                                importedAssignments = Object.values(importedData.assignments);
                            } else {
                                throw new Error('تنسيق التعيينات غير مدعوم');
                            }

                            // تنظيف وتصحيح التعيينات
                            const validAssignments = importedAssignments.filter(assignment => {
                                // التحقق من الحقول الأساسية
                                if (!assignment || typeof assignment !== 'object') return false;
                                if (!assignment.giftName || typeof assignment.giftName !== 'string') return false;

                                // إضافة الحقول المفقودة بقيم افتراضية
                                if (!assignment.id) assignment.id = 'imported_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                                if (!assignment.eventType) assignment.eventType = 'gift';
                                if (!assignment.creatureType) assignment.creatureType = 'random';
                                if (!assignment.minCount) assignment.minCount = 1;
                                if (!assignment.maxCount) assignment.maxCount = 999;
                                if (!assignment.sizeMultiplier) assignment.sizeMultiplier = 1;
                                if (!assignment.specialEffect) assignment.specialEffect = 'none';
                                if (!assignment.soundEffect) assignment.soundEffect = 'spawn';

                                return true;
                            });

                            if (validAssignments.length === 0) {
                                importResults.push(`⚠️ التعيينات: لا توجد تعيينات صحيحة للاستيراد`);
                            } else {
                                // دمج مع التعيينات الحالية
                                const existingIds = assignments.map(a => a.id);
                                const newAssignments = validAssignments.filter(a => !existingIds.includes(a.id));

                                assignments = [...assignments, ...newAssignments];
                                renderAssignments();
                                saveAssignments();
                                importResults.push(`✅ التعيينات: ${newAssignments.length} تعيين جديد من أصل ${importedAssignments.length}`);
                            }
                        } catch (error) {
                            console.error('خطأ في استيراد التعيينات:', error);
                            importResults.push(`❌ فشل في استيراد التعيينات: ${error.message}`);
                        }
                    } else {
                        importResults.push(`ℹ️ التعيينات: لا توجد تعيينات في الملف`);
                    }

                    // 3. استيراد الأصوات المخصصة
                    if (importedData.customSounds && typeof importedData.customSounds === 'object') {
                        try {
                            let importedSoundsCount = 0;

                            for (const [soundName, base64Data] of Object.entries(importedData.customSounds)) {
                                try {
                                    // حفظ في localStorage
                                    localStorage.setItem(`fishGameCustomSound_${soundName}`, base64Data);

                                    // تحميل في الذاكرة إذا كان audioContext متاح
                                    if (audioContext) {
                                        await loadCustomSoundFromStorage(soundName);
                                    }

                                    // تحديث واجهة المستخدم
                                    updateUploadUI(soundName, true);
                                    importedSoundsCount++;

                                } catch (soundError) {
                                    console.warn(`فشل في استيراد الصوت ${soundName}:`, soundError);
                                }
                            }

                            importResults.push(`✅ الأصوات المخصصة: ${importedSoundsCount} صوت`);
                        } catch (error) {
                            importResults.push(`❌ فشل في استيراد الأصوات: ${error.message}`);
                        }
                    }

                    // 4. استيراد معلومات الأصوات المخصصة
                    if (importedData.customSoundsInfo) {
                        try {
                            localStorage.setItem('fishGameCustomSounds', JSON.stringify(importedData.customSoundsInfo));
                            importResults.push(`✅ معلومات الأصوات المخصصة`);
                        } catch (error) {
                            importResults.push(`❌ فشل في استيراد معلومات الأصوات: ${error.message}`);
                        }
                    }

                    // عرض النتائج
                    const resultText = importResults.join('\n');
                    console.log('📥 نتائج الاستيراد الشامل:\n' + resultText);

                    const hasErrors = importResults.some(result => result.includes('❌'));

                    if (hasErrors) {
                        showStatus('⚠️ تم الاستيراد مع بعض الأخطاء', 'warning');
                    } else {
                        showStatus('✅ تم الاستيراد الشامل بنجاح!', 'success');
                    }

                    setTimeout(() => {
                        alert(`📥 نتائج الاستيراد الشامل:\n\n${resultText}\n\nتم نقل جميع بياناتك من الصفحة القديمة!`);
                    }, 500);

                } catch (error) {
                    showStatus('❌ فشل في استيراد البيانات: ' + error.message, 'error');
                    console.error('خطأ في الاستيراد الشامل:', error);
                }

                fileInput.value = '';
            };

            reader.readAsText(file);
        }

        // تشخيص البيانات الحالية
        function debugCurrentData() {
            console.log('🔍 تشخيص البيانات الحالية:');
            console.log('- التعيينات الحالية:', assignments);
            console.log('- نوع التعيينات:', Array.isArray(assignments) ? 'مصفوفة' : typeof assignments);
            console.log('- عدد التعيينات:', assignments ? assignments.length : 0);
            console.log('- localStorage assignments:', localStorage.getItem('fishGameAssignments'));
            console.log('- localStorage settings:', localStorage.getItem('fishGameSettings'));

            const debugInfo = [
                `التعيينات الحالية: ${assignments ? assignments.length : 0}`,
                `نوع التعيينات: ${Array.isArray(assignments) ? 'مصفوفة' : typeof assignments}`,
                `localStorage assignments: ${localStorage.getItem('fishGameAssignments') ? 'موجود' : 'غير موجود'}`,
                `localStorage settings: ${localStorage.getItem('fishGameSettings') ? 'موجود' : 'غير موجود'}`
            ].join('\n');

            alert(`🔍 تشخيص البيانات الحالية:\n\n${debugInfo}\n\nتحقق من وحدة التحكم للمزيد من التفاصيل.`);
        }

        // إنشاء عنصر تعيين
        function createAssignmentElement(assignment, index) {
            const element = document.createElement('div');
            element.className = 'assignment-item';
            element.innerHTML = `
                <div class="assignment-header">
                    <span style="font-size: 10px; color: #bdc3c7;">#${assignment.id || index}</span>
                    <button type="button" class="btn btn-danger" style="padding: 2px 6px; font-size: 9px;" onclick="removeAssignment(this)">🗑️</button>
                </div>
                <div class="assignment-fields">
                    <div class="field-group">
                        <label>نوع الحدث:</label>
                        <select class="event-type">
                            <option value="gift">🎁 هدية</option>
                            <option value="like">👍 إعجاب</option>
                            <option value="comment">💬 تعليق</option>
                            <option value="follow">➕ متابعة</option>
                            <option value="share">📤 مشاركة</option>
                            <option value="join">👋 انضمام</option>
                        </select>
                    </div>
                    <div class="field-group">
                        <label>اسم الهدية/الكلمة:</label>
                        <input type="text" class="gift-name-input" placeholder="Rose, Diamond, fish...">
                    </div>
                    <div class="field-group">
                        <label>نوع المخلوق:</label>
                        <select class="creature-type">
                            <option value="random">🎲 عشوائي</option>
                            <option value="normal">🐟 سمكة عادية</option>
                            <option value="shark">🦈 قرش</option>
                            <option value="whale">🐋 حوت</option>
                            <option value="dolphin">🐬 دولفين</option>
                            <option value="eel">🐍 أنقليس</option>
                            <option value="ray">🗲 راي</option>
                            <option value="salmon">🐟 سلمون</option>
                            <option value="jellyfish">🎐 قنديل البحر</option>
                            <option value="octopus">🐙 أخطبوط</option>
                            <option value="seahorse">🐴 حصان البحر</option>
                            <option value="turtle">🐢 سلحفاة</option>
                            <option value="crab">🦀 سرطان</option>
                            <option value="lobster">🦞 كركند</option>
                            <option value="starfish">⭐ نجمة البحر</option>
                            <option value="anglerfish">🎣 سمكة الصياد</option>
                            <option value="swordfish">⚔️ سمكة السيف</option>
                            <option value="hammerhead">🔨 القرش المطرقة</option>
                        </select>
                    </div>
                    <div class="field-group">
                        <label>العدد الأدنى:</label>
                        <input type="number" class="min-count" min="1" value="1">
                    </div>
                    <div class="field-group">
                        <label>العدد الأقصى:</label>
                        <input type="number" class="max-count" min="1" value="999">
                    </div>
                    <div class="field-group">
                        <label>مضاعف الحجم:</label>
                        <input type="number" class="size-multiplier" min="0.1" max="5" step="0.1" value="1">
                    </div>
                    <div class="field-group">
                        <label>التأثير الخاص:</label>
                        <select class="special-effect">
                            <option value="none">بدون تأثير</option>
                            <option value="speed_boost">تسريع</option>
                            <option value="size_boost">زيادة حجم</option>
                            <option value="wild_mode">وضع شراسة</option>
                            <option value="electric">كهربائي</option>
                            <option value="poison">سام</option>
                            <option value="regeneration">تجديد</option>
                            <option value="shell_defense">دفاع الصدفة</option>
                            <option value="welcome_glow">توهج ترحيبي</option>
                            <option value="loyalty_boost">تعزيز الولاء</option>
                            <option value="battle_mode">وضع المعركة</option>
                        </select>
                    </div>
                    <div class="field-group">
                        <label>تأثير صوتي:</label>
                        <select class="sound-effect">
                            <option value="spawn">ظهور</option>
                            <option value="shark_roar">زئير قرش</option>
                            <option value="whale_call">نداء حوت</option>
                            <option value="eat_small">أكل صغير</option>
                            <option value="eat_large">أكل كبير</option>
                        </select>
                    </div>
                </div>
            `;

            // تعبئة القيم
            element.querySelector('.event-type').value = assignment.eventType || 'gift';
            element.querySelector('.gift-name-input').value = assignment.giftName || '';
            element.querySelector('.creature-type').value = assignment.creatureType || 'random';
            element.querySelector('.min-count').value = assignment.minCount || 1;
            element.querySelector('.max-count').value = assignment.maxCount || 999;
            element.querySelector('.size-multiplier').value = assignment.sizeMultiplier || 1;
            element.querySelector('.special-effect').value = assignment.specialEffect || 'none';
            element.querySelector('.sound-effect').value = assignment.soundEffect || 'spawn';

            // إضافة مستمعي الأحداث
            element.querySelector('.event-type').addEventListener('change', function() {
                assignments[index].eventType = this.value;
                saveAssignments();
            });

            element.querySelector('.gift-name-input').addEventListener('input', function() {
                assignments[index].giftName = this.value;
                saveAssignments();
            });

            element.querySelector('.creature-type').addEventListener('change', function() {
                assignments[index].creatureType = this.value;
                saveAssignments();
            });

            element.querySelector('.min-count').addEventListener('input', function() {
                assignments[index].minCount = parseInt(this.value) || 1;
                saveAssignments();
            });

            element.querySelector('.max-count').addEventListener('input', function() {
                assignments[index].maxCount = parseInt(this.value) || 999;
                saveAssignments();
            });

            element.querySelector('.size-multiplier').addEventListener('input', function() {
                assignments[index].sizeMultiplier = parseFloat(this.value) || 1;
                saveAssignments();
            });

            element.querySelector('.special-effect').addEventListener('change', function() {
                assignments[index].specialEffect = this.value;
                saveAssignments();
            });

            element.querySelector('.sound-effect').addEventListener('change', function() {
                assignments[index].soundEffect = this.value;
                saveAssignments();
            });

            return element;
        }

        // حفظ التعيينات
        function saveAssignments() {
            try {
                localStorage.setItem('fishGameAssignments', JSON.stringify(assignments));
                console.log('💾 تم حفظ التعيينات:', assignments.length);
            } catch (error) {
                console.error('❌ فشل في حفظ التعيينات:', error);
                showStatus('❌ فشل في حفظ التعيينات', 'error');
            }
        }

        // تحميل التعيينات
        function loadAssignments() {
            try {
                const savedData = localStorage.getItem('fishGameAssignments');
                if (savedData) {
                    const loadedAssignments = JSON.parse(savedData);
                    console.log('📂 تم تحميل التعيينات:', loadedAssignments.length);
                    return loadedAssignments;
                }
            } catch (error) {
                console.error('❌ فشل في تحميل التعيينات:', error);
            }
            return null;
        }

        // الحصول على تعيين للهدية
        function getAssignmentForGift(giftName, giftCount) {
            const assignment = assignments.find(a =>
                a.giftName.toLowerCase() === giftName.toLowerCase() &&
                giftCount >= a.minCount
            );

            if (assignment) {
                return {
                    creatureType: assignment.creatureType,
                    sizeMultiplier: assignment.sizeMultiplier,
                    specialEffect: assignment.specialEffect
                };
            }

            return {
                creatureType: 'random',
                sizeMultiplier: 1,
                specialEffect: 'none'
            };
        }

        // تصدير الوظيفة للاستخدام العام
        window.getAssignmentForGift = getAssignmentForGift;

        // اختبار شامل للنظام
        function runSystemTest() {
            showStatus('🔧 بدء اختبار النظام...', 'info');

            const testResults = [];

            // اختبار 1: التعيينات
            try {
                const testAssignments = assignments.length;
                testResults.push(`✅ التعيينات: ${testAssignments} تعيين محمل`);

                if (testAssignments === 0) {
                    testResults.push('⚠️ تحذير: لا توجد تعيينات محملة');
                }
            } catch (error) {
                testResults.push('❌ خطأ في التعيينات: ' + error.message);
            }

            // اختبار 2: الأصوات
            try {
                const defaultSounds = soundBuffers.size;
                const customSoundsCount = customSounds.size;
                testResults.push(`✅ الأصوات الافتراضية: ${defaultSounds}`);
                testResults.push(`✅ الأصوات المخصصة: ${customSoundsCount}`);

                if (!audioContext) {
                    testResults.push('⚠️ تحذير: نظام الصوت غير مفعل');
                }
            } catch (error) {
                testResults.push('❌ خطأ في نظام الصوت: ' + error.message);
            }

            // اختبار 3: التخزين المحلي
            try {
                const settingsSize1 = localStorage.getItem('fishGameSettings')?.length || 0;
                const settingsSize2 = localStorage.getItem('fishEatFishSettings')?.length || 0;
                const assignmentsSize = localStorage.getItem('fishGameAssignments')?.length || 0;

                testResults.push(`✅ حجم الإعدادات (fishGameSettings): ${(settingsSize1/1024).toFixed(1)} KB`);
                testResults.push(`✅ حجم الإعدادات (fishEatFishSettings): ${(settingsSize2/1024).toFixed(1)} KB`);
                testResults.push(`✅ حجم التعيينات: ${(assignmentsSize/1024).toFixed(1)} KB`);

                // التحقق من تطابق الإعدادات
                if (settingsSize1 > 0 && settingsSize2 > 0) {
                    const settings1 = localStorage.getItem('fishGameSettings');
                    const settings2 = localStorage.getItem('fishEatFishSettings');
                    if (settings1 === settings2) {
                        testResults.push('✅ الإعدادات متطابقة في كلا المفتاحين - جاهزة لـ TikTok Live');
                    } else {
                        testResults.push('⚠️ الإعدادات غير متطابقة - قد تحتاج إعادة حفظ');
                    }
                } else {
                    testResults.push('❌ الإعدادات غير محفوظة في كلا المفتاحين');
                }

                // حساب حجم الأصوات المخصصة
                let customSoundsSize = 0;
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (key && key.startsWith('fishGameCustomSound_')) {
                        customSoundsSize += localStorage.getItem(key).length;
                    }
                }
                testResults.push(`✅ حجم الأصوات المخصصة: ${(customSoundsSize/1024/1024).toFixed(1)} MB`);

            } catch (error) {
                testResults.push('❌ خطأ في التخزين المحلي: ' + error.message);
            }

            // اختبار 4: الوظائف الأساسية
            try {
                const settings = getAllSettings();
                testResults.push(`✅ جمع الإعدادات: ${Object.keys(settings).length} إعداد`);

                // اختبار وظيفة البحث عن التعيينات
                const testAssignment = getAssignmentForGift('Rose', 1);
                testResults.push(`✅ البحث في التعيينات: ${testAssignment.creatureType}`);

            } catch (error) {
                testResults.push('❌ خطأ في الوظائف الأساسية: ' + error.message);
            }

            // اختبار 5: التوافق مع اللعبة
            try {
                if (window.customSoundBuffers) {
                    testResults.push('✅ التوافق مع اللعبة: الأصوات المخصصة متاحة');
                } else {
                    testResults.push('⚠️ التوافق مع اللعبة: الأصوات المخصصة غير متاحة');
                }

                if (typeof window.getAssignmentForGift === 'function') {
                    testResults.push('✅ التوافق مع اللعبة: وظيفة التعيينات متاحة');
                } else {
                    testResults.push('❌ التوافق مع اللعبة: وظيفة التعيينات غير متاحة');
                }

            } catch (error) {
                testResults.push('❌ خطأ في التوافق مع اللعبة: ' + error.message);
            }

            // عرض النتائج
            const resultText = testResults.join('\n');
            console.log('🔧 نتائج اختبار النظام:\n' + resultText);

            // عرض النتائج في نافذة منبثقة
            alert('🔧 نتائج اختبار النظام:\n\n' + resultText);

            const hasErrors = testResults.some(result => result.includes('❌'));
            const hasWarnings = testResults.some(result => result.includes('⚠️'));

            if (hasErrors) {
                showStatus('❌ تم العثور على أخطاء في النظام', 'error');
            } else if (hasWarnings) {
                showStatus('⚠️ تم العثور على تحذيرات في النظام', 'warning');
            } else {
                showStatus('✅ النظام يعمل بشكل مثالي!', 'success');
            }
        }

        // === وظائف الأصوات ===

        // توليد الأصوات للمعاينة
        async function generateSounds() {
            const sounds = {
                eat_small: generateRealisticEatingSound('small'),
                eat_large: generateRealisticEatingSound('large'),
                shark_roar: generateScarySound('shark'),
                whale_call: generateScarySound('whale'),
                spawn: generateSpawnSound(),
                ocean_waves: generateAmbientSound('waves'),
                underwater_bubbles: generateAmbientSound('bubbles'),
                deep_sea: generateAmbientSound('deep'),
                whale_song: generateAmbientSound('whale_song')
            };

            for (const [name, buffer] of Object.entries(sounds)) {
                soundBuffers.set(name, buffer);
            }
        }

        // توليد أصوات أكل واقعية
        function generateRealisticEatingSound(size) {
            const sampleRate = audioContext.sampleRate;
            const duration = size === 'large' ? 1.2 : 0.6;
            const buffer = audioContext.createBuffer(1, sampleRate * duration, sampleRate);
            const data = buffer.getChannelData(0);

            for (let i = 0; i < buffer.length; i++) {
                const t = i / sampleRate;
                let sound = 0;

                if (size === 'large') {
                    sound += Math.sin(2 * Math.PI * 60 * t) * Math.exp(-t * 1.5) * 0.4;
                    sound += Math.sin(2 * Math.PI * 120 * t) * Math.exp(-t * 2) * 0.3;
                    if (t > 0.3 && t < 0.8) {
                        sound += (Math.random() - 0.5) * 0.2 * Math.exp(-(t - 0.5) * 8);
                    }
                } else {
                    sound += Math.sin(2 * Math.PI * 200 * t) * Math.exp(-t * 4) * 0.3;
                    sound += (Math.random() - 0.5) * 0.1 * Math.exp(-t * 3);
                }

                data[i] = sound * 0.5;
            }

            return buffer;
        }

        // توليد أصوات مرعبة
        function generateScarySound(fishType) {
            const sampleRate = audioContext.sampleRate;
            const duration = 2.0;
            const buffer = audioContext.createBuffer(1, sampleRate * duration, sampleRate);
            const data = buffer.getChannelData(0);

            for (let i = 0; i < buffer.length; i++) {
                const t = i / sampleRate;
                let sound = 0;

                switch (fishType) {
                    case 'shark':
                        sound += Math.sin(2 * Math.PI * 40 * t) * Math.exp(-t * 0.6) * 0.6;
                        sound += Math.sin(2 * Math.PI * 80 * t) * Math.exp(-t * 1) * 0.4;
                        sound += Math.sin(2 * Math.PI * 25 * t) * Math.exp(-t * 0.8) * 0.3;
                        break;

                    case 'whale':
                        const freq = 20 + 10 * Math.sin(t * 3);
                        sound += Math.sin(2 * Math.PI * freq * t) * Math.exp(-t * 0.4) * 0.7;
                        sound += Math.sin(2 * Math.PI * freq * 2 * t) * Math.exp(-t * 0.6) * 0.3;
                        break;
                }

                data[i] = sound * 0.6;
            }

            return buffer;
        }

        // توليد أصوات المحيط
        function generateAmbientSound(type) {
            const sampleRate = audioContext.sampleRate;
            const duration = 3;
            const buffer = audioContext.createBuffer(1, sampleRate * duration, sampleRate);
            const data = buffer.getChannelData(0);

            for (let i = 0; i < buffer.length; i++) {
                const t = i / sampleRate;
                let sound = 0;

                switch (type) {
                    case 'waves':
                        const waveFreq = 0.3 + Math.sin(t * 0.1) * 0.1;
                        const waveNoise = (Math.random() - 0.5) * 0.4;
                        const wavePattern = Math.sin(2 * Math.PI * waveFreq * t) * 0.3;
                        sound = (waveNoise + wavePattern) * Math.sin(t * 0.5) * 0.6;
                        break;

                    case 'bubbles':
                        if (Math.random() < 0.05) {
                            const bubbleFreq = 800 + Math.random() * 1200;
                            const bubbleDecay = Math.exp(-t * 8);
                            sound += Math.sin(2 * Math.PI * bubbleFreq * t) * bubbleDecay * 0.2;
                        }
                        sound += (Math.random() - 0.5) * 0.1;
                        sound += Math.sin(2 * Math.PI * 60 * t) * 0.05;
                        break;

                    case 'deep':
                        const deepFreq = 20 + Math.sin(t * 0.2) * 10;
                        sound += Math.sin(2 * Math.PI * deepFreq * t) * 0.4;
                        if (Math.random() < 0.01) {
                            const mysteryFreq = 100 + Math.random() * 200;
                            sound += Math.sin(2 * Math.PI * mysteryFreq * t) * Math.exp(-t * 2) * 0.3;
                        }
                        sound += (Math.random() - 0.5) * 0.15;
                        break;

                    case 'whale_song':
                        const whaleFreq = 200 + Math.sin(t * 0.5) * 100;
                        sound += Math.sin(2 * Math.PI * whaleFreq * t) * Math.exp(-Math.abs(t - 1.5) * 0.5) * 0.4;
                        sound += Math.sin(2 * Math.PI * (whaleFreq * 1.5) * t) * Math.exp(-Math.abs(t - 2) * 0.3) * 0.2;
                        break;
                }

                data[i] = sound * 0.3;
            }

            return buffer;
        }

        // توليد صوت الظهور
        function generateSpawnSound() {
            const sampleRate = audioContext.sampleRate;
            const duration = 0.8;
            const buffer = audioContext.createBuffer(1, sampleRate * duration, sampleRate);
            const data = buffer.getChannelData(0);

            for (let i = 0; i < buffer.length; i++) {
                const t = i / sampleRate;
                const freq = 300 + t * 400;
                const sound = Math.sin(2 * Math.PI * freq * t) * Math.exp(-t * 2) * 0.4;
                data[i] = sound;
            }

            return buffer;
        }

        // معاينة الصوت
        function previewSound(soundName) {
            if (!audioContext || !soundBuffers.has(soundName)) return;

            try {
                const source = audioContext.createBufferSource();
                const gainNode = audioContext.createGain();

                source.buffer = soundBuffers.get(soundName);
                gainNode.gain.value = 0.5;

                source.connect(gainNode);
                gainNode.connect(audioContext.destination);

                source.start();

                showStatus('🔊 تم تشغيل المعاينة', 'info');
            } catch (error) {
                showStatus('❌ فشل في تشغيل المعاينة', 'error');
            }
        }

        // رفع ملف صوتي مخصص
        async function uploadCustomSound(soundName, fileInput) {
            const file = fileInput.files[0];
            if (!file) return;

            const allowedTypes = ['audio/mp3', 'audio/wav', 'audio/ogg', 'audio/m4a', 'audio/mpeg'];
            if (!allowedTypes.includes(file.type)) {
                showStatus('نوع الملف غير مدعوم. استخدم MP3, WAV, OGG, أو M4A', 'error');
                fileInput.value = '';
                return;
            }

            const maxSize = 10 * 1024 * 1024;
            if (file.size > maxSize) {
                showStatus('حجم الملف كبير جداً. الحد الأقصى 10MB', 'error');
                fileInput.value = '';
                return;
            }

            showStatus('جاري رفع الملف...', 'info');

            try {
                const fileReader = new FileReader();
                const arrayBuffer = await new Promise((resolve, reject) => {
                    fileReader.onload = () => resolve(fileReader.result);
                    fileReader.onerror = () => reject(fileReader.error);
                    fileReader.readAsArrayBuffer(file);
                });

                const audioBuffer = await audioContext.decodeAudioData(arrayBuffer.slice());

                customSounds.set(soundName, {
                    buffer: audioBuffer,
                    fileName: file.name,
                    size: file.size
                });

                if (!window.customSoundBuffers) {
                    window.customSoundBuffers = new Map();
                }
                window.customSoundBuffers.set(soundName, audioBuffer);
                soundBuffers.set(soundName, audioBuffer);

                // حفظ في localStorage بالطريقة الجديدة (Base64 كامل)
                const base64Data = await saveAudioAsBase64(file, soundName);

                // حفظ أيض<|im_start|> بالطريقة القديمة للتوافق
                const base64Reader = new FileReader();
                const base64Only = await new Promise((resolve, reject) => {
                    base64Reader.onload = () => {
                        const result = base64Reader.result;
                        const base64 = result.split(',')[1];
                        resolve(base64);
                    };
                    base64Reader.onerror = () => reject(new Error('فشل في قراءة الملف'));
                    base64Reader.readAsDataURL(file);
                });

                localStorage.setItem(`fishGameCustomSound_${soundName}`, base64Only);

                updateUploadUI(soundName, true);
                showStatus(`تم رفع ${file.name} بنجاح`, 'success');
                saveCustomSounds();

            } catch (error) {
                console.error('خطأ في رفع الملف:', error);
                showStatus('فشل في معالجة الملف الصوتي', 'error');
                fileInput.value = '';
            }
        }

        // إزالة صوت مخصص
        function removeCustomSound(soundName) {
            if (customSounds.has(soundName)) {
                customSounds.delete(soundName);
                localStorage.removeItem(`fishGameCustomSound_${soundName}`);

                if (window.customSoundBuffers) {
                    window.customSoundBuffers.delete(soundName);
                }

                const defaultSound = generateDefaultSound(soundName);
                if (defaultSound) {
                    soundBuffers.set(soundName, defaultSound);
                }

                updateUploadUI(soundName, false);
                showStatus('تم حذف الصوت المخصص', 'success');
                saveCustomSounds();
            }
        }

        // تحديث واجهة المستخدم للرفع
        function updateUploadUI(soundName, hasCustomFile) {
            const uploadItem = document.querySelector(`#upload_${soundName}`).closest('.sound-upload-item');
            const removeBtn = uploadItem.querySelector('.remove-custom-btn');
            const fileInput = uploadItem.querySelector('input[type="file"]');

            if (hasCustomFile) {
                uploadItem.classList.add('has-file');
                removeBtn.style.display = 'block';
            } else {
                uploadItem.classList.remove('has-file');
                removeBtn.style.display = 'none';
                fileInput.value = '';
            }
        }

        // توليد الصوت الافتراضي
        function generateDefaultSound(soundName) {
            switch (soundName) {
                case 'eat_small': return generateRealisticEatingSound('small');
                case 'eat_large': return generateRealisticEatingSound('large');
                case 'shark_roar': return generateScarySound('shark');
                case 'whale_call': return generateScarySound('whale');
                case 'spawn': return generateSpawnSound();
                case 'ocean_waves': return generateAmbientSound('waves');
                case 'underwater_bubbles': return generateAmbientSound('bubbles');
                case 'deep_sea': return generateAmbientSound('deep');
                case 'whale_song': return generateAmbientSound('whale_song');
                default: return null;
            }
        }

        // حفظ الأصوات المخصصة
        function saveCustomSounds() {
            try {
                let customSoundsData = {};
                for (const [soundName, soundData] of customSounds.entries()) {
                    customSoundsData[soundName] = {
                        fileName: soundData.fileName,
                        size: soundData.size,
                        timestamp: Date.now()
                    };
                }
                localStorage.setItem('fishGameCustomSounds', JSON.stringify(customSoundsData));
            } catch (error) {
                console.error('فشل في حفظ معلومات الأصوات المخصصة:', error);
            }
        }

        // تحميل معلومات الأصوات المخصصة
        function loadCustomSoundsInfo() {
            try {
                const savedData = localStorage.getItem('fishGameCustomSounds');
                if (savedData) {
                    const customSoundsData = JSON.parse(savedData);
                    for (const [soundName, soundInfo] of Object.entries(customSoundsData)) {
                        updateUploadUI(soundName, true);
                        // محاولة تحميل الصوت المخصص
                        loadCustomSoundFromStorage(soundName);
                    }
                }
            } catch (error) {
                console.warn('فشل في تحميل معلومات الأصوات المخصصة:', error);
            }
        }

        // تحميل صوت مخصص من localStorage
        async function loadCustomSoundFromStorage(soundName) {
            try {
                const base64Data = localStorage.getItem(`fishGameCustomSound_${soundName}`);
                if (base64Data && audioContext) {
                    // تحويل base64 إلى ArrayBuffer
                    const binaryString = atob(base64Data);
                    const bytes = new Uint8Array(binaryString.length);
                    for (let i = 0; i < binaryString.length; i++) {
                        bytes[i] = binaryString.charCodeAt(i);
                    }

                    // فك تشفير الصوت
                    const audioBuffer = await audioContext.decodeAudioData(bytes.buffer.slice());

                    // حفظ في الذاكرة
                    customSounds.set(soundName, {
                        buffer: audioBuffer,
                        fileName: 'مخصص',
                        size: bytes.length
                    });

                    if (!window.customSoundBuffers) {
                        window.customSoundBuffers = new Map();
                    }
                    window.customSoundBuffers.set(soundName, audioBuffer);
                    soundBuffers.set(soundName, audioBuffer);

                    console.log(`🔊 تم تحميل الصوت المخصص: ${soundName}`);
                }
            } catch (error) {
                console.warn(`⚠️ فشل في تحميل الصوت المخصص ${soundName}:`, error);
                // إزالة البيانات التالفة
                localStorage.removeItem(`fishGameCustomSound_${soundName}`);
            }
        }

        // عرض حالة الرفع
        function showUploadStatus(soundName, type, message) {
            const uploadItem = document.querySelector(`#upload_${soundName}`).closest('.sound-upload-item');
            const statusEl = uploadItem.querySelector('.upload-status') || document.createElement('div');

            if (!uploadItem.querySelector('.upload-status')) {
                statusEl.className = 'upload-status';
                statusEl.style.cssText = 'font-size: 9px; margin-top: 3px; padding: 2px; border-radius: 2px;';
                uploadItem.appendChild(statusEl);
            }

            statusEl.textContent = message;

            switch (type) {
                case 'success':
                    statusEl.style.background = 'rgba(39, 174, 96, 0.2)';
                    statusEl.style.color = '#27ae60';
                    break;
                case 'error':
                    statusEl.style.background = 'rgba(231, 76, 60, 0.2)';
                    statusEl.style.color = '#e74c3c';
                    break;
                case 'loading':
                    statusEl.style.background = 'rgba(52, 152, 219, 0.2)';
                    statusEl.style.color = '#3498db';
                    break;
                default:
                    statusEl.style.background = 'rgba(149, 165, 166, 0.2)';
                    statusEl.style.color = '#95a5a6';
            }

            if (type === 'success' || type === 'error') {
                setTimeout(() => {
                    if (statusEl.parentNode) {
                        statusEl.remove();
                    }
                }, 3000);
            }
        }


    </script>

    <!-- Firebase and Auth Scripts -->
    <script type="module" src="/js/firebase-config.js"></script>
    <script>
      // تأخير تحميل auth-guard حتى يتم تحميل Firebase
      setTimeout(() => {
        const script = document.createElement('script');
        script.type = 'module';
        script.src = '/js/auth-guard.js';
        document.head.appendChild(script);
      }, 500);
    </script>

    <script>
        // متغيرات حالة الاشتراك
        let currentUser = null;
        let userSubscription = null;
        let hasActiveSubscription = false;
        let authCheckDone = false;
        let hasRedirected = false;

        // التحقق من المصادقة
        window.addEventListener('authStateChanged', (event) => {
          if (authCheckDone || hasRedirected) return;

          const user = event.detail.user;
          console.log('🔐 Fish Settings - Auth state:', user?.email);

          if (!user) {
            console.log('❌ No user, redirecting to auth');
            hasRedirected = true;
            window.location.href = '/auth.html';
          } else if (!user.emailVerified) {
            console.log('❌ Email not verified, redirecting to verification');
            hasRedirected = true;
            window.location.href = '/email-verification.html';
          } else {
            console.log('✅ User authenticated and verified');
            authCheckDone = true;
            currentUser = user;

            // تحميل بيانات الاشتراك
            loadUserSubscription();
          }
        });

        // تحميل بيانات اشتراك المستخدم
        async function loadUserSubscription() {
          try {
            if (window.firebaseHelpers && currentUser) {
              userSubscription = await window.firebaseHelpers.getUserSubscription(currentUser.uid);
              hasActiveSubscription = userSubscription &&
                userSubscription.status === 'active' &&
                new Date() < (userSubscription.endDate?.toDate ? userSubscription.endDate.toDate() : new Date(userSubscription.endDate));

              console.log('User subscription status:', hasActiveSubscription ? 'Active' : 'None');

              if (!hasActiveSubscription) {
                showSubscriptionRequired();
              } else {
                hideSubscriptionMessage();
                enableSettingsPage();
              }
            }
          } catch (error) {
            console.error('Error loading user subscription:', error);
            hasActiveSubscription = false;
            showSubscriptionRequired();
          }
        }

        // عرض رسالة الاشتراك المطلوب
        function showSubscriptionRequired() {
          // إخفاء محتوى الصفحة
          const container = document.querySelector('.container');
          if (container) {
            container.style.display = 'none';
          }

          // إنشاء رسالة الاشتراك
          const subscriptionMessage = document.createElement('div');
          subscriptionMessage.id = 'subscription-message';
          subscriptionMessage.innerHTML = `
            <div style="
              position: fixed;
              top: 0;
              left: 0;
              width: 100%;
              height: 100%;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              display: flex;
              align-items: center;
              justify-content: center;
              z-index: 10000;
              color: white;
              text-align: center;
              font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            ">
              <div style="
                max-width: 600px;
                padding: 40px;
                background: rgba(255,255,255,0.1);
                border-radius: 20px;
                backdrop-filter: blur(10px);
                box-shadow: 0 20px 40px rgba(0,0,0,0.2);
              ">
                <div style="font-size: 4rem; margin-bottom: 20px;">🔒</div>
                <h1 style="font-size: 2.5rem; margin-bottom: 20px;">إعدادات Fish Eat Fish مقفلة</h1>
                <p style="font-size: 1.3rem; margin-bottom: 30px; opacity: 0.9;">
                  إعدادات اللعبة متاحة فقط للمشتركين في الباقة الشاملة
                </p>

                <div style="
                  background: rgba(255,255,255,0.1);
                  border-radius: 15px;
                  padding: 25px;
                  margin: 30px 0;
                ">
                  <h3 style="font-size: 1.5rem; margin-bottom: 20px;">⚙️ ما ستحصل عليه:</h3>
                  <div style="text-align: right; margin: 20px 0;">
                    <div style="margin: 10px 0;">🎁 تخصيص تعيينات الأحداث</div>
                    <div style="margin: 10px 0;">🔊 إعدادات صوتية متقدمة</div>
                    <div style="margin: 10px 0;">🎮 تحكم كامل في اللعبة</div>
                    <div style="margin: 10px 0;">🎨 إعدادات مرئية احترافية</div>
                    <div style="margin: 10px 0;">🔧 خيارات متقدمة</div>
                    <div style="margin: 10px 0;">🎵 رفع أصوات مخصصة</div>
                  </div>
                </div>

                <div style="margin: 30px 0;">
                  <div style="
                    display: inline-block;
                    background: rgba(255,255,255,0.2);
                    padding: 15px 25px;
                    border-radius: 50px;
                    margin-bottom: 10px;
                  ">
                    <span style="font-size: 2rem; font-weight: bold; color: #00ff7f;">$10</span>
                    <span style="font-size: 1rem; opacity: 0.8;"> / شهرياً</span>
                  </div>
                  <p style="font-size: 1.1rem; opacity: 0.9;">باقة واحدة شاملة - جميع الميزات</p>
                </div>

                <div style="display: flex; gap: 15px; justify-content: center; flex-wrap: wrap;">
                  <button onclick="window.location.href='/subscriptions.html'" style="
                    background: linear-gradient(45deg, #00ff7f, #00cc66);
                    color: white;
                    border: none;
                    padding: 15px 30px;
                    border-radius: 50px;
                    font-size: 1.1rem;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    box-shadow: 0 10px 20px rgba(0,255,127,0.3);
                  " onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 15px 30px rgba(0,255,127,0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 20px rgba(0,255,127,0.3)'">
                    ⚡ اشترك الآن
                  </button>
                  <button onclick="window.location.href='/games.html'" style="
                    background: rgba(255,255,255,0.2);
                    color: white;
                    border: 2px solid rgba(255,255,255,0.3);
                    padding: 15px 30px;
                    border-radius: 50px;
                    font-size: 1.1rem;
                    font-weight: bold;
                    cursor: pointer;
                    transition: all 0.3s ease;
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    backdrop-filter: blur(10px);
                  " onmouseover="this.style.background='rgba(255,255,255,0.3)'; this.style.transform='translateY(-3px)'" onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(0)'">
                    🔙 العودة للألعاب
                  </button>
                </div>
              </div>
            </div>
          `;

          document.body.appendChild(subscriptionMessage);
        }

        // إخفاء رسالة الاشتراك
        function hideSubscriptionMessage() {
          const subscriptionMessage = document.getElementById('subscription-message');
          if (subscriptionMessage) {
            subscriptionMessage.remove();
          }
        }

        // تفعيل صفحة الإعدادات للمشتركين
        function enableSettingsPage() {
          const container = document.querySelector('.container');
          if (container) {
            container.style.display = 'block';
          }

          console.log('⚙️ Fish Settings enabled for subscriber');
        }

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', () => {
          console.log('⚙️ Fish Settings page loaded');

          // إخفاء المحتوى مؤقتاً حتى يتم التحقق من الاشتراك
          const container = document.querySelector('.container');
          if (container) {
            container.style.display = 'none';
          }
        });
    </script>
</body>
</html>
