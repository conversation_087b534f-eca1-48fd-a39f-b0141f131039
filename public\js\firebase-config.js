// Firebase Configuration
// تكوين Firebase للمشروع

// Import Firebase modules - استخدام أحدث إصدار
import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-app.js';
import { getAuth, onAuthStateChanged, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut, updateProfile, sendEmailVerification, connectAuthEmulator } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-auth.js';
import { getFirestore, doc, setDoc, getDoc, updateDoc, collection, addDoc, query, where, getDocs, orderBy, limit, connectFirestoreEmulator } from 'https://www.gstatic.com/firebasejs/10.8.0/firebase-firestore.js';

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyDAG1ZwOOND0gyZSRcbUF1Qoh2SYN9R4Hs",
  authDomain: "streamtok-c6830.firebaseapp.com",
  projectId: "streamtok-c6830",
  storageBucket: "streamtok-c6830.firebasestorage.app",
  messagingSenderId: "123501258730",
  appId: "1:123501258730:web:690222fb0e45b217fbb493"
};

// Initialize Firebase
let app, auth, db;

try {
  app = initializeApp(firebaseConfig);
  console.log('✅ Firebase app initialized successfully');

  // Initialize Firebase services
  auth = getAuth(app);
  db = getFirestore(app);

  console.log('✅ Firebase Auth and Firestore initialized successfully');

} catch (error) {
  console.error('❌ Firebase initialization error:', error);
  alert('خطأ في تهيئة Firebase. يرجى التحقق من الإعدادات.');
}

// Export Firebase services for use in other files
window.firebaseAuth = auth;
window.firebaseDB = db;
window.firebaseApp = app;

// Export Firebase functions
window.firebaseFunctions = {
  // Auth functions
  onAuthStateChanged,
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut,
  updateProfile,
  sendEmailVerification,
  
  // Firestore functions
  doc,
  setDoc,
  getDoc,
  updateDoc,
  collection,
  addDoc,
  query,
  where,
  getDocs,
  orderBy,
  limit
};

// Global auth state management
let currentUser = null;
let authStateChangeTimeout = null;

// Listen for auth state changes
onAuthStateChanged(auth, (user) => {
  // تجنب إطلاق أحداث متعددة بسرعة
  if (authStateChangeTimeout) {
    clearTimeout(authStateChangeTimeout);
  }

  authStateChangeTimeout = setTimeout(() => {
    currentUser = user;

    // إرسال session للخادم عند تسجيل الدخول
    if (user && user.emailVerified) {
      if (window.socket) {
        window.socket.emit('userAuthenticated', {
          userId: user.uid,
          email: user.email,
          emailVerified: user.emailVerified
        });
        console.log('✅ User session sent to server:', user.email);
      } else {
        console.log('⚠️ Socket not available, will retry when connected');
        // حفظ بيانات المستخدم لإرسالها لاحق<|im_start|>
        window.pendingUserAuth = {
          userId: user.uid,
          email: user.email,
          emailVerified: user.emailVerified
        };
      }
    }

    // Dispatch custom event for auth state changes
    window.dispatchEvent(new CustomEvent('authStateChanged', {
      detail: { user }
    }));

    console.log('🔐 Auth state changed:', user ? `User logged in: ${user.email} (Verified: ${user.emailVerified})` : 'User logged out');
  }, 100);
});

// Helper functions
window.firebaseHelpers = {
  // Get current user
  getCurrentUser: () => currentUser,
  
  // Check if user is logged in
  isLoggedIn: () => !!currentUser,
  
  // Get user data from Firestore
  getUserData: async (userId) => {
    try {
      const userDoc = await getDoc(doc(db, 'users', userId));
      return userDoc.exists() ? userDoc.data() : null;
    } catch (error) {
      console.error('Error getting user data:', error);
      return null;
    }
  },
  
  // Create or update user profile in Firestore
  createUserProfile: async (userId, userData) => {
    try {
      await setDoc(doc(db, 'users', userId), {
        ...userData,
        createdAt: new Date(),
        updatedAt: new Date()
      }, { merge: true });
      return true;
    } catch (error) {
      console.error('Error creating user profile:', error);
      return false;
    }
  },
  
  // Get user subscription - نسخة مبسطة بدون فهرس
  getUserSubscription: async (userId) => {
    try {
      // استعلام مبسط بدون orderBy لتجنب الحاجة للفهرس
      const subscriptionQuery = query(
        collection(db, 'subscriptions'),
        where('userId', '==', userId)
      );

      const querySnapshot = await getDocs(subscriptionQuery);

      if (!querySnapshot.empty) {
        // البحث عن الاشتراك النشط يدوياً
        let activeSubscription = null;
        let latestDate = null;

        querySnapshot.docs.forEach(doc => {
          const data = doc.data();
          if (data.status === 'active') {
            const createdAt = data.createdAt?.toDate ? data.createdAt.toDate() : new Date(data.createdAt);
            if (!latestDate || createdAt > latestDate) {
              latestDate = createdAt;
              activeSubscription = { id: doc.id, ...data };
            }
          }
        });

        return activeSubscription;
      }

      return null;
    } catch (error) {
      console.error('Error getting user subscription:', error);
      return null;
    }
  },
  
  // Create subscription
  createSubscription: async (userId, planData) => {
    try {
      const subscriptionData = {
        userId,
        plan: planData.plan,
        status: 'active',
        startDate: new Date(),
        endDate: new Date(Date.now() + (planData.durationDays * 24 * 60 * 60 * 1000)),
        features: planData.features,
        price: planData.price,
        createdAt: new Date(),
        updatedAt: new Date(),

        // معلومات الدفع (إذا توفرت)
        ...(planData.paymentId && { paymentId: planData.paymentId }),
        ...(planData.paymentMethod && { paymentMethod: planData.paymentMethod }),
        ...(planData.paymentDetails && { paymentDetails: planData.paymentDetails })
      };

      const docRef = await addDoc(collection(db, 'subscriptions'), subscriptionData);
      return { id: docRef.id, ...subscriptionData };
    } catch (error) {
      console.error('Error creating subscription:', error);
      return null;
    }
  },

  // Get user subscription history
  getUserSubscriptionHistory: async (userId) => {
    try {
      // استعلام مبسط لجميع اشتراكات المستخدم
      const subscriptionQuery = query(
        collection(db, 'subscriptions'),
        where('userId', '==', userId)
      );

      const querySnapshot = await getDocs(subscriptionQuery);
      const subscriptions = [];

      querySnapshot.forEach((doc) => {
        subscriptions.push({ id: doc.id, ...doc.data() });
      });

      // ترتيب الاشتراكات حسب تاريخ الإنشاء (الأحدث أولاً)
      subscriptions.sort((a, b) => {
        const dateA = a.createdAt?.toDate ? a.createdAt.toDate() : new Date(a.createdAt);
        const dateB = b.createdAt?.toDate ? b.createdAt.toDate() : new Date(b.createdAt);
        return dateB - dateA;
      });

      return subscriptions;
    } catch (error) {
      console.error('Error getting user subscription history:', error);
      return [];
    }
  },

  // Check if user has access to feature
  hasFeatureAccess: async (userId, feature) => {
    try {
      const subscription = await window.firebaseHelpers.getUserSubscription(userId);
      
      if (!subscription) {
        return false; // No active subscription
      }
      
      // Check if subscription is still valid
      if (new Date() > subscription.endDate.toDate()) {
        return false; // Subscription expired
      }
      
      // Check if feature is included in subscription
      return subscription.features && subscription.features.includes(feature);
    } catch (error) {
      console.error('Error checking feature access:', error);
      return false;
    }
  }
};

console.log('Firebase initialized successfully');
